#!/bin/bash
# Health check script for Smartflo AI Caller

# Set default values
HOST=${HOST:-0.0.0.0}
PORT=${PORT:-8000}
HEALTH_CHECK_PATH=${HEALTH_CHECK_PATH:-/health}

# Make HTTP request to health endpoint
response=$(curl -s -o /dev/null -w "%{http_code}" "http://${HOST}:${PORT}${HEALTH_CHECK_PATH}" 2>/dev/null)

# Check if request was successful
if [ "$response" = "200" ]; then
    echo "Health check passed"
    exit 0
else
    echo "Health check failed with status: $response"
    exit 1
fi