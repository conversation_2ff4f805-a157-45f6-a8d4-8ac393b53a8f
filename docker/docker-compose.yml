version: '3.8'

services:
  smartflo-ai-caller:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: smartflo-ai-caller
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      # Server Configuration
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=false
      - ENVIRONMENT=production
      
      # AI Voice Mate Configuration
      - AI_VOICE_MATE_WS_URL=ws://ai-voice-mate:5010
      - AI_VOICE_MATE_CONNECTION_TIMEOUT=30
      - AI_VOICE_MATE_RETRY_ATTEMPTS=5
      - AI_VOICE_MATE_RETRY_DELAY=10
      
      # Webhook Configuration
      - WEBHOOK_PATH=/webhook/smartflo
      - WEBHOOK_SECRET=${WEBHOOK_SECRET}
      
      # Logging Configuration
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - LOG_FILE=/app/logs/smartflo-ai-caller.log
      
      # Session Management
      - SESSION_TIMEOUT=300
      - MAX_CONCURRENT_SESSIONS=100
      
      # Health Check Configuration
      - HEALTH_CHECK_ENABLED=true
      - HEALTH_CHECK_PATH=/health
      
      # Monitoring
      - METRICS_ENABLED=true
      - METRICS_PATH=/metrics
    volumes:
      # Mount logs directory
      - logs:/app/logs
      # Mount data directory for persistence
      - data:/app/data
      # Mount config directory for custom configurations
      - ./config:/app/config:ro
    networks:
      - smartflo-network
    depends_on:
      - ai-voice-mate
    healthcheck:
      test: ["CMD", "/usr/local/bin/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # AI Voice Mate service (external dependency)
  ai-voice-mate:
    image: ai-voice-mate:latest  # Replace with actual image
    container_name: ai-voice-mate
    restart: unless-stopped
    ports:
      - "5010:5010"
    networks:
      - smartflo-network
    # Add ai-voice-mate specific configuration here

  # Optional: Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: smartflo-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl:ro
    networks:
      - smartflo-network
    depends_on:
      - smartflo-ai-caller
    profiles:
      - nginx

  # Optional: Redis for session storage (if needed for scaling)
  redis:
    image: redis:7-alpine
    container_name: smartflo-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - smartflo-network
    command: redis-server --appendonly yes
    profiles:
      - redis

networks:
  smartflo-network:
    driver: bridge
    name: smartflo-network

volumes:
  logs:
    driver: local
  data:
    driver: local
  redis-data:
    driver: local