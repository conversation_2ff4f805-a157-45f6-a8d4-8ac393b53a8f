#!/usr/bin/env python3
"""Debug configuration values."""

from src.smartflo_ai_caller.config import settings

print("=== Configuration Debug ===")
print(f"Health check enabled: {settings.health_check.enabled}")
print(f"Health check path: {settings.health_check.path}")
print(f"Metrics enabled: {settings.metrics.enabled}")
print(f"Metrics path: {settings.metrics.path}")
print(f"Webhook path: {settings.webhook.path}")
print("==========================")