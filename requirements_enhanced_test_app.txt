# Enhanced Voice Call Test Application Requirements

# Core Flask and WebSocket support
Flask==2.3.3
Flask-SocketIO==5.3.6
python-socketio==5.9.0
python-engineio==4.7.1

# WebSocket clients for real integration
websockets==12.0
websocket-client==1.6.4

# HTTP client for health checks
requests==2.31.0

# Additional utilities
python-json-logger==2.0.7
pyyaml>=6.0.1

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0

# Optional: For enhanced audio processing
# numpy>=1.24.0
# scipy>=1.11.0

# Note: audioop is part of Python standard library (no separate installation needed)