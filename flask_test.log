WARNING:werkzeug:Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
🎙️ Starting Enhanced Voice Call Test Application
🔗 Real Integration with smartflo-ai-caller & AI Voice Mate
🌐 Access the application at: http://localhost:5002
📋 System Requirements:
   - smartflo-ai-caller running on localhost:8000
   - AI Voice Mate running on localhost:5010
======================================================================
 * Serving Flask app 'enhanced_voice_call_test_app'
 * Debug mode: on
Address already in use
Port 5002 is in use by another program. Either identify and stop that program, or start the server with a different port.
