#!/usr/bin/env python3
"""
Comprehensive test suite for bi-directional audio streaming implementation.
Tests compliance with the PDF specification for all event types and workflows.
"""

import asyncio
import base64
import json
import struct
import sys
import os
import wave
import io
import time
from typing import Dict, Any, List, Optional
from datetime import datetime

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


class MockVendorServer:
    """Mock vendor WebSocket server for testing bi-directional streaming."""
    
    def __init__(self):
        self.received_messages = []
        self.session_data = {}
        self.sequence_number = 1
    
    def next_sequence(self) -> int:
        """Get next sequence number."""
        current = self.sequence_number
        self.sequence_number += 1
        return current
    
    async def handle_message(self, message: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Handle incoming message and return response(s)."""
        self.received_messages.append(message)
        responses = []
        
        event_type = message.get("event")
        stream_id = message.get("streamSid")
        
        # Handle different event types according to specification
        if event_type == "connected":
            # Connection established, no response needed
            pass
            
        elif event_type == "start":
            # Stream started, acknowledge and begin media exchange
            start_data = message.get("start", {})
            self.session_data[stream_id] = start_data
            
            # Could send initial media response
            responses.append({
                "event": "media",
                "streamSid": stream_id,
                "media": {
                    "payload": self._generate_mock_mulaw_audio(),
                    "chunk": "1"
                }
            })
            
        elif event_type == "media":
            # Received media from client, process and possibly respond
            media_data = message.get("media", {})
            chunk_num = int(media_data.get("chunk", 1))
            
            # Echo back with different audio or send mark
            if chunk_num % 3 == 0:  # Every 3rd chunk, send a mark
                responses.append({
                    "event": "mark",
                    "streamSid": stream_id,
                    "mark": {
                        "name": f"playback_complete_{chunk_num}"
                    }
                })
            else:
                # Send audio response
                responses.append({
                    "event": "media",
                    "streamSid": stream_id,
                    "media": {
                        "payload": self._generate_mock_mulaw_audio(),
                        "chunk": str(chunk_num + 100)  # Offset to differentiate
                    }
                })
                
        elif event_type == "dtmf":
            # DTMF digit received, acknowledge
            dtmf_data = message.get("dtmf", {})
            digit = dtmf_data.get("digit")
            
            responses.append({
                "event": "mark",
                "streamSid": stream_id,
                "mark": {
                    "name": f"dtmf_received_{digit}"
                }
            })
            
        elif event_type == "mark":
            # Mark received from client, process
            mark_data = message.get("mark", {})
            mark_name = mark_data.get("name")
            
            # Send completion acknowledgment
            responses.append({
                "event": "mark",
                "streamSid": stream_id,
                "mark": {
                    "name": f"ack_{mark_name}"
                }
            })
            
        elif event_type == "stop":
            # Stream ended
            stop_data = message.get("stop", {})
            if stream_id in self.session_data:
                del self.session_data[stream_id]
        
        return responses
    
    def _generate_mock_mulaw_audio(self) -> str:
        """Generate mock μ-law audio data as base64."""
        # Create simple μ-law test pattern
        mulaw_samples = []
        for i in range(160):  # 160 samples = 20ms at 8kHz
            # Simple sine wave pattern encoded as μ-law
            sample = int(127 * (1 + 0.5 * (i % 10) / 10))
            mulaw_samples.append(sample & 0xFF)
        
        mulaw_data = bytes(mulaw_samples)
        return base64.b64encode(mulaw_data).decode('utf-8')


async def test_message_format_compliance():
    """Test that all message formats exactly match PDF specification."""
    print("=== Testing Message Format Compliance ===")
    
    try:
        from smartflo_ai_caller.audio_streaming import AudioStreamManager, StreamSession
        
        manager = AudioStreamManager()
        
        # Create test session
        session = await manager.create_stream_session(
            stream_id="MZTEST********************1234",
            account_id="ACTEST********************1234",
            call_id="CATEST********************1234",
            from_number="+**********",
            to_number="+**********",
            custom_parameters={"FirstName": "John", "LastName": "Doe"}
        )
        
        print("✓ Created test session")
        
        # Test 1: Connected event
        connected_event = await manager.create_connected_event()
        expected_connected = {"event": "connected"}
        assert connected_event == expected_connected, f"Connected event mismatch: {connected_event}"
        print("✓ Connected event format correct")
        
        # Test 2: Start event
        start_event = await manager.create_start_event(session)
        
        # Verify start event structure
        assert start_event["event"] == "start"
        assert "sequenceNumber" in start_event
        assert "start" in start_event
        assert "streamSid" in start_event
        
        start_data = start_event["start"]
        assert start_data["streamSid"] == session.stream_id
        assert start_data["accountSid"] == session.account_id
        assert start_data["callSid"] == session.call_id
        assert start_data["from"] == session.from_number
        assert start_data["to"] == session.to_number
        
        # Verify media format
        media_format = start_data["mediaFormat"]
        assert media_format["encoding"] == "audio/x-mulaw"
        assert media_format["sampleRate"] == 8000
        assert media_format["bitRate"] == 64
        assert media_format["bitDepth"] == 8
        
        print("✓ Start event format correct")
        
        # Test 3: Media event
        from smartflo_ai_caller.audio_utils import AudioChunk
        
        # Create test audio chunk
        test_audio = bytes([i % 256 for i in range(160)])  # 160 bytes of test data
        audio_chunk = AudioChunk(
            chunk_number=1,
            timestamp=20,
            payload=test_audio,
            sequence_number=session.next_sequence(),
            stream_id=session.stream_id
        )
        
        media_event = await manager.create_media_event(session, audio_chunk)
        
        assert media_event["event"] == "media"
        assert "sequenceNumber" in media_event
        assert "streamSid" in media_event
        assert media_event["streamSid"] == session.stream_id
        
        media_data = media_event["media"]
        assert "chunk" in media_data
        assert "timestamp" in media_data
        assert "payload" in media_data
        
        # Verify base64 encoding
        decoded_payload = base64.b64decode(media_data["payload"])
        assert len(decoded_payload) > 0
        
        print("✓ Media event format correct")
        
        # Test 4: Stop event
        stop_event = await manager.create_stop_event(session, "Test termination")
        
        assert stop_event["event"] == "stop"
        assert "sequenceNumber" in stop_event
        assert "streamSid" in stop_event
        
        stop_data = stop_event["stop"]
        assert stop_data["accountSid"] == session.account_id
        assert stop_data["callSid"] == session.call_id
        assert stop_data["reason"] == "Test termination"
        
        print("✓ Stop event format correct")
        
        # Test 5: DTMF event
        dtmf_event = await manager.create_dtmf_event(session, "5")
        
        assert dtmf_event["event"] == "dtmf"
        assert dtmf_event["streamSid"] == session.stream_id
        assert "sequenceNumber" in dtmf_event
        assert dtmf_event["dtmf"]["digit"] == "5"
        
        print("✓ DTMF event format correct")
        
        # Test 6: Mark event
        mark_event = await manager.create_mark_event(session, "test_mark")
        
        assert mark_event["event"] == "mark"
        assert mark_event["streamSid"] == session.stream_id
        assert "sequenceNumber" in mark_event
        assert mark_event["mark"]["name"] == "test_mark"
        
        print("✓ Mark event format correct")
        
        # Test 7: Clear event
        clear_event = await manager.create_clear_event(session)
        
        assert clear_event["event"] == "clear"
        assert clear_event["streamSid"] == session.stream_id
        
        print("✓ Clear event format correct")
        
        return True
        
    except Exception as e:
        print(f"❌ Message format test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_audio_format_compliance():
    """Test audio format compliance (8kHz, μ-law, 8-bit, base64)."""
    print("\\n=== Testing Audio Format Compliance ===")
    
    try:
        from smartflo_ai_caller.audio_utils import AudioCodec, AudioProcessor
        
        # Test μ-law codec
        codec = AudioCodec()
        
        # Create test PCM data (16-bit signed)
        test_samples = []
        for i in range(160):  # 160 samples = 20ms at 8kHz
            sample = int(16000 * 0.5 * (i % 20) / 20)  # Simple sawtooth
            test_samples.append(sample)
        
        pcm_data = struct.pack(f'<{len(test_samples)}h', *test_samples)
        print(f"✓ Generated {len(pcm_data)} bytes of test PCM data")
        
        # Test PCM to μ-law conversion
        mulaw_data = codec.encode_pcm_to_mulaw(pcm_data)
        assert len(mulaw_data) == len(test_samples), f"μ-law data length mismatch: {len(mulaw_data)} vs {len(test_samples)}"
        assert all(0 <= b <= 255 for b in mulaw_data), "μ-law data contains invalid bytes"
        print("✓ PCM to μ-law conversion correct")
        
        # Test μ-law to PCM conversion
        decoded_pcm = codec.decode_mulaw_to_pcm(mulaw_data)
        assert len(decoded_pcm) == len(pcm_data), "Decoded PCM length mismatch"
        print("✓ μ-law to PCM conversion correct")
        
        # Test base64 encoding
        base64_data = codec.encode_to_base64(pcm_data)
        assert isinstance(base64_data, str), "Base64 data is not string"
        assert len(base64_data) > 0, "Base64 data is empty"
        
        # Verify base64 can be decoded
        decoded_b64 = base64.b64decode(base64_data)
        assert len(decoded_b64) > 0, "Base64 decoding failed"
        print("✓ Base64 encoding/decoding correct")
        
        # Test audio processor with vendor settings
        processor = AudioProcessor(sample_rate=8000, channels=1, chunk_duration_ms=20)
        
        assert processor.sample_rate == 8000, f"Sample rate incorrect: {processor.sample_rate}"
        assert processor.channels == 1, f"Channels incorrect: {processor.channels}"
        assert processor.chunk_duration_ms == 20, f"Chunk duration incorrect: {processor.chunk_duration_ms}"
        
        # Calculate expected chunk size: 8000 Hz * 0.02s * 2 bytes/sample * 1 channel = 320 bytes
        expected_chunk_size = int((8000 * 20 / 1000) * 2 * 1)
        assert processor.chunk_size_bytes == expected_chunk_size, f"Chunk size incorrect: {processor.chunk_size_bytes} vs {expected_chunk_size}"
        
        print("✓ Audio processor vendor settings correct")
        
        return True
        
    except Exception as e:
        print(f"❌ Audio format test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_mark_synchronization():
    """Test mark synchronization mechanism."""
    print("\\n=== Testing Mark Synchronization ===")
    
    try:
        from smartflo_ai_caller.mark_synchronizer import MarkSynchronizer
        
        synchronizer = MarkSynchronizer()
        stream_id = "test_stream_123"
        
        # Test adding pending marks
        await synchronizer.add_pending_mark(stream_id, "mark1", 1, timeout_seconds=5)
        await synchronizer.add_pending_mark(stream_id, "mark2", 2, timeout_seconds=5)
        
        # Verify pending marks
        pending = await synchronizer.get_pending_marks(stream_id)
        assert len(pending) == 2, f"Expected 2 pending marks, got {len(pending)}"
        print("✓ Added pending marks correctly")
        
        # Test mark completion
        completed = await synchronizer.complete_mark(stream_id, "mark1")
        assert completed, "Mark completion failed"
        
        pending_after = await synchronizer.get_pending_marks(stream_id)
        assert len(pending_after) == 1, f"Expected 1 pending mark after completion, got {len(pending_after)}"
        print("✓ Mark completion works correctly")
        
        # Test stream clearing
        cleared_marks = await synchronizer.clear_stream_marks(stream_id)
        assert len(cleared_marks) == 1, f"Expected 1 cleared mark, got {len(cleared_marks)}"
        
        pending_cleared = await synchronizer.get_pending_marks(stream_id)
        assert len(pending_cleared) == 0, f"Expected 0 pending marks after clear, got {len(pending_cleared)}"
        print("✓ Stream mark clearing works correctly")
        
        # Test statistics
        stats = await synchronizer.get_stream_stats(stream_id)
        assert "total_marks" in stats
        assert "pending" in stats
        assert "completed" in stats
        print("✓ Mark statistics working correctly")
        
        await synchronizer.shutdown()
        print("✓ Mark synchronizer shutdown successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Mark synchronization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_complete_workflow():
    """Test complete bi-directional streaming workflow."""
    print("\\n=== Testing Complete Workflow ===")
    
    try:
        from smartflo_ai_caller.audio_streaming import AudioStreamManager
        from smartflo_ai_caller.bidirectional_ws_handler import BiDirectionalWSHandler
        
        manager = AudioStreamManager()
        handler = BiDirectionalWSHandler(manager)
        
        mock_server = MockVendorServer()
        
        print("1. Testing Stream Creation")
        session = await handler.start_audio_stream(
            stream_id="MZTEST********************1234",
            account_id="ACTEST********************1234",
            call_id="CATEST********************1234",
            from_number="+**********",
            to_number="+**********",
            custom_parameters={"TestParam": "TestValue"}
        )
        
        assert session.stream_id == "MZTEST********************1234"
        print("   ✓ Stream session created successfully")
        
        print("2. Testing Audio Streaming")
        # Generate test audio data
        test_audio = bytes([i % 256 for i in range(3200)])  # 200ms of test data at 8kHz
        
        chunk_count = 0
        async for media_event in manager.stream_audio_chunks(session, test_audio):
            chunk_count += 1
            
            # Verify event format
            assert media_event["event"] == "media"
            assert "streamSid" in media_event
            assert "sequenceNumber" in media_event
            assert "media" in media_event
            
            media_data = media_event["media"]
            assert "chunk" in media_data
            assert "timestamp" in media_data
            assert "payload" in media_data
            
            # Test only first few chunks to avoid long test
            if chunk_count >= 3:
                break
        
        assert chunk_count >= 3, f"Expected at least 3 audio chunks, got {chunk_count}"
        print(f"   ✓ Generated {chunk_count} audio chunks successfully")
        
        print("3. Testing DTMF Handling")
        dtmf_sent = await handler.send_dtmf_to_vendors(session.stream_id, "5")
        assert dtmf_sent, "DTMF sending failed"
        print("   ✓ DTMF sent to vendors successfully")
        
        print("4. Testing Mark Synchronization")
        mark_sent = await handler.send_mark_to_vendors(session.stream_id, "test_marker")
        assert mark_sent, "Mark sending failed"
        print("   ✓ Mark sent to vendors with synchronization")
        
        print("5. Testing Clear Event")
        clear_sent = await handler.send_clear_to_vendors(session.stream_id)
        assert clear_sent, "Clear event sending failed"
        print("   ✓ Clear event sent to vendors successfully")
        
        print("6. Testing Stream Termination")
        stream_stopped = await handler.stop_audio_stream(session.stream_id, "Test completed")
        assert stream_stopped, "Stream stopping failed"
        print("   ✓ Stream terminated successfully")
        
        # Test statistics
        stats = await handler.get_connection_stats()
        assert "streams" in stats
        print("   ✓ Connection statistics available")
        
        await handler.shutdown()
        print("   ✓ Handler shutdown successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Complete workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_inbound_message_processing():
    """Test processing of inbound messages from vendors."""
    print("\\n=== Testing Inbound Message Processing ===")
    
    try:
        from smartflo_ai_caller.audio_streaming import AudioStreamManager
        
        manager = AudioStreamManager()
        stream_id = "MZTEST********************1234"
        
        # Create session for testing
        session = await manager.create_stream_session(
            stream_id=stream_id,
            account_id="ACTEST********************1234",
            call_id="CATEST********************1234", 
            from_number="+**********",
            to_number="+**********"
        )
        
        print("✓ Created test session for inbound processing")
        
        # Test 1: Connected message
        connected_msg = {"event": "connected"}
        await manager.process_inbound_message(connected_msg)
        print("✓ Processed connected message")
        
        # Test 2: Start message
        start_msg = {
            "event": "start",
            "streamSid": stream_id,
            "sequenceNumber": "1",
            "start": {
                "accountSid": "ACTEST********************1234",
                "streamSid": stream_id,
                "callSid": "CATEST********************1234"
            }
        }
        await manager.process_inbound_message(start_msg)
        print("✓ Processed start message")
        
        # Test 3: Media message  
        mock_server = MockVendorServer()
        media_payload = mock_server._generate_mock_mulaw_audio()
        
        media_msg = {
            "event": "media",
            "streamSid": stream_id,
            "sequenceNumber": "2",
            "media": {
                "chunk": "1",
                "timestamp": "20",
                "payload": media_payload
            }
        }
        await manager.process_inbound_message(media_msg)
        print("✓ Processed media message")
        
        # Test 4: DTMF message
        dtmf_msg = {
            "event": "dtmf",
            "streamSid": stream_id,
            "sequenceNumber": "3",
            "dtmf": {
                "digit": "7"
            }
        }
        await manager.process_inbound_message(dtmf_msg)
        print("✓ Processed DTMF message")
        
        # Test 5: Mark message
        mark_msg = {
            "event": "mark",
            "streamSid": stream_id,
            "sequenceNumber": "4",
            "mark": {
                "name": "playback_complete"
            }
        }
        await manager.process_inbound_message(mark_msg)
        print("✓ Processed mark message")
        
        # Test 6: Clear message
        clear_msg = {
            "event": "clear",
            "streamSid": stream_id
        }
        await manager.process_inbound_message(clear_msg)
        print("✓ Processed clear message")
        
        # Test 7: Stop message
        stop_msg = {
            "event": "stop",
            "streamSid": stream_id,
            "sequenceNumber": "5",
            "stop": {
                "accountSid": "ACTEST********************1234",
                "callSid": "CATEST********************1234",
                "reason": "Vendor disconnected"
            }
        }
        await manager.process_inbound_message(stop_msg)
        print("✓ Processed stop message")
        
        return True
        
    except Exception as e:
        print(f"❌ Inbound message processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all bi-directional streaming tests."""
    print("🎙️ Bi-Directional Audio Streaming Compliance Test 🎙️")
    print("=" * 70)
    
    tests = [
        ("Message Format Compliance", test_message_format_compliance),
        ("Audio Format Compliance", test_audio_format_compliance),
        ("Mark Synchronization", test_mark_synchronization),
        ("Inbound Message Processing", test_inbound_message_processing),
        ("Complete Workflow", test_complete_workflow)
    ]
    
    async def run_tests():
        results = []
        
        for test_name, test_func in tests:
            try:
                print(f"\\n{'='*25} {test_name} {'='*25}")
                result = await test_func()
                results.append((test_name, result))
                status = "✅ PASSED" if result else "❌ FAILED"
                print(f"\\n{status}: {test_name}")
            except Exception as e:
                print(f"\\n❌ CRASHED: {test_name} - {e}")
                results.append((test_name, False))
        
        # Final Summary
        print(f"\\n{'='*70}")
        print("📊 FINAL BI-DIRECTIONAL STREAMING TEST RESULTS")
        print(f"{'='*70}")
        
        passed = sum(1 for _, result in results if result)
        
        for test_name, result in results:
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name:<35} {status}")
        
        print(f"\\nOverall Compliance: {passed}/{len(results)} tests passed")
        
        if passed == len(results):
            print("\\n🎉 FULL BI-DIRECTIONAL STREAMING COMPLIANCE ACHIEVED! 🎉")
            print("\\n✅ All event types implemented correctly")
            print("✅ Audio format requirements met (8kHz, μ-law, 8-bit)")
            print("✅ Mark synchronization working")
            print("✅ Complete workflow validated")
            print("✅ Message formats match PDF specification")
            print("\\n🚀 Ready for production bi-directional streaming!")
            return True
        else:
            print(f"\\n⚠️  {len(results) - passed} compliance issue(s) found.")
            return False
    
    success = asyncio.run(run_tests())
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)