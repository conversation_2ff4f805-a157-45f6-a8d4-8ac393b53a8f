#!/usr/bin/env python3
"""
Test script to verify that the llm_answer message handling fix works.
This simulates the exact scenario from the user's logs.
"""

import asyncio
import json
import websockets
import base64
import time


async def test_llm_answer_handling():
    """Test that llm_answer messages are properly handled."""
    print("🎯 Testing llm_answer Message Handling Fix")
    print("="*60)
    
    try:
        # Connect to AI Voice Mate
        print("🔌 Connecting to AI Voice Mate at ws://localhost:5010...")
        websocket = await websockets.connect("ws://localhost:5010")
        print("✅ Connected successfully!")
        
        # Register user (like in the logs)
        print("\n👤 Registering user...")
        store_user_msg = {
            "type": "store_user",
            "session": "test_session_llm_fix",
            "data": {
                "name": "Test",
                "mobile": "+1234567890",
                "userId": "test_user_001",
                "sentences": [],
                "sessionType": "call",
                "target": "english_tutor"
            }
        }
        
        await websocket.send(json.dumps(store_user_msg))
        print("✅ User registration sent")
        
        # Wait for store_user response
        response = await websocket.recv()
        response_data = json.loads(response)
        print(f"📨 Received: {response_data}")
        
        # Start AI call
        print("\n🤖 Starting AI call...")
        start_call_msg = {
            "type": "start_ai_call",
            "session": "test_session_llm_fix",
            "data": None
        }
        
        await websocket.send(json.dumps(start_call_msg))
        print("✅ AI call start sent")
        
        # Enable AI listening
        print("\n🎧 Enabling AI listening...")
        start_listening_msg = {
            "type": "ai_start_listening",
            "session": "test_session_llm_fix",
            "data": {
                "stream_audio": True
            }
        }
        
        await websocket.send(json.dumps(start_listening_msg))
        print("✅ AI listening enabled")
        
        # Send dummy audio to trigger AI response
        print("\n🎤 Sending dummy audio to trigger AI response...")
        dummy_audio_msg = {
            "type": "audio_chunk",
            "session": "test_session_llm_fix",
            "data": [0] * 100  # Dummy audio data
        }
        
        await websocket.send(json.dumps(dummy_audio_msg))
        print("✅ Dummy audio sent")
        
        # Listen for responses
        print("\n👂 Listening for AI responses...")
        print("   Waiting up to 30 seconds for llm_answer message...")
        
        llm_answer_received = False
        start_time = time.time()
        
        while time.time() - start_time < 30:
            try:
                # Wait for message with timeout
                response = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                response_data = json.loads(response)
                message_type = response_data.get("type")
                
                print(f"📨 Received message: {message_type}")
                
                if message_type == "llm_answer":
                    llm_answer_received = True
                    audio_data = response_data.get("data", "")
                    print(f"🔊 llm_answer received with {len(audio_data)} chars of base64 audio!")
                    
                    # Try to decode the audio to verify it's valid
                    try:
                        audio_bytes = base64.b64decode(audio_data)
                        print(f"   ✅ Successfully decoded {len(audio_bytes)} bytes of audio")
                        
                        # Check if it's WAV format
                        if audio_bytes.startswith(b'RIFF') and b'WAVE' in audio_bytes[:12]:
                            print("   🎵 Confirmed: Audio is in WAV format")
                        else:
                            print("   ⚠️  Audio format is not WAV")
                            
                    except Exception as e:
                        print(f"   ❌ Failed to decode audio: {e}")
                    
                    break
                    
                elif message_type == "store_user":
                    print("   📝 User registration confirmed")
                elif message_type == "error":
                    print(f"   ⚠️  Error: {response_data.get('message', 'Unknown')}")
                else:
                    print(f"   📋 Other message: {message_type}")
                    
            except asyncio.TimeoutError:
                # No message received in 1 second, continue waiting
                elapsed = int(time.time() - start_time)
                print(f"   ⏳ Still waiting... ({elapsed}s elapsed)")
                continue
        
        await websocket.close()
        
        if llm_answer_received:
            print("\n🎉 SUCCESS: llm_answer message was received and processed!")
            print("   This confirms that AI Voice Mate is sending audio responses.")
            print("   The fix should work for the main application.")
            return True
        else:
            print("\n❌ FAILURE: No llm_answer message received")
            print("   This could mean:")
            print("   1. AI Voice Mate is not generating audio responses")
            print("   2. The AI is not responding to the dummy audio")
            print("   3. There's a configuration issue")
            return False
            
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        return False


async def main():
    """Main function."""
    print("🧪 llm_answer Message Handling Test")
    print("This test verifies that AI Voice Mate sends llm_answer messages")
    print("and that our fix can properly handle them.")
    print()
    
    success = await test_llm_answer_handling()
    
    print("\n" + "="*60)
    if success:
        print("✅ TEST PASSED: llm_answer handling is working!")
        print("🔧 The audio playback fix should resolve the issue.")
    else:
        print("❌ TEST FAILED: Need to investigate further")
    print("="*60)


if __name__ == "__main__":
    asyncio.run(main())
