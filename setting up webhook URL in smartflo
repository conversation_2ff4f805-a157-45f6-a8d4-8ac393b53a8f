 🔗 Setting Up Webhook URL in Smartflo

  1. Your Webhook URL

  Based on your current setup, your webhook URL should be:

  http://your-domain.com:8000/webhook/smartflo

  https://74f8bfedad0e.ngrok-free.app/webhook/smartflo

  Or for local testing:
  http://localhost:8000/webhook/smartflo

  2. Configure in Smartflo Dashboard

  According to the Smartflo documentation, you need to:

  1. Login to Smartflo Dashboard
    - Access your Tata Telebusiness Smartflo portal
    - Navigate to webhook configuration section
  2. Create New Webhook
    - Name: AI Voice Mate Integration
    - URL: http://your-domain.com:8000/webhook/smartflo
    - HTTP Method: POST
    - Content Type: application/json
    - Call Type: Inbound (or both Inbound/Outbound as needed)
  3. Select Trigger Events
  Choose the events you want to receive:
    - ✅ Call received on server
    - ✅ Call answered on IVR
    - ✅ Call answered by agent
    - ✅ Call hangup (missed/answered)
    - ✅ DTMF option selection
    - ✅ Agent status changes
    - ✅ Call disposition updates

  3. For Production Deployment

  If deploying to production, update these URLs:

  Environment Variables:
  # In your .env file or deployment config
  HOST=0.0.0.0
  PORT=8000
  WEBHOOK_PATH=/webhook/smartflo
  WEBHOOK_SECRET=your_secret_here  # Optional but recommended

  Production URL Examples:
  # Using domain name
  https://your-ai-caller.yourdomain.com/webhook/smartflo

  # Using IP address
  http://************:8000/webhook/smartflo

  # Using load balancer
  https://api.yourcompany.com/smartflo-ai-caller/webhook/smartflo

  4. Security Configuration

  Optional Webhook Secret:
  # Set in Smartflo dashboard and your environment
  WEBHOOK_SECRET=your_secure_secret_key_here

  Firewall/Security Groups:
  - Allow inbound traffic on port 8000 (or your chosen port)
  - Restrict access to Smartflo IP ranges if known

  5. Test Your Webhook URL

  You can test if your webhook is accessible:

  # Test webhook endpoint
  curl -X POST http://your-domain.com:8000/webhook/smartflo \
    -H "Content-Type: application/json" \
    -d '{
      "call_id": "test_001",
      "call_type": "inbound",
      "caller_number": "+1234567890",
      "called_number": "+0987654321",
      "event_type": "call_received",
      "event_timestamp": "2024-01-15T10:30:00Z",
      "call_status": "ringing"
    }'

  Expected response:
  {
    "status": "success",
    "message": "Webhook processed successfully"
  }


  curl -X POST https://74f8bfedad0e.ngrok-free.app/webhook/smartflo \
    -H "Content-Type: application/json" \
    -d '{
      "call_id": "test_001",
      "call_type": "inbound",
      "caller_number": "+1234567890",
      "called_number": "+0987654321",
      "event_type": "call_received",
      "event_timestamp": "2024-01-15T10:30:00Z",
      "call_status": "ringing"
    }'

  6. Deployment Considerations

  For Docker Deployment:
  # docker-compose.yml
  services:
    smartflo-ai-caller:
      ports:
        - "8000:8000"  # Make sure port is exposed
      environment:
        - WEBHOOK_PATH=/webhook/smartflo

  For Cloud Deployment:
  - AWS: Use Application Load Balancer + ECS/EC2
  - Google Cloud: Use Cloud Load Balancing + Cloud Run/GKE
  - Azure: Use Application Gateway + Container Instances/AKS

  7. Monitoring Your Webhook

  Check webhook activity:
  # View webhook logs
  curl http://localhost:8000/health

  # Check metrics  
  curl http://localhost:8000/metrics

  Contact Smartflo Support

  Since webhook configuration requires activation by Tata Cloud
  Telephony support:

  Contact Details:
  - Contact Tata Telebusiness support to enable webhook functionality
  - Provide them your webhook URL:
  http://your-domain.com:8000/webhook/smartflo
  - Request activation for the events you need

  Information to Provide:
  - Account details
  - Webhook URL
  - Required trigger events
  - Expected call volume

  Once configured in Smartflo, your AI Voice Mate integration will start
   receiving real call events! 🚀