#!/usr/bin/env python3
"""
Test script for Voice Call API implementation.
This validates the complete AI Voice Mate WebSocket protocol implementation.
"""

import asyncio
import base64
import json
import struct
import sys
import os
import wave
import io
from typing import Dict, Any, List

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


class MockAIVoiceMateServer:
    """Mock AI Voice Mate server for testing."""
    
    def __init__(self):
        self.received_messages = []
        self.connected_clients = []
        self.session_responses = {}
    
    def add_session_response(self, message_type: str, response_data: Any):
        """Add predefined response for testing."""
        self.session_responses[message_type] = response_data
    
    async def handle_message(self, message: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Handle incoming message and return response(s)."""
        self.received_messages.append(message)
        responses = []
        
        msg_type = message.get("type")
        session_id = message.get("session")
        
        # Handle different message types according to API specification
        if msg_type == "store_user":
            responses.append({
                "type": "store_user",
                "data": "user added successfully"
            })
        
        elif msg_type == "start_ai_call":
            # AI call started, could send initial response
            responses.append({
                "type": "ai_response",
                "data": "Hello! I'm your AI tutor. How can I help you today?"
            })
        
        elif msg_type == "ai_start_listening":
            # AI is now listening, ready for audio chunks
            pass
        
        elif msg_type == "audio_chunk":
            # Simulate processing audio and returning responses
            responses.extend([
                {
                    "type": "transcript_batch",
                    "data": "Hello, I want to practice English"
                },
                {
                    "type": "pause_audio_recording",
                    "data": ""
                },
                {
                    "type": "llm_answer",
                    "data": self._generate_mock_wav_audio()
                },
                {
                    "type": "speech_text",
                    "data": "Hello, I want to practice English"
                }
            ])
        
        elif msg_type == "voice_action_stop":
            # User stopped speaking
            responses.append({
                "type": "ai_response",
                "data": "I understand you want to practice English. That's great!"
            })
        
        elif msg_type == "text_input":
            responses.append({
                "type": "ai_response",
                "data": f"I received your message: {message.get('data')}"
            })
        
        elif msg_type == "end_ai_call":
            responses.append({
                "type": "ai_end_call",
                "data": "ai call completed"
            })
        
        elif msg_type == "request_active_users":
            responses.append({
                "type": "active_users",
                "data": [
                    {
                        "session": session_id,
                        "name": "Test User",
                        "mobile": "+1234567890",
                        "user_id": "test_001",
                        "sentences": []
                    }
                ]
            })
        
        return responses
    
    def _generate_mock_wav_audio(self) -> str:
        """Generate mock WAV audio data as base64."""
        # Create a simple sine wave as mock audio
        sample_rate = 16000
        duration = 0.5  # 500ms
        frequency = 440  # A4 note
        
        # Generate samples
        samples = []
        for i in range(int(sample_rate * duration)):
            t = i / sample_rate
            sample = int(16000 * math.sin(2 * math.pi * frequency * t))
            samples.append(sample)
        
        # Create WAV file in memory
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            
            # Pack samples as bytes
            audio_data = struct.pack(f'<{len(samples)}h', *samples)
            wav_file.writeframes(audio_data)
        
        # Encode as base64
        wav_bytes = wav_buffer.getvalue()
        return base64.b64encode(wav_bytes).decode('utf-8')


async def test_message_handlers():
    """Test all voice call API message handlers."""
    print("=== Testing Voice Call API Message Handlers ===")
    
    try:
        # Import after path setup
        from smartflo_ai_caller.main import (
            handle_llm_answer, handle_transcript_batch, handle_pause_audio_recording,
            handle_active_users, handle_store_user_response
        )
        
        # Test llm_answer handler
        llm_message = {
            "type": "llm_answer",
            "data": "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBjOOzfPZeSsFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwS="
        }
        
        print("✓ Testing llm_answer handler")
        await handle_llm_answer(llm_message)
        
        # Test transcript_batch handler
        transcript_message = {
            "type": "transcript_batch",
            "data": "Hello, I want to practice English"
        }
        
        print("✓ Testing transcript_batch handler")
        await handle_transcript_batch(transcript_message)
        
        # Test pause_audio_recording handler
        pause_message = {
            "type": "pause_audio_recording",
            "data": ""
        }
        
        print("✓ Testing pause_audio_recording handler")
        await handle_pause_audio_recording(pause_message)
        
        # Test active_users handler
        users_message = {
            "type": "active_users",
            "data": [
                {
                    "session": "test_session_123",
                    "name": "John Doe",
                    "mobile": "+1234567890",
                    "user_id": "user_001",
                    "sentences": []
                }
            ]
        }
        
        print("✓ Testing active_users handler")
        await handle_active_users(users_message)
        
        # Test store_user response handler
        store_response = {
            "type": "store_user",
            "data": "user added successfully"
        }
        
        print("✓ Testing store_user response handler")
        await handle_store_user_response(store_response)
        
        print("✅ All message handlers working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Message handler test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_ai_voice_client():
    """Test AI Voice Mate client with Voice Call API methods."""
    print("\n=== Testing AI Voice Client ===")
    
    try:
        from smartflo_ai_caller.ai_voice_client import AIVoiceMateClient
        
        # Create client (won't actually connect in test)
        client = AIVoiceMateClient()
        
        # Test audio chunk format
        test_audio = bytes(range(256))
        session_id = "test_session_123"
        
        print("✓ Testing audio_chunk message format")
        # This would normally send to WebSocket
        message = {
            "type": "audio_chunk",
            "session": session_id,
            "data": list(test_audio)
        }
        
        assert message["type"] == "audio_chunk"
        assert message["session"] == session_id
        assert isinstance(message["data"], list)
        assert len(message["data"]) == 256
        
        print("✓ Testing voice_action_stop method")
        # Test method exists and has correct signature
        assert hasattr(client, 'voice_action_stop')
        
        print("✓ Testing request_active_users method")
        assert hasattr(client, 'request_active_users')
        
        print("✓ Testing send_audio_data method")
        assert hasattr(client, 'send_audio_data')
        
        print("✅ AI Voice Client API compliance verified")
        return True
        
    except Exception as e:
        print(f"❌ AI Voice Client test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_audio_format_compliance():
    """Test audio format compliance with Voice Call API."""
    print("\n=== Testing Audio Format Compliance ===")
    
    try:
        from smartflo_ai_caller.audio_utils import AudioProcessor
        
        # Test 16kHz audio processor
        processor = AudioProcessor(sample_rate=16000, channels=1)
        
        print(f"✓ Audio processor sample rate: {processor.sample_rate}Hz")
        assert processor.sample_rate == 16000
        
        print(f"✓ Audio processor channels: {processor.channels}")
        assert processor.channels == 1
        
        # Test audio resampling
        print("✓ Testing audio resampling")
        test_pcm_8k = bytes(range(0, 320, 2))  # 160 samples at 8kHz (20ms)
        resampled_16k = processor.resample_audio(test_pcm_8k, 8000, 16000)
        
        # Should be approximately double the length
        expected_length = len(test_pcm_8k) * 2
        actual_length = len(resampled_16k)
        print(f"   8kHz data: {len(test_pcm_8k)} bytes")
        print(f"   16kHz data: {actual_length} bytes")
        print(f"   Expected ~{expected_length} bytes")
        
        # Test prepare_ai_audio method
        print("✓ Testing AI audio preparation")
        ai_audio = await processor.prepare_ai_audio(test_pcm_8k, 16000)
        assert len(ai_audio) > 0
        
        print("✅ Audio format compliance verified")
        return True
        
    except Exception as e:
        print(f"❌ Audio format test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_voice_call_workflow():
    """Test complete voice call workflow simulation."""
    print("\n=== Testing Voice Call Workflow ===")
    
    try:
        mock_server = MockAIVoiceMateServer()
        
        print("1. Connection & User Registration")
        # Simulate store_user message
        store_user_msg = {
            "type": "store_user",
            "session": "test_session_123",
            "data": {
                "name": "John Doe",
                "mobile": "+1234567890",
                "userId": "user_001",
                "sentences": [],
                "sessionType": "call",
                "target": "english_tutor"
            }
        }
        
        responses = await mock_server.handle_message(store_user_msg)
        assert len(responses) == 1
        assert responses[0]["type"] == "store_user"
        assert "successfully" in responses[0]["data"]
        print("   ✓ User registration successful")
        
        print("2. Start Voice Call")
        # Start AI call
        start_call_msg = {
            "type": "start_ai_call",
            "session": "test_session_123",
            "data": None
        }
        
        responses = await mock_server.handle_message(start_call_msg)
        assert len(responses) == 1
        assert responses[0]["type"] == "ai_response"
        print("   ✓ AI call started with greeting")
        
        # Enable AI listening
        listening_msg = {
            "type": "ai_start_listening",
            "session": "test_session_123",
            "data": None
        }
        
        responses = await mock_server.handle_message(listening_msg)
        print("   ✓ AI listening enabled")
        
        print("3. Audio Streaming")
        # Send audio chunk
        audio_chunk_msg = {
            "type": "audio_chunk",
            "session": "test_session_123",
            "data": list(bytes(range(1024)))  # 1024 bytes as per API
        }
        
        responses = await mock_server.handle_message(audio_chunk_msg)
        # Should get transcript_batch, pause_audio_recording, llm_answer, speech_text
        assert len(responses) == 4
        
        response_types = [r["type"] for r in responses]
        expected_types = ["transcript_batch", "pause_audio_recording", "llm_answer", "speech_text"]
        
        for expected_type in expected_types:
            assert expected_type in response_types
            print(f"   ✓ Received {expected_type}")
        
        print("4. Voice Action Stop")
        stop_msg = {
            "type": "voice_action_stop",
            "session": "test_session_123",
            "data": "stop_recording"
        }
        
        responses = await mock_server.handle_message(stop_msg)
        assert len(responses) == 1
        assert responses[0]["type"] == "ai_response"
        print("   ✓ Voice action stop handled")
        
        print("5. Text Input")
        text_msg = {
            "type": "text_input",
            "session": "test_session_123",
            "data": "Hello, I want to practice English conversation"
        }
        
        responses = await mock_server.handle_message(text_msg)
        assert len(responses) == 1
        assert responses[0]["type"] == "ai_response"
        print("   ✓ Text input processed")
        
        print("6. Request Active Users")
        users_request = {
            "type": "request_active_users",
            "session": "test_session_123",
            "data": None
        }
        
        responses = await mock_server.handle_message(users_request)
        assert len(responses) == 1
        assert responses[0]["type"] == "active_users"
        assert len(responses[0]["data"]) == 1
        print("   ✓ Active users retrieved")
        
        print("7. End Call")
        end_call_msg = {
            "type": "end_ai_call",
            "session": "test_session_123",
            "data": None
        }
        
        responses = await mock_server.handle_message(end_call_msg)
        assert len(responses) == 1
        assert responses[0]["type"] == "ai_end_call"
        print("   ✓ Call ended successfully")
        
        print("✅ Complete voice call workflow verified")
        return True
        
    except Exception as e:
        print(f"❌ Voice call workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_message_formats():
    """Test that all message formats match API specification exactly."""
    print("\n=== Testing Message Format Compliance ===")
    
    try:
        # Test outbound message formats
        session_id = "test_session_123"
        
        # store_user format
        store_user = {
            "type": "store_user",
            "session": session_id,
            "data": {
                "name": "John Doe",
                "mobile": "+1234567890",
                "userId": "user_001",
                "sentences": [],
                "sessionType": "call",
                "target": "english_tutor"
            }
        }
        assert store_user["type"] == "store_user"
        assert "name" in store_user["data"]
        print("✓ store_user format correct")
        
        # start_ai_call format
        start_call = {
            "type": "start_ai_call",
            "session": session_id,
            "data": None
        }
        assert start_call["type"] == "start_ai_call"
        assert start_call["data"] is None
        print("✓ start_ai_call format correct")
        
        # audio_chunk format
        audio_chunk = {
            "type": "audio_chunk",
            "session": session_id,
            "data": [255, 128, 64, 32, 16]  # Array of bytes
        }
        assert audio_chunk["type"] == "audio_chunk"
        assert isinstance(audio_chunk["data"], list)
        print("✓ audio_chunk format correct")
        
        # ai_start_listening format
        start_listening = {
            "type": "ai_start_listening",
            "session": session_id,
            "data": None
        }
        assert start_listening["type"] == "ai_start_listening"
        print("✓ ai_start_listening format correct")
        
        # voice_action_stop format
        voice_stop = {
            "type": "voice_action_stop",
            "session": session_id,
            "data": "stop_recording"
        }
        assert voice_stop["type"] == "voice_action_stop"
        assert voice_stop["data"] == "stop_recording"
        print("✓ voice_action_stop format correct")
        
        # text_input format
        text_input = {
            "type": "text_input",
            "session": session_id,
            "data": "Hello, I want to practice English"
        }
        assert text_input["type"] == "text_input"
        assert isinstance(text_input["data"], str)
        print("✓ text_input format correct")
        
        # end_ai_call format
        end_call = {
            "type": "end_ai_call",
            "session": session_id,
            "data": None
        }
        assert end_call["type"] == "end_ai_call"
        print("✓ end_ai_call format correct")
        
        # request_active_users format
        request_users = {
            "type": "request_active_users",
            "session": session_id,
            "data": None
        }
        assert request_users["type"] == "request_active_users"
        print("✓ request_active_users format correct")
        
        print("✅ All message formats match API specification")
        return True
        
    except Exception as e:
        print(f"❌ Message format test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all Voice Call API tests."""
    print("🎙️ Voice Call API Implementation Testing 🎙️")
    print("=" * 60)
    
    # Import math for audio generation
    import math
    globals()['math'] = math
    
    tests = [
        ("Message Handlers", test_message_handlers),
        ("AI Voice Client", test_ai_voice_client),
        ("Audio Format Compliance", test_audio_format_compliance),
        ("Message Format Compliance", test_message_formats),
        ("Voice Call Workflow", test_voice_call_workflow)
    ]
    
    async def run_tests():
        results = []
        
        for test_name, test_func in tests:
            try:
                print(f"\n{'='*20} {test_name} {'='*20}")
                result = await test_func()
                results.append((test_name, result))
                if result:
                    print(f"✅ {test_name} PASSED")
                else:
                    print(f"❌ {test_name} FAILED")
            except Exception as e:
                print(f"❌ {test_name} CRASHED: {e}")
                results.append((test_name, False))
        
        # Summary
        print(f"\n{'='*60}")
        print("📊 VOICE CALL API TEST SUMMARY")
        print(f"{'='*60}")
        
        passed = sum(1 for _, result in results if result)
        
        for test_name, result in results:
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name:<25} {status}")
        
        print(f"\nOverall: {passed}/{len(results)} tests passed")
        
        if passed == len(results):
            print("\n🎉 All Voice Call API tests passed!")
            print("✓ Message handlers implemented correctly")
            print("✓ Audio format compliance verified")
            print("✓ Message formats match specification")
            print("✓ Complete workflow validated")
            print("\n✅ Voice Call API implementation is ready!")
            return True
        else:
            print(f"\n⚠️  {len(results) - passed} test(s) failed.")
            return False
    
    success = asyncio.run(run_tests())
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)