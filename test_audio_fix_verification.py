#!/usr/bin/env python3
"""
Verification test for the audio fix.
This test demonstrates that the llm_answer message handling fix works correctly.
"""

import asyncio
import json
import sys
import os

# Add the tests directory to the path so we can import the fixed client
sys.path.append(os.path.join(os.path.dirname(__file__), 'tests'))

from tests.voice_call_client import VoiceCallTestClient


async def test_audio_fix_with_smartflo():
    """Test the audio fix using the smartflo-ai-caller middleware."""
    print("🎯 Testing Audio Fix with SmartFlo AI Caller")
    print("="*60)
    
    # Create a test client with our fix
    client = VoiceCallTestClient()
    
    try:
        # Connect to smartflo-ai-caller (which should forward to AI Voice Mate)
        print("🔌 Connecting to SmartFlo AI Caller...")
        client.server_url = "ws://localhost:8000/audio-stream"  # SmartFlo WebSocket endpoint
        
        if not await client.connect():
            print("❌ Failed to connect to SmartFlo AI Caller")
            return False
        
        print("✅ Connected to SmartFlo AI Caller successfully")
        
        # Register user
        print("\n👤 Registering user...")
        await client.register_user({
            "name": "Audio Fix Test User",
            "mobile": "******-AUDIO-FIX",
            "userId": "audio_fix_test_001"
        })
        
        await asyncio.sleep(1)
        print("✅ User registered")
        
        # Start AI call
        print("\n🤖 Starting AI call...")
        await client.start_ai_call()
        await asyncio.sleep(2)
        print("✅ AI call started")
        
        # Send some audio to trigger AI response
        print("\n🎤 Sending audio to trigger AI response...")
        dummy_audio = [100, -100, 200, -200] * 25  # Simple audio pattern
        await client.send_audio_chunk(dummy_audio)
        print("✅ Audio sent")
        
        # Wait for AI response
        print("\n⏳ Waiting for AI audio response...")
        print("   This will test if our llm_answer handler works...")
        
        initial_responses = client.ai_responses_received
        wait_time = 30
        start_wait = asyncio.get_event_loop().time()
        
        while asyncio.get_event_loop().time() - start_wait < wait_time:
            await asyncio.sleep(1)
            current_responses = client.ai_responses_received
            elapsed = int(asyncio.get_event_loop().time() - start_wait)
            
            if current_responses > initial_responses:
                print(f"🔊 SUCCESS: Received {current_responses - initial_responses} AI audio response(s)!")
                print("🎉 The llm_answer message handler is working correctly!")
                return True
            
            print(f"   ⏳ Still waiting for audio response... ({elapsed}s elapsed)")
        
        print("❌ No AI audio responses received within timeout")
        print("   This could mean:")
        print("   1. AI Voice Mate is not generating responses")
        print("   2. The audio data was too short/simple")
        print("   3. There's still an issue with message routing")
        return False
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if client.is_connected:
            await client.disconnect()
        print("\n🔌 Disconnected from SmartFlo AI Caller")


async def demonstrate_fix():
    """Demonstrate what the fix does."""
    print("🔧 Audio Fix Demonstration")
    print("="*60)
    print()
    print("PROBLEM:")
    print("  - AI Voice Mate was sending audio responses in 'llm_answer' messages")
    print("  - Test client was only looking for 'ai_response' messages")
    print("  - Result: Audio was sent but not processed/played")
    print()
    print("SOLUTION:")
    print("  - Added 'llm_answer' message handler to test client")
    print("  - Handler extracts base64 WAV audio and processes it")
    print("  - Added audio decoding and playback simulation")
    print()
    print("VERIFICATION:")
    print("  - Test client now properly handles llm_answer messages")
    print("  - Audio data is decoded and processed correctly")
    print("  - No more 'UNKNOWN MESSAGE TYPE' errors")
    print()


async def main():
    """Main test function."""
    await demonstrate_fix()
    
    print("🧪 Running Audio Fix Verification Test")
    print("This test verifies that the llm_answer message handling fix works.")
    print()
    
    success = await test_audio_fix_with_smartflo()
    
    print("\n" + "="*60)
    if success:
        print("✅ AUDIO FIX VERIFICATION: PASSED")
        print("🎉 The voice calling system should now work properly!")
        print("🔊 Audio responses will be processed and played correctly.")
    else:
        print("❌ AUDIO FIX VERIFICATION: NEEDS INVESTIGATION")
        print("🔍 The fix is implemented but may need additional debugging.")
    print("="*60)
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
