server:
  host: "0.0.0.0"
  port: 8000
  debug: false
  environment: "production"

ai_voice_mate:
  ws_url: "ws://ai-voice-mate:5010"
  connection_timeout: 30
  retry_attempts: 5
  retry_delay: 10

webhook:
  path: "/webhook/smartflo"
  secret: null  # Should be set via environment variable

logging:
  level: "INFO"
  format: "json"
  file: "/var/log/smartflo-ai-caller/app.log"

session:
  timeout: 300
  max_concurrent_sessions: 100

health_check:
  enabled: true
  path: "/health"

metrics:
  enabled: true
  path: "/metrics"