version: 1
disable_existing_loggers: false

formatters:
  default:
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  json:
    (): "pythonjsonlogger.jsonlogger.JsonFormatter"
    format: "%(asctime)s %(name)s %(levelname)s %(message)s"

handlers:
  console:
    class: logging.StreamHandler
    level: DEBUG
    formatter: default
    stream: ext://sys.stdout
  
  file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: json
    filename: /home/<USER>/PycharmProjects/smartflo-ai-caller/logs/smartflo-ai-caller.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf-8

loggers:
  smartflo_ai_caller:
    level: DEBUG
    handlers: [console, file]
    propagate: false
  
  uvicorn:
    level: INFO
    handlers: [console]
    propagate: false
  
  fastapi:
    level: INFO
    handlers: [console]
    propagate: false
  
  websockets:
    level: WARNING
    handlers: [console]
    propagate: false

root:
  level: INFO
  handlers: [console]