{"asctime": "2025-07-25 15:39:40,296", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 15:39:40,303", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 15:41:40,030", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session cleanup task cancelled", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 15:41:40,030", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session manager shutdown completed", "session_id": null, "user_id": null, "ended_sessions": 0, "correlation_id": null}
{"asctime": "2025-07-25 15:41:40,031", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "WARNING", "message": "Connection closed by AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 15:41:40,031", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 1/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 15:41:40,031", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Disconnected from AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 15:42:08,441", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 15:42:08,457", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 15:43:28,446", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session cleanup task cancelled", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 15:43:28,447", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session manager shutdown completed", "session_id": null, "user_id": null, "ended_sessions": 0, "correlation_id": null}
{"asctime": "2025-07-25 15:43:28,448", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "WARNING", "message": "Connection closed by AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 15:43:28,448", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 1/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 15:43:28,448", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Disconnected from AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 15:43:44,243", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 15:43:44,249", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 15:44:03,576", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "b7d77369-af78-4106-96ab-0ccf9055a781"}
{"asctime": "2025-07-25 15:44:03,576", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "test_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567890", "correlation_id": "b7d77369-af78-4106-96ab-0ccf9055a781"}
{"asctime": "2025-07-25 15:44:03,576", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "test_001", "user_id": null, "correlation_id": "b7d77369-af78-4106-96ab-0ccf9055a781"}
{"asctime": "2025-07-25 15:44:03,577", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "test_001", "user_id": null, "correlation_id": "b7d77369-af78-4106-96ab-0ccf9055a781"}
{"asctime": "2025-07-25 15:44:03,577", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: test_001", "session_id": "test_001", "user_id": null, "caller_number": "+1234567890", "called_number": "+0987654321", "active_sessions": 1, "correlation_id": "b7d77369-af78-4106-96ab-0ccf9055a781"}
{"asctime": "2025-07-25 15:44:03,577", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "test_001", "user_id": "test_001", "correlation_id": "b7d77369-af78-4106-96ab-0ccf9055a781"}
{"asctime": "2025-07-25 15:44:03,577", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: test_001 -> active", "session_id": "test_001", "user_id": null, "new_status": "active", "correlation_id": "b7d77369-af78-4106-96ab-0ccf9055a781"}
{"asctime": "2025-07-25 15:44:20,146", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_call_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "e7c363b6-abdf-4310-abee-a1b5f502d081"}
{"asctime": "2025-07-25 15:44:20,146", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "test_call_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567890", "correlation_id": "e7c363b6-abdf-4310-abee-a1b5f502d081"}
{"asctime": "2025-07-25 15:44:20,146", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "test_call_001", "user_id": null, "correlation_id": "e7c363b6-abdf-4310-abee-a1b5f502d081"}
{"asctime": "2025-07-25 15:44:20,146", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "test_call_001", "user_id": null, "correlation_id": "e7c363b6-abdf-4310-abee-a1b5f502d081"}
{"asctime": "2025-07-25 15:44:20,146", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: test_call_001", "session_id": "test_call_001", "user_id": null, "caller_number": "+1234567890", "called_number": "+0987654321", "active_sessions": 2, "correlation_id": "e7c363b6-abdf-4310-abee-a1b5f502d081"}
{"asctime": "2025-07-25 15:44:20,146", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "test_call_001", "user_id": "test_call_001", "correlation_id": "e7c363b6-abdf-4310-abee-a1b5f502d081"}
{"asctime": "2025-07-25 15:44:20,146", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: test_call_001 -> active", "session_id": "test_call_001", "user_id": null, "new_status": "active", "correlation_id": "e7c363b6-abdf-4310-abee-a1b5f502d081"}
{"asctime": "2025-07-25 15:44:21,212", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_call_001", "user_id": null, "event_type": "call_answered_ivr", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "3038e82d-5f90-4a0b-a6b7-b21bc0947049"}
{"asctime": "2025-07-25 15:44:21,213", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_answered_ivr", "session_id": "test_call_001", "user_id": null, "event_type": "call_answered_ivr", "call_status": "answered", "caller_number": "+1234567890", "correlation_id": "3038e82d-5f90-4a0b-a6b7-b21bc0947049"}
{"asctime": "2025-07-25 15:44:21,213", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_answered_ivr", "session_id": "test_call_001", "user_id": null, "correlation_id": "3038e82d-5f90-4a0b-a6b7-b21bc0947049"}
{"asctime": "2025-07-25 15:44:21,213", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_answered_ivr", "session_id": "test_call_001", "user_id": null, "correlation_id": "3038e82d-5f90-4a0b-a6b7-b21bc0947049"}
{"asctime": "2025-07-25 15:44:21,213", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "AI call started", "session_id": "test_call_001", "user_id": null, "correlation_id": "3038e82d-5f90-4a0b-a6b7-b21bc0947049"}
{"asctime": "2025-07-25 15:44:21,213", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: test_call_001 -> active", "session_id": "test_call_001", "user_id": null, "new_status": "active", "correlation_id": "3038e82d-5f90-4a0b-a6b7-b21bc0947049"}
{"asctime": "2025-07-25 15:44:21,214", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Text input sent to AI", "session_id": "test_call_001", "user_id": null, "text_length": 55, "correlation_id": "3038e82d-5f90-4a0b-a6b7-b21bc0947049"}
{"asctime": "2025-07-25 15:44:21,239", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_call_001", "user_id": null, "event_type": "dtmf_received", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "b339ffef-098c-4a38-9522-c904d25ca693"}
{"asctime": "2025-07-25 15:44:21,240", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: dtmf_received", "session_id": "test_call_001", "user_id": null, "event_type": "dtmf_received", "call_status": "answered", "caller_number": "+1234567890", "correlation_id": "b339ffef-098c-4a38-9522-c904d25ca693"}
{"asctime": "2025-07-25 15:44:21,240", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: dtmf_received", "session_id": "test_call_001", "user_id": null, "correlation_id": "b339ffef-098c-4a38-9522-c904d25ca693"}
{"asctime": "2025-07-25 15:44:21,240", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: dtmf_received", "session_id": "test_call_001", "user_id": null, "correlation_id": "b339ffef-098c-4a38-9522-c904d25ca693"}
{"asctime": "2025-07-25 15:44:21,240", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Text input sent to AI", "session_id": "test_call_001", "user_id": null, "text_length": 30, "correlation_id": "b339ffef-098c-4a38-9522-c904d25ca693"}
{"asctime": "2025-07-25 15:44:21,240", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "DTMF input sent to AI", "session_id": "test_call_001", "user_id": null, "dtmf_digits": "123*", "correlation_id": "b339ffef-098c-4a38-9522-c904d25ca693"}
{"asctime": "2025-07-25 15:44:21,263", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_call_001", "user_id": null, "event_type": "call_hangup", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "81492532-630b-43fa-bcdf-294263a476f6"}
{"asctime": "2025-07-25 15:44:21,263", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_hangup", "session_id": "test_call_001", "user_id": null, "event_type": "call_hangup", "call_status": "ended", "caller_number": "+1234567890", "correlation_id": "81492532-630b-43fa-bcdf-294263a476f6"}
{"asctime": "2025-07-25 15:44:21,263", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_hangup", "session_id": "test_call_001", "user_id": null, "correlation_id": "81492532-630b-43fa-bcdf-294263a476f6"}
{"asctime": "2025-07-25 15:44:21,263", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_hangup", "session_id": "test_call_001", "user_id": null, "correlation_id": "81492532-630b-43fa-bcdf-294263a476f6"}
{"asctime": "2025-07-25 15:44:21,264", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "AI call ended", "session_id": "test_call_001", "user_id": null, "correlation_id": "81492532-630b-43fa-bcdf-294263a476f6"}
{"asctime": "2025-07-25 15:44:21,264", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: test_call_001 -> ended", "session_id": "test_call_001", "user_id": null, "new_status": "ended", "correlation_id": "81492532-630b-43fa-bcdf-294263a476f6"}
{"asctime": "2025-07-25 15:44:21,264", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session ended: test_call_001", "session_id": "test_call_001", "user_id": null, "call_duration": 120, "session_duration": 1.117669, "correlation_id": "81492532-630b-43fa-bcdf-294263a476f6"}
{"asctime": "2025-07-25 15:44:21,395", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: test_call_001 -> ended", "session_id": "test_call_001", "user_id": null, "new_status": "ended", "correlation_id": "872ca053-98ad-4551-a6d6-7cf64da9839a"}
{"asctime": "2025-07-25 15:44:21,395", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session ended: test_call_001", "session_id": "test_call_001", "user_id": null, "call_duration": null, "session_duration": 1.249117, "correlation_id": "872ca053-98ad-4551-a6d6-7cf64da9839a"}
{"asctime": "2025-07-25 15:44:21,440", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_integration_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1111111111", "correlation_id": "301e9508-ea00-4272-8c41-61448f4698e8"}
{"asctime": "2025-07-25 15:44:21,440", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "test_integration_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1111111111", "correlation_id": "301e9508-ea00-4272-8c41-61448f4698e8"}
{"asctime": "2025-07-25 15:44:21,440", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "test_integration_001", "user_id": null, "correlation_id": "301e9508-ea00-4272-8c41-61448f4698e8"}
{"asctime": "2025-07-25 15:44:21,440", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "test_integration_001", "user_id": null, "correlation_id": "301e9508-ea00-4272-8c41-61448f4698e8"}
{"asctime": "2025-07-25 15:44:21,440", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: test_integration_001", "session_id": "test_integration_001", "user_id": null, "caller_number": "+1111111111", "called_number": "+2222222222", "active_sessions": 2, "correlation_id": "301e9508-ea00-4272-8c41-61448f4698e8"}
{"asctime": "2025-07-25 15:44:21,441", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "test_integration_001", "user_id": "test_integration_001", "correlation_id": "301e9508-ea00-4272-8c41-61448f4698e8"}
{"asctime": "2025-07-25 15:44:21,441", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: test_integration_001 -> active", "session_id": "test_integration_001", "user_id": null, "new_status": "active", "correlation_id": "301e9508-ea00-4272-8c41-61448f4698e8"}
{"asctime": "2025-07-25 15:44:23,565", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "concurrent_test_000", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "eb9838fb-3ab4-4c4f-83ea-2119dfc87b9c"}
{"asctime": "2025-07-25 15:44:23,566", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "concurrent_test_000", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567890", "correlation_id": "eb9838fb-3ab4-4c4f-83ea-2119dfc87b9c"}
{"asctime": "2025-07-25 15:44:23,566", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "concurrent_test_000", "user_id": null, "correlation_id": "eb9838fb-3ab4-4c4f-83ea-2119dfc87b9c"}
{"asctime": "2025-07-25 15:44:23,566", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "concurrent_test_000", "user_id": null, "correlation_id": "eb9838fb-3ab4-4c4f-83ea-2119dfc87b9c"}
{"asctime": "2025-07-25 15:44:23,566", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: concurrent_test_000", "session_id": "concurrent_test_000", "user_id": null, "caller_number": "+1234567890", "called_number": "+0987654321", "active_sessions": 3, "correlation_id": "eb9838fb-3ab4-4c4f-83ea-2119dfc87b9c"}
{"asctime": "2025-07-25 15:44:23,567", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "concurrent_test_000", "user_id": "concurrent_test_000", "correlation_id": "eb9838fb-3ab4-4c4f-83ea-2119dfc87b9c"}
{"asctime": "2025-07-25 15:44:23,567", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: concurrent_test_000 -> active", "session_id": "concurrent_test_000", "user_id": null, "new_status": "active", "correlation_id": "eb9838fb-3ab4-4c4f-83ea-2119dfc87b9c"}
{"asctime": "2025-07-25 15:44:23,567", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "concurrent_test_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567891", "correlation_id": "af4ef42d-c4c1-4280-b8c8-776d46db9f71"}
{"asctime": "2025-07-25 15:44:23,567", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "concurrent_test_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567891", "correlation_id": "af4ef42d-c4c1-4280-b8c8-776d46db9f71"}
{"asctime": "2025-07-25 15:44:23,568", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "concurrent_test_001", "user_id": null, "correlation_id": "af4ef42d-c4c1-4280-b8c8-776d46db9f71"}
{"asctime": "2025-07-25 15:44:23,568", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "concurrent_test_001", "user_id": null, "correlation_id": "af4ef42d-c4c1-4280-b8c8-776d46db9f71"}
{"asctime": "2025-07-25 15:44:23,568", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: concurrent_test_001", "session_id": "concurrent_test_001", "user_id": null, "caller_number": "+1234567891", "called_number": "+0987654321", "active_sessions": 4, "correlation_id": "af4ef42d-c4c1-4280-b8c8-776d46db9f71"}
{"asctime": "2025-07-25 15:44:23,568", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "concurrent_test_001", "user_id": "concurrent_test_001", "correlation_id": "af4ef42d-c4c1-4280-b8c8-776d46db9f71"}
{"asctime": "2025-07-25 15:44:23,569", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: concurrent_test_001 -> active", "session_id": "concurrent_test_001", "user_id": null, "new_status": "active", "correlation_id": "af4ef42d-c4c1-4280-b8c8-776d46db9f71"}
{"asctime": "2025-07-25 15:44:23,569", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "concurrent_test_002", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567892", "correlation_id": "cb90018f-0194-40af-ad91-c38190d5daca"}
{"asctime": "2025-07-25 15:44:23,569", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "concurrent_test_002", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567892", "correlation_id": "cb90018f-0194-40af-ad91-c38190d5daca"}
{"asctime": "2025-07-25 15:44:23,570", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "concurrent_test_002", "user_id": null, "correlation_id": "cb90018f-0194-40af-ad91-c38190d5daca"}
{"asctime": "2025-07-25 15:44:23,570", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "concurrent_test_002", "user_id": null, "correlation_id": "cb90018f-0194-40af-ad91-c38190d5daca"}
{"asctime": "2025-07-25 15:44:23,570", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: concurrent_test_002", "session_id": "concurrent_test_002", "user_id": null, "caller_number": "+1234567892", "called_number": "+0987654321", "active_sessions": 5, "correlation_id": "cb90018f-0194-40af-ad91-c38190d5daca"}
{"asctime": "2025-07-25 15:44:23,570", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "concurrent_test_002", "user_id": "concurrent_test_002", "correlation_id": "cb90018f-0194-40af-ad91-c38190d5daca"}
{"asctime": "2025-07-25 15:44:23,570", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: concurrent_test_002 -> active", "session_id": "concurrent_test_002", "user_id": null, "new_status": "active", "correlation_id": "cb90018f-0194-40af-ad91-c38190d5daca"}
{"asctime": "2025-07-25 15:44:23,571", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "concurrent_test_003", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567893", "correlation_id": "ac224aa7-00bd-493c-8aba-bcc1bcb2ae71"}
{"asctime": "2025-07-25 15:44:23,571", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "concurrent_test_003", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567893", "correlation_id": "ac224aa7-00bd-493c-8aba-bcc1bcb2ae71"}
{"asctime": "2025-07-25 15:44:23,571", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "concurrent_test_003", "user_id": null, "correlation_id": "ac224aa7-00bd-493c-8aba-bcc1bcb2ae71"}
{"asctime": "2025-07-25 15:44:23,571", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "concurrent_test_003", "user_id": null, "correlation_id": "ac224aa7-00bd-493c-8aba-bcc1bcb2ae71"}
{"asctime": "2025-07-25 15:44:23,571", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: concurrent_test_003", "session_id": "concurrent_test_003", "user_id": null, "caller_number": "+1234567893", "called_number": "+0987654321", "active_sessions": 6, "correlation_id": "ac224aa7-00bd-493c-8aba-bcc1bcb2ae71"}
{"asctime": "2025-07-25 15:44:23,572", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "concurrent_test_003", "user_id": "concurrent_test_003", "correlation_id": "ac224aa7-00bd-493c-8aba-bcc1bcb2ae71"}
{"asctime": "2025-07-25 15:44:23,572", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: concurrent_test_003 -> active", "session_id": "concurrent_test_003", "user_id": null, "new_status": "active", "correlation_id": "ac224aa7-00bd-493c-8aba-bcc1bcb2ae71"}
{"asctime": "2025-07-25 15:44:23,572", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "concurrent_test_004", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567894", "correlation_id": "f613bacd-323d-4af7-92ac-f63ef288c85b"}
{"asctime": "2025-07-25 15:44:23,572", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "concurrent_test_004", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567894", "correlation_id": "f613bacd-323d-4af7-92ac-f63ef288c85b"}
{"asctime": "2025-07-25 15:44:23,572", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "concurrent_test_004", "user_id": null, "correlation_id": "f613bacd-323d-4af7-92ac-f63ef288c85b"}
{"asctime": "2025-07-25 15:44:23,573", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "concurrent_test_004", "user_id": null, "correlation_id": "f613bacd-323d-4af7-92ac-f63ef288c85b"}
{"asctime": "2025-07-25 15:44:23,573", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: concurrent_test_004", "session_id": "concurrent_test_004", "user_id": null, "caller_number": "+1234567894", "called_number": "+0987654321", "active_sessions": 7, "correlation_id": "f613bacd-323d-4af7-92ac-f63ef288c85b"}
{"asctime": "2025-07-25 15:44:23,573", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "concurrent_test_004", "user_id": "concurrent_test_004", "correlation_id": "f613bacd-323d-4af7-92ac-f63ef288c85b"}
{"asctime": "2025-07-25 15:44:23,573", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: concurrent_test_004 -> active", "session_id": "concurrent_test_004", "user_id": null, "new_status": "active", "correlation_id": "f613bacd-323d-4af7-92ac-f63ef288c85b"}
{"asctime": "2025-07-25 15:45:08,875", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "large_test_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "2627e789-cca8-48bc-808d-159ea8e531f6"}
{"asctime": "2025-07-25 15:45:08,875", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "large_test_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567890", "correlation_id": "2627e789-cca8-48bc-808d-159ea8e531f6"}
{"asctime": "2025-07-25 15:45:08,876", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "large_test_001", "user_id": null, "correlation_id": "2627e789-cca8-48bc-808d-159ea8e531f6"}
{"asctime": "2025-07-25 15:45:08,876", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "large_test_001", "user_id": null, "correlation_id": "2627e789-cca8-48bc-808d-159ea8e531f6"}
{"asctime": "2025-07-25 15:45:08,876", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: large_test_001", "session_id": "large_test_001", "user_id": null, "caller_number": "+1234567890", "called_number": "+0987654321", "active_sessions": 8, "correlation_id": "2627e789-cca8-48bc-808d-159ea8e531f6"}
{"asctime": "2025-07-25 15:45:08,876", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "large_test_001", "user_id": "large_test_001", "correlation_id": "2627e789-cca8-48bc-808d-159ea8e531f6"}
{"asctime": "2025-07-25 15:45:08,876", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: large_test_001 -> active", "session_id": "large_test_001", "user_id": null, "new_status": "active", "correlation_id": "2627e789-cca8-48bc-808d-159ea8e531f6"}
{"asctime": "2025-07-25 15:45:08,880", "name": "smartflo_ai_caller.webhook_handler", "levelname": "WARNING", "message": "Unknown event type received: unknown_event_type", "correlation_id": "f6a8700a-c5aa-4ad3-9e94-8e5e114c2c05"}
{"asctime": "2025-07-25 15:45:08,880", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "unknown_test_001", "user_id": null, "event_type": "unknown_event_type", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "f6a8700a-c5aa-4ad3-9e94-8e5e114c2c05"}
{"asctime": "2025-07-25 15:45:08,880", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: unknown_event_type", "session_id": "unknown_test_001", "user_id": null, "event_type": "unknown_event_type", "call_status": "ringing", "caller_number": "+1234567890", "correlation_id": "f6a8700a-c5aa-4ad3-9e94-8e5e114c2c05"}
{"asctime": "2025-07-25 15:45:08,880", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "WARNING", "message": "Unknown event type received: unknown_event_type", "session_id": "unknown_test_001", "user_id": null, "event_type": "unknown_event_type", "correlation_id": "f6a8700a-c5aa-4ad3-9e94-8e5e114c2c05"}
{"asctime": "2025-07-25 15:45:08,880", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: unknown_event_type", "session_id": "unknown_test_001", "user_id": null, "correlation_id": "f6a8700a-c5aa-4ad3-9e94-8e5e114c2c05"}
{"asctime": "2025-07-25 15:45:08,881", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "WARNING", "message": "No transformer found for action: unknown_event", "session_id": "unknown_test_001", "user_id": null, "correlation_id": "f6a8700a-c5aa-4ad3-9e94-8e5e114c2c05"}
{"asctime": "2025-07-25 15:45:08,883", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "special_test_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1-234-567-890 ext.123", "correlation_id": "8a372325-6a6f-4d1c-a488-bcbd1646d626"}
{"asctime": "2025-07-25 15:45:08,883", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "special_test_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1-234-567-890 ext.123", "correlation_id": "8a372325-6a6f-4d1c-a488-bcbd1646d626"}
{"asctime": "2025-07-25 15:45:08,883", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "special_test_001", "user_id": null, "correlation_id": "8a372325-6a6f-4d1c-a488-bcbd1646d626"}
{"asctime": "2025-07-25 15:45:08,884", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "special_test_001", "user_id": null, "correlation_id": "8a372325-6a6f-4d1c-a488-bcbd1646d626"}
{"asctime": "2025-07-25 15:45:08,884", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: special_test_001", "session_id": "special_test_001", "user_id": null, "caller_number": "+1-234-567-890 ext.123", "called_number": "+098-765-4321", "active_sessions": 9, "correlation_id": "8a372325-6a6f-4d1c-a488-bcbd1646d626"}
{"asctime": "2025-07-25 15:45:08,884", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "special_test_001", "user_id": "special_test_001", "correlation_id": "8a372325-6a6f-4d1c-a488-bcbd1646d626"}
{"asctime": "2025-07-25 15:45:08,884", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: special_test_001 -> active", "session_id": "special_test_001", "user_id": null, "new_status": "active", "correlation_id": "8a372325-6a6f-4d1c-a488-bcbd1646d626"}
{"asctime": "2025-07-25 15:45:08,889", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "complete_flow_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+5555555555", "correlation_id": "b696d08e-fb1b-4caf-af4b-2c744d327241"}
{"asctime": "2025-07-25 15:45:08,889", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "complete_flow_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+5555555555", "correlation_id": "b696d08e-fb1b-4caf-af4b-2c744d327241"}
{"asctime": "2025-07-25 15:45:08,889", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "complete_flow_001", "user_id": null, "correlation_id": "b696d08e-fb1b-4caf-af4b-2c744d327241"}
{"asctime": "2025-07-25 15:45:08,889", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "complete_flow_001", "user_id": null, "correlation_id": "b696d08e-fb1b-4caf-af4b-2c744d327241"}
{"asctime": "2025-07-25 15:45:08,889", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: complete_flow_001", "session_id": "complete_flow_001", "user_id": null, "caller_number": "+5555555555", "called_number": "+4444444444", "active_sessions": 10, "correlation_id": "b696d08e-fb1b-4caf-af4b-2c744d327241"}
{"asctime": "2025-07-25 15:45:08,890", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "complete_flow_001", "user_id": "complete_flow_001", "correlation_id": "b696d08e-fb1b-4caf-af4b-2c744d327241"}
{"asctime": "2025-07-25 15:45:08,890", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: complete_flow_001 -> active", "session_id": "complete_flow_001", "user_id": null, "new_status": "active", "correlation_id": "b696d08e-fb1b-4caf-af4b-2c744d327241"}
{"asctime": "2025-07-25 15:45:08,892", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "complete_flow_001", "user_id": null, "event_type": "call_answered_ivr", "call_type": "inbound", "caller_number": "+5555555555", "correlation_id": "83767ea7-27bf-4aee-84e5-28b8176b7a47"}
{"asctime": "2025-07-25 15:45:08,892", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_answered_ivr", "session_id": "complete_flow_001", "user_id": null, "event_type": "call_answered_ivr", "call_status": "answered", "caller_number": "+5555555555", "correlation_id": "83767ea7-27bf-4aee-84e5-28b8176b7a47"}
{"asctime": "2025-07-25 15:45:08,893", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_answered_ivr", "session_id": "complete_flow_001", "user_id": null, "correlation_id": "83767ea7-27bf-4aee-84e5-28b8176b7a47"}
{"asctime": "2025-07-25 15:45:08,893", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_answered_ivr", "session_id": "complete_flow_001", "user_id": null, "correlation_id": "83767ea7-27bf-4aee-84e5-28b8176b7a47"}
{"asctime": "2025-07-25 15:45:08,893", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "AI call started", "session_id": "complete_flow_001", "user_id": null, "correlation_id": "83767ea7-27bf-4aee-84e5-28b8176b7a47"}
{"asctime": "2025-07-25 15:45:08,893", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: complete_flow_001 -> active", "session_id": "complete_flow_001", "user_id": null, "new_status": "active", "correlation_id": "83767ea7-27bf-4aee-84e5-28b8176b7a47"}
{"asctime": "2025-07-25 15:45:08,893", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Text input sent to AI", "session_id": "complete_flow_001", "user_id": null, "text_length": 55, "correlation_id": "83767ea7-27bf-4aee-84e5-28b8176b7a47"}
{"asctime": "2025-07-25 15:45:08,896", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "complete_flow_001", "user_id": null, "event_type": "dtmf_received", "call_type": "inbound", "caller_number": "+5555555555", "correlation_id": "ab6f818d-e873-4917-873b-d5bdbb1d06b0"}
{"asctime": "2025-07-25 15:45:08,896", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: dtmf_received", "session_id": "complete_flow_001", "user_id": null, "event_type": "dtmf_received", "call_status": "answered", "caller_number": "+5555555555", "correlation_id": "ab6f818d-e873-4917-873b-d5bdbb1d06b0"}
{"asctime": "2025-07-25 15:45:08,896", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: dtmf_received", "session_id": "complete_flow_001", "user_id": null, "correlation_id": "ab6f818d-e873-4917-873b-d5bdbb1d06b0"}
{"asctime": "2025-07-25 15:45:08,896", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: dtmf_received", "session_id": "complete_flow_001", "user_id": null, "correlation_id": "ab6f818d-e873-4917-873b-d5bdbb1d06b0"}
{"asctime": "2025-07-25 15:45:08,896", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Text input sent to AI", "session_id": "complete_flow_001", "user_id": null, "text_length": 32, "correlation_id": "ab6f818d-e873-4917-873b-d5bdbb1d06b0"}
{"asctime": "2025-07-25 15:45:08,897", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "DTMF input sent to AI", "session_id": "complete_flow_001", "user_id": null, "dtmf_digits": "1234*#", "correlation_id": "ab6f818d-e873-4917-873b-d5bdbb1d06b0"}
{"asctime": "2025-07-25 15:45:08,898", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "complete_flow_001", "user_id": null, "event_type": "call_hangup", "call_type": "inbound", "caller_number": "+5555555555", "correlation_id": "962f3b82-c2f9-4415-8161-325231b3a14a"}
{"asctime": "2025-07-25 15:45:08,899", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_hangup", "session_id": "complete_flow_001", "user_id": null, "event_type": "call_hangup", "call_status": "ended", "caller_number": "+5555555555", "correlation_id": "962f3b82-c2f9-4415-8161-325231b3a14a"}
{"asctime": "2025-07-25 15:45:08,899", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_hangup", "session_id": "complete_flow_001", "user_id": null, "correlation_id": "962f3b82-c2f9-4415-8161-325231b3a14a"}
{"asctime": "2025-07-25 15:45:08,899", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_hangup", "session_id": "complete_flow_001", "user_id": null, "correlation_id": "962f3b82-c2f9-4415-8161-325231b3a14a"}
{"asctime": "2025-07-25 15:45:08,899", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "AI call ended", "session_id": "complete_flow_001", "user_id": null, "correlation_id": "962f3b82-c2f9-4415-8161-325231b3a14a"}
{"asctime": "2025-07-25 15:45:08,899", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: complete_flow_001 -> ended", "session_id": "complete_flow_001", "user_id": null, "new_status": "ended", "correlation_id": "962f3b82-c2f9-4415-8161-325231b3a14a"}
{"asctime": "2025-07-25 15:45:08,900", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session ended: complete_flow_001", "session_id": "complete_flow_001", "user_id": null, "call_duration": 120, "session_duration": 0.010114, "correlation_id": "962f3b82-c2f9-4415-8161-325231b3a14a"}
{"asctime": "2025-07-25 15:45:08,905", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "rapid_test_000", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "ba65ee43-2b56-4ad3-9889-12f6a0b1b6ba"}
{"asctime": "2025-07-25 15:45:08,905", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "rapid_test_000", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567890", "correlation_id": "ba65ee43-2b56-4ad3-9889-12f6a0b1b6ba"}
{"asctime": "2025-07-25 15:45:08,905", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "rapid_test_000", "user_id": null, "correlation_id": "ba65ee43-2b56-4ad3-9889-12f6a0b1b6ba"}
{"asctime": "2025-07-25 15:45:08,905", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "rapid_test_000", "user_id": null, "correlation_id": "ba65ee43-2b56-4ad3-9889-12f6a0b1b6ba"}
{"asctime": "2025-07-25 15:45:08,906", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: rapid_test_000", "session_id": "rapid_test_000", "user_id": null, "caller_number": "+1234567890", "called_number": "+0987654321", "active_sessions": 10, "correlation_id": "ba65ee43-2b56-4ad3-9889-12f6a0b1b6ba"}
{"asctime": "2025-07-25 15:45:08,906", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "rapid_test_000", "user_id": "rapid_test_000", "correlation_id": "ba65ee43-2b56-4ad3-9889-12f6a0b1b6ba"}
{"asctime": "2025-07-25 15:45:08,906", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: rapid_test_000 -> active", "session_id": "rapid_test_000", "user_id": null, "new_status": "active", "correlation_id": "ba65ee43-2b56-4ad3-9889-12f6a0b1b6ba"}
{"asctime": "2025-07-25 15:45:08,914", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "rapid_test_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567891", "correlation_id": "25e543ef-3f0f-443f-b1a7-d9b6b4c326b8"}
{"asctime": "2025-07-25 15:45:08,914", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "rapid_test_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567891", "correlation_id": "25e543ef-3f0f-443f-b1a7-d9b6b4c326b8"}
{"asctime": "2025-07-25 15:45:08,915", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "rapid_test_001", "user_id": null, "correlation_id": "25e543ef-3f0f-443f-b1a7-d9b6b4c326b8"}
{"asctime": "2025-07-25 15:45:08,915", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "rapid_test_001", "user_id": null, "correlation_id": "25e543ef-3f0f-443f-b1a7-d9b6b4c326b8"}
{"asctime": "2025-07-25 15:45:08,915", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: rapid_test_001", "session_id": "rapid_test_001", "user_id": null, "caller_number": "+1234567891", "called_number": "+0987654321", "active_sessions": 11, "correlation_id": "25e543ef-3f0f-443f-b1a7-d9b6b4c326b8"}
{"asctime": "2025-07-25 15:45:08,915", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "rapid_test_001", "user_id": "rapid_test_001", "correlation_id": "25e543ef-3f0f-443f-b1a7-d9b6b4c326b8"}
{"asctime": "2025-07-25 15:45:08,916", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: rapid_test_001 -> active", "session_id": "rapid_test_001", "user_id": null, "new_status": "active", "correlation_id": "25e543ef-3f0f-443f-b1a7-d9b6b4c326b8"}
{"asctime": "2025-07-25 15:45:08,918", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "rapid_test_002", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567892", "correlation_id": "5dba9090-f04f-44cc-9ffe-ae303b40aa23"}
{"asctime": "2025-07-25 15:45:08,918", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "rapid_test_002", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567892", "correlation_id": "5dba9090-f04f-44cc-9ffe-ae303b40aa23"}
{"asctime": "2025-07-25 15:45:08,918", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "rapid_test_002", "user_id": null, "correlation_id": "5dba9090-f04f-44cc-9ffe-ae303b40aa23"}
{"asctime": "2025-07-25 15:45:08,918", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "rapid_test_002", "user_id": null, "correlation_id": "5dba9090-f04f-44cc-9ffe-ae303b40aa23"}
{"asctime": "2025-07-25 15:45:08,919", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: rapid_test_002", "session_id": "rapid_test_002", "user_id": null, "caller_number": "+1234567892", "called_number": "+0987654321", "active_sessions": 12, "correlation_id": "5dba9090-f04f-44cc-9ffe-ae303b40aa23"}
{"asctime": "2025-07-25 15:45:08,919", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "rapid_test_002", "user_id": "rapid_test_002", "correlation_id": "5dba9090-f04f-44cc-9ffe-ae303b40aa23"}
{"asctime": "2025-07-25 15:45:08,919", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: rapid_test_002 -> active", "session_id": "rapid_test_002", "user_id": null, "new_status": "active", "correlation_id": "5dba9090-f04f-44cc-9ffe-ae303b40aa23"}
{"asctime": "2025-07-25 15:45:08,920", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "rapid_test_003", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567893", "correlation_id": "05985f20-b02f-485c-bfb1-70074ce23963"}
{"asctime": "2025-07-25 15:45:08,920", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "rapid_test_003", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567893", "correlation_id": "05985f20-b02f-485c-bfb1-70074ce23963"}
{"asctime": "2025-07-25 15:45:08,921", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "rapid_test_003", "user_id": null, "correlation_id": "05985f20-b02f-485c-bfb1-70074ce23963"}
{"asctime": "2025-07-25 15:45:08,921", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "rapid_test_003", "user_id": null, "correlation_id": "05985f20-b02f-485c-bfb1-70074ce23963"}
{"asctime": "2025-07-25 15:45:08,921", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: rapid_test_003", "session_id": "rapid_test_003", "user_id": null, "caller_number": "+1234567893", "called_number": "+0987654321", "active_sessions": 13, "correlation_id": "05985f20-b02f-485c-bfb1-70074ce23963"}
{"asctime": "2025-07-25 15:45:08,921", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "rapid_test_003", "user_id": "rapid_test_003", "correlation_id": "05985f20-b02f-485c-bfb1-70074ce23963"}
{"asctime": "2025-07-25 15:45:08,922", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: rapid_test_003 -> active", "session_id": "rapid_test_003", "user_id": null, "new_status": "active", "correlation_id": "05985f20-b02f-485c-bfb1-70074ce23963"}
{"asctime": "2025-07-25 15:45:08,922", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "rapid_test_004", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567894", "correlation_id": "329f0720-15dd-42db-a8ca-090a22d3f764"}
{"asctime": "2025-07-25 15:45:08,923", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "rapid_test_004", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567894", "correlation_id": "329f0720-15dd-42db-a8ca-090a22d3f764"}
{"asctime": "2025-07-25 15:45:08,923", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "rapid_test_004", "user_id": null, "correlation_id": "329f0720-15dd-42db-a8ca-090a22d3f764"}
{"asctime": "2025-07-25 15:45:08,923", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "rapid_test_004", "user_id": null, "correlation_id": "329f0720-15dd-42db-a8ca-090a22d3f764"}
{"asctime": "2025-07-25 15:45:08,923", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: rapid_test_004", "session_id": "rapid_test_004", "user_id": null, "caller_number": "+1234567894", "called_number": "+0987654321", "active_sessions": 14, "correlation_id": "329f0720-15dd-42db-a8ca-090a22d3f764"}
{"asctime": "2025-07-25 15:45:08,924", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "rapid_test_004", "user_id": "rapid_test_004", "correlation_id": "329f0720-15dd-42db-a8ca-090a22d3f764"}
{"asctime": "2025-07-25 15:45:08,924", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: rapid_test_004 -> active", "session_id": "rapid_test_004", "user_id": null, "new_status": "active", "correlation_id": "329f0720-15dd-42db-a8ca-090a22d3f764"}
{"asctime": "2025-07-25 15:45:08,924", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "rapid_test_005", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567895", "correlation_id": "9f799eb7-132f-4fee-a7da-093088511d7b"}
{"asctime": "2025-07-25 15:45:08,924", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "rapid_test_005", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567895", "correlation_id": "9f799eb7-132f-4fee-a7da-093088511d7b"}
{"asctime": "2025-07-25 15:45:08,924", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "rapid_test_005", "user_id": null, "correlation_id": "9f799eb7-132f-4fee-a7da-093088511d7b"}
{"asctime": "2025-07-25 15:45:08,925", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "rapid_test_005", "user_id": null, "correlation_id": "9f799eb7-132f-4fee-a7da-093088511d7b"}
{"asctime": "2025-07-25 15:45:08,925", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: rapid_test_005", "session_id": "rapid_test_005", "user_id": null, "caller_number": "+1234567895", "called_number": "+0987654321", "active_sessions": 15, "correlation_id": "9f799eb7-132f-4fee-a7da-093088511d7b"}
{"asctime": "2025-07-25 15:45:08,925", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "rapid_test_005", "user_id": "rapid_test_005", "correlation_id": "9f799eb7-132f-4fee-a7da-093088511d7b"}
{"asctime": "2025-07-25 15:45:08,925", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: rapid_test_005 -> active", "session_id": "rapid_test_005", "user_id": null, "new_status": "active", "correlation_id": "9f799eb7-132f-4fee-a7da-093088511d7b"}
{"asctime": "2025-07-25 15:45:08,926", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "rapid_test_006", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567896", "correlation_id": "f5ee8131-bc4c-47f2-ab11-dddd8f88a206"}
{"asctime": "2025-07-25 15:45:08,926", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "rapid_test_006", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567896", "correlation_id": "f5ee8131-bc4c-47f2-ab11-dddd8f88a206"}
{"asctime": "2025-07-25 15:45:08,926", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "rapid_test_006", "user_id": null, "correlation_id": "f5ee8131-bc4c-47f2-ab11-dddd8f88a206"}
{"asctime": "2025-07-25 15:45:08,926", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "rapid_test_006", "user_id": null, "correlation_id": "f5ee8131-bc4c-47f2-ab11-dddd8f88a206"}
{"asctime": "2025-07-25 15:45:08,926", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: rapid_test_006", "session_id": "rapid_test_006", "user_id": null, "caller_number": "+1234567896", "called_number": "+0987654321", "active_sessions": 16, "correlation_id": "f5ee8131-bc4c-47f2-ab11-dddd8f88a206"}
{"asctime": "2025-07-25 15:45:08,927", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "rapid_test_006", "user_id": "rapid_test_006", "correlation_id": "f5ee8131-bc4c-47f2-ab11-dddd8f88a206"}
{"asctime": "2025-07-25 15:45:08,927", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: rapid_test_006 -> active", "session_id": "rapid_test_006", "user_id": null, "new_status": "active", "correlation_id": "f5ee8131-bc4c-47f2-ab11-dddd8f88a206"}
{"asctime": "2025-07-25 15:45:08,928", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "rapid_test_007", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567897", "correlation_id": "1b77a8db-eee2-431b-b6ff-9a05c399c4f6"}
{"asctime": "2025-07-25 15:45:08,928", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "rapid_test_007", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567897", "correlation_id": "1b77a8db-eee2-431b-b6ff-9a05c399c4f6"}
{"asctime": "2025-07-25 15:45:08,928", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "rapid_test_007", "user_id": null, "correlation_id": "1b77a8db-eee2-431b-b6ff-9a05c399c4f6"}
{"asctime": "2025-07-25 15:45:08,928", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "rapid_test_007", "user_id": null, "correlation_id": "1b77a8db-eee2-431b-b6ff-9a05c399c4f6"}
{"asctime": "2025-07-25 15:45:08,928", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: rapid_test_007", "session_id": "rapid_test_007", "user_id": null, "caller_number": "+1234567897", "called_number": "+0987654321", "active_sessions": 17, "correlation_id": "1b77a8db-eee2-431b-b6ff-9a05c399c4f6"}
{"asctime": "2025-07-25 15:45:08,929", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "rapid_test_007", "user_id": "rapid_test_007", "correlation_id": "1b77a8db-eee2-431b-b6ff-9a05c399c4f6"}
{"asctime": "2025-07-25 15:45:08,929", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: rapid_test_007 -> active", "session_id": "rapid_test_007", "user_id": null, "new_status": "active", "correlation_id": "1b77a8db-eee2-431b-b6ff-9a05c399c4f6"}
{"asctime": "2025-07-25 15:45:08,929", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "rapid_test_008", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567898", "correlation_id": "7f697f2f-cf4e-4ace-9cd6-d2ba1d60d6dd"}
{"asctime": "2025-07-25 15:45:08,929", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "rapid_test_008", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567898", "correlation_id": "7f697f2f-cf4e-4ace-9cd6-d2ba1d60d6dd"}
{"asctime": "2025-07-25 15:45:08,929", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "rapid_test_008", "user_id": null, "correlation_id": "7f697f2f-cf4e-4ace-9cd6-d2ba1d60d6dd"}
{"asctime": "2025-07-25 15:45:08,930", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "rapid_test_008", "user_id": null, "correlation_id": "7f697f2f-cf4e-4ace-9cd6-d2ba1d60d6dd"}
{"asctime": "2025-07-25 15:45:08,930", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: rapid_test_008", "session_id": "rapid_test_008", "user_id": null, "caller_number": "+1234567898", "called_number": "+0987654321", "active_sessions": 18, "correlation_id": "7f697f2f-cf4e-4ace-9cd6-d2ba1d60d6dd"}
{"asctime": "2025-07-25 15:45:08,930", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "rapid_test_008", "user_id": "rapid_test_008", "correlation_id": "7f697f2f-cf4e-4ace-9cd6-d2ba1d60d6dd"}
{"asctime": "2025-07-25 15:45:08,930", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: rapid_test_008 -> active", "session_id": "rapid_test_008", "user_id": null, "new_status": "active", "correlation_id": "7f697f2f-cf4e-4ace-9cd6-d2ba1d60d6dd"}
{"asctime": "2025-07-25 15:45:08,931", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "rapid_test_009", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567899", "correlation_id": "abf2ac8b-c4ab-4e69-8470-7caf31f54dc5"}
{"asctime": "2025-07-25 15:45:08,931", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "rapid_test_009", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567899", "correlation_id": "abf2ac8b-c4ab-4e69-8470-7caf31f54dc5"}
{"asctime": "2025-07-25 15:45:08,931", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "rapid_test_009", "user_id": null, "correlation_id": "abf2ac8b-c4ab-4e69-8470-7caf31f54dc5"}
{"asctime": "2025-07-25 15:45:08,932", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "rapid_test_009", "user_id": null, "correlation_id": "abf2ac8b-c4ab-4e69-8470-7caf31f54dc5"}
{"asctime": "2025-07-25 15:45:08,932", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: rapid_test_009", "session_id": "rapid_test_009", "user_id": null, "caller_number": "+1234567899", "called_number": "+0987654321", "active_sessions": 19, "correlation_id": "abf2ac8b-c4ab-4e69-8470-7caf31f54dc5"}
{"asctime": "2025-07-25 15:45:08,932", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "rapid_test_009", "user_id": "rapid_test_009", "correlation_id": "abf2ac8b-c4ab-4e69-8470-7caf31f54dc5"}
{"asctime": "2025-07-25 15:45:08,932", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: rapid_test_009 -> active", "session_id": "rapid_test_009", "user_id": null, "new_status": "active", "correlation_id": "abf2ac8b-c4ab-4e69-8470-7caf31f54dc5"}
{"asctime": "2025-07-25 15:46:03,527", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "demo_call_12345", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1-555-123-4567", "correlation_id": "b9782b1f-e9c3-4738-902e-e9045d95b1c7"}
{"asctime": "2025-07-25 15:46:03,527", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "demo_call_12345", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1-555-123-4567", "correlation_id": "b9782b1f-e9c3-4738-902e-e9045d95b1c7"}
{"asctime": "2025-07-25 15:46:03,527", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "demo_call_12345", "user_id": null, "correlation_id": "b9782b1f-e9c3-4738-902e-e9045d95b1c7"}
{"asctime": "2025-07-25 15:46:03,528", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "demo_call_12345", "user_id": null, "correlation_id": "b9782b1f-e9c3-4738-902e-e9045d95b1c7"}
{"asctime": "2025-07-25 15:46:03,528", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: demo_call_12345", "session_id": "demo_call_12345", "user_id": null, "caller_number": "+1-555-123-4567", "called_number": "+1-800-AI-VOICE", "active_sessions": 20, "correlation_id": "b9782b1f-e9c3-4738-902e-e9045d95b1c7"}
{"asctime": "2025-07-25 15:46:03,528", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "demo_call_12345", "user_id": "demo_call_12345", "correlation_id": "b9782b1f-e9c3-4738-902e-e9045d95b1c7"}
{"asctime": "2025-07-25 15:46:03,528", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: demo_call_12345 -> active", "session_id": "demo_call_12345", "user_id": null, "new_status": "active", "correlation_id": "b9782b1f-e9c3-4738-902e-e9045d95b1c7"}
{"asctime": "2025-07-25 15:46:04,534", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "demo_call_12345", "user_id": null, "event_type": "call_answered_ivr", "call_type": "inbound", "caller_number": "+1-555-123-4567", "correlation_id": "98339f5c-07c0-4d65-baee-f31368886821"}
{"asctime": "2025-07-25 15:46:04,534", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_answered_ivr", "session_id": "demo_call_12345", "user_id": null, "event_type": "call_answered_ivr", "call_status": "answered", "caller_number": "+1-555-123-4567", "correlation_id": "98339f5c-07c0-4d65-baee-f31368886821"}
{"asctime": "2025-07-25 15:46:04,534", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_answered_ivr", "session_id": "demo_call_12345", "user_id": null, "correlation_id": "98339f5c-07c0-4d65-baee-f31368886821"}
{"asctime": "2025-07-25 15:46:04,535", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_answered_ivr", "session_id": "demo_call_12345", "user_id": null, "correlation_id": "98339f5c-07c0-4d65-baee-f31368886821"}
{"asctime": "2025-07-25 15:46:04,535", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "AI call started", "session_id": "demo_call_12345", "user_id": null, "correlation_id": "98339f5c-07c0-4d65-baee-f31368886821"}
{"asctime": "2025-07-25 15:46:04,535", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: demo_call_12345 -> active", "session_id": "demo_call_12345", "user_id": null, "new_status": "active", "correlation_id": "98339f5c-07c0-4d65-baee-f31368886821"}
{"asctime": "2025-07-25 15:46:04,536", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Text input sent to AI", "session_id": "demo_call_12345", "user_id": null, "text_length": 55, "correlation_id": "98339f5c-07c0-4d65-baee-f31368886821"}
{"asctime": "2025-07-25 15:46:06,539", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "demo_call_12345", "user_id": null, "event_type": "dtmf_received", "call_type": "inbound", "caller_number": "+1-555-123-4567", "correlation_id": "2622b5c6-a956-4829-8e4e-f61214eecd0b"}
{"asctime": "2025-07-25 15:46:06,539", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: dtmf_received", "session_id": "demo_call_12345", "user_id": null, "event_type": "dtmf_received", "call_status": "answered", "caller_number": "+1-555-123-4567", "correlation_id": "2622b5c6-a956-4829-8e4e-f61214eecd0b"}
{"asctime": "2025-07-25 15:46:06,540", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: dtmf_received", "session_id": "demo_call_12345", "user_id": null, "correlation_id": "2622b5c6-a956-4829-8e4e-f61214eecd0b"}
{"asctime": "2025-07-25 15:46:06,540", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: dtmf_received", "session_id": "demo_call_12345", "user_id": null, "correlation_id": "2622b5c6-a956-4829-8e4e-f61214eecd0b"}
{"asctime": "2025-07-25 15:46:06,540", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Text input sent to AI", "session_id": "demo_call_12345", "user_id": null, "text_length": 30, "correlation_id": "2622b5c6-a956-4829-8e4e-f61214eecd0b"}
{"asctime": "2025-07-25 15:46:06,540", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "DTMF input sent to AI", "session_id": "demo_call_12345", "user_id": null, "dtmf_digits": "123*", "correlation_id": "2622b5c6-a956-4829-8e4e-f61214eecd0b"}
{"asctime": "2025-07-25 15:46:07,544", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "demo_call_12345", "user_id": null, "event_type": "agent_status_change", "call_type": "inbound", "caller_number": "+1-555-123-4567", "correlation_id": "e62063f2-4adf-4cbe-89f2-d7d04f8b23f6"}
{"asctime": "2025-07-25 15:46:07,544", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: agent_status_change", "session_id": "demo_call_12345", "user_id": null, "event_type": "agent_status_change", "call_status": "answered", "caller_number": "+1-555-123-4567", "correlation_id": "e62063f2-4adf-4cbe-89f2-d7d04f8b23f6"}
{"asctime": "2025-07-25 15:46:07,544", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: agent_status_change", "session_id": "demo_call_12345", "user_id": null, "correlation_id": "e62063f2-4adf-4cbe-89f2-d7d04f8b23f6"}
{"asctime": "2025-07-25 15:46:07,545", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: agent_status_change", "session_id": "demo_call_12345", "user_id": null, "correlation_id": "e62063f2-4adf-4cbe-89f2-d7d04f8b23f6"}
{"asctime": "2025-07-25 15:46:08,549", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "demo_call_12345", "user_id": null, "event_type": "call_hangup", "call_type": "inbound", "caller_number": "+1-555-123-4567", "correlation_id": "9bfa1f5f-28b5-441a-9714-726acc88becd"}
{"asctime": "2025-07-25 15:46:08,549", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_hangup", "session_id": "demo_call_12345", "user_id": null, "event_type": "call_hangup", "call_status": "ended", "caller_number": "+1-555-123-4567", "correlation_id": "9bfa1f5f-28b5-441a-9714-726acc88becd"}
{"asctime": "2025-07-25 15:46:08,549", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_hangup", "session_id": "demo_call_12345", "user_id": null, "correlation_id": "9bfa1f5f-28b5-441a-9714-726acc88becd"}
{"asctime": "2025-07-25 15:46:08,549", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_hangup", "session_id": "demo_call_12345", "user_id": null, "correlation_id": "9bfa1f5f-28b5-441a-9714-726acc88becd"}
{"asctime": "2025-07-25 15:46:08,549", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "AI call ended", "session_id": "demo_call_12345", "user_id": null, "correlation_id": "9bfa1f5f-28b5-441a-9714-726acc88becd"}
{"asctime": "2025-07-25 15:46:08,550", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: demo_call_12345 -> ended", "session_id": "demo_call_12345", "user_id": null, "new_status": "ended", "correlation_id": "9bfa1f5f-28b5-441a-9714-726acc88becd"}
{"asctime": "2025-07-25 15:46:08,550", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session ended: demo_call_12345", "session_id": "demo_call_12345", "user_id": null, "call_duration": 210, "session_duration": 5.022005, "correlation_id": "9bfa1f5f-28b5-441a-9714-726acc88becd"}
{"asctime": "2025-07-25 15:49:54,453", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: test_001", "session_id": "test_001", "user_id": null, "final_status": "active", "duration": 350.876531, "correlation_id": null}
{"asctime": "2025-07-25 15:49:54,454", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: test_call_001", "session_id": "test_call_001", "user_id": null, "final_status": "ended", "duration": 334.307613, "correlation_id": null}
{"asctime": "2025-07-25 15:49:54,454", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: test_integration_001", "session_id": "test_integration_001", "user_id": null, "final_status": "active", "duration": 333.013605, "correlation_id": null}
{"asctime": "2025-07-25 15:49:54,454", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: concurrent_test_000", "session_id": "concurrent_test_000", "user_id": null, "final_status": "active", "duration": 330.887786, "correlation_id": null}
{"asctime": "2025-07-25 15:49:54,454", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: concurrent_test_001", "session_id": "concurrent_test_001", "user_id": null, "final_status": "active", "duration": 330.886208, "correlation_id": null}
{"asctime": "2025-07-25 15:49:54,454", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: concurrent_test_002", "session_id": "concurrent_test_002", "user_id": null, "final_status": "active", "duration": 330.884588, "correlation_id": null}
{"asctime": "2025-07-25 15:49:54,455", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: concurrent_test_003", "session_id": "concurrent_test_003", "user_id": null, "final_status": "active", "duration": 330.883082, "correlation_id": null}
{"asctime": "2025-07-25 15:49:54,455", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: concurrent_test_004", "session_id": "concurrent_test_004", "user_id": null, "final_status": "active", "duration": 330.881983, "correlation_id": null}
{"asctime": "2025-07-25 15:49:54,455", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Cleaned up 8 expired sessions", "session_id": null, "user_id": null, "expired_session_count": 8, "correlation_id": null}
{"asctime": "2025-07-25 15:50:55,353", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: large_test_001", "session_id": "large_test_001", "user_id": null, "final_status": "active", "duration": 346.477252, "correlation_id": null}
{"asctime": "2025-07-25 15:50:55,354", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: special_test_001", "session_id": "special_test_001", "user_id": null, "final_status": "active", "duration": 346.469817, "correlation_id": null}
{"asctime": "2025-07-25 15:50:55,354", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: complete_flow_001", "session_id": "complete_flow_001", "user_id": null, "final_status": "ended", "duration": 346.464245, "correlation_id": null}
{"asctime": "2025-07-25 15:50:55,354", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: rapid_test_000", "session_id": "rapid_test_000", "user_id": null, "final_status": "active", "duration": 346.448163, "correlation_id": null}
{"asctime": "2025-07-25 15:50:55,354", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: rapid_test_001", "session_id": "rapid_test_001", "user_id": null, "final_status": "active", "duration": 346.438773, "correlation_id": null}
{"asctime": "2025-07-25 15:50:55,354", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: rapid_test_002", "session_id": "rapid_test_002", "user_id": null, "final_status": "active", "duration": 346.435422, "correlation_id": null}
{"asctime": "2025-07-25 15:50:55,354", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: rapid_test_003", "session_id": "rapid_test_003", "user_id": null, "final_status": "active", "duration": 346.432965, "correlation_id": null}
{"asctime": "2025-07-25 15:50:55,354", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: rapid_test_004", "session_id": "rapid_test_004", "user_id": null, "final_status": "active", "duration": 346.430897, "correlation_id": null}
{"asctime": "2025-07-25 15:50:55,355", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: rapid_test_005", "session_id": "rapid_test_005", "user_id": null, "final_status": "active", "duration": 346.429749, "correlation_id": null}
{"asctime": "2025-07-25 15:50:55,355", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: rapid_test_006", "session_id": "rapid_test_006", "user_id": null, "final_status": "active", "duration": 346.428253, "correlation_id": null}
{"asctime": "2025-07-25 15:50:55,355", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: rapid_test_007", "session_id": "rapid_test_007", "user_id": null, "final_status": "active", "duration": 346.426447, "correlation_id": null}
{"asctime": "2025-07-25 15:50:55,355", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: rapid_test_008", "session_id": "rapid_test_008", "user_id": null, "final_status": "active", "duration": 346.425356, "correlation_id": null}
{"asctime": "2025-07-25 15:50:55,355", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: rapid_test_009", "session_id": "rapid_test_009", "user_id": null, "final_status": "active", "duration": 346.423445, "correlation_id": null}
{"asctime": "2025-07-25 15:50:55,355", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Cleaned up 13 expired sessions", "session_id": null, "user_id": null, "expired_session_count": 13, "correlation_id": null}
{"asctime": "2025-07-25 15:51:56,826", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: demo_call_12345", "session_id": "demo_call_12345", "user_id": null, "final_status": "ended", "duration": 353.298509, "correlation_id": null}
{"asctime": "2025-07-25 15:51:56,827", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Cleaned up 1 expired sessions", "session_id": null, "user_id": null, "expired_session_count": 1, "correlation_id": null}
{"asctime": "2025-07-25 15:57:14,064", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "complete_test_6a040ab7", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "8df2db0a-f73a-4d64-b354-21d8c39db655"}
{"asctime": "2025-07-25 15:57:14,065", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "complete_test_6a040ab7", "user_id": null, "event_type": "call_received", "call_status": "active", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "8df2db0a-f73a-4d64-b354-21d8c39db655"}
{"asctime": "2025-07-25 15:57:14,065", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "complete_test_6a040ab7", "user_id": null, "correlation_id": "8df2db0a-f73a-4d64-b354-21d8c39db655"}
{"asctime": "2025-07-25 15:57:14,065", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "complete_test_6a040ab7", "user_id": null, "correlation_id": "8df2db0a-f73a-4d64-b354-21d8c39db655"}
{"asctime": "2025-07-25 15:57:14,066", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: complete_test_6a040ab7", "session_id": "complete_test_6a040ab7", "user_id": null, "caller_number": "+1-555-VOICE-TEST", "called_number": "+1-800-AI-HELP", "active_sessions": 1, "correlation_id": "8df2db0a-f73a-4d64-b354-21d8c39db655"}
{"asctime": "2025-07-25 15:57:14,066", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "complete_test_6a040ab7", "user_id": "complete_test_6a040ab7", "correlation_id": "8df2db0a-f73a-4d64-b354-21d8c39db655"}
{"asctime": "2025-07-25 15:57:14,066", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: complete_test_6a040ab7 -> active", "session_id": "complete_test_6a040ab7", "user_id": null, "new_status": "active", "correlation_id": "8df2db0a-f73a-4d64-b354-21d8c39db655"}
{"asctime": "2025-07-25 15:57:14,122", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "complete_test_6a040ab7", "user_id": null, "event_type": "call_answered_ivr", "call_type": "inbound", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "0274e09a-4a95-471b-a7fe-986dc9b9fde8"}
{"asctime": "2025-07-25 15:57:14,122", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_answered_ivr", "session_id": "complete_test_6a040ab7", "user_id": null, "event_type": "call_answered_ivr", "call_status": "active", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "0274e09a-4a95-471b-a7fe-986dc9b9fde8"}
{"asctime": "2025-07-25 15:57:14,122", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_answered_ivr", "session_id": "complete_test_6a040ab7", "user_id": null, "correlation_id": "0274e09a-4a95-471b-a7fe-986dc9b9fde8"}
{"asctime": "2025-07-25 15:57:14,123", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_answered_ivr", "session_id": "complete_test_6a040ab7", "user_id": null, "correlation_id": "0274e09a-4a95-471b-a7fe-986dc9b9fde8"}
{"asctime": "2025-07-25 15:57:14,123", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "AI call started", "session_id": "complete_test_6a040ab7", "user_id": null, "correlation_id": "0274e09a-4a95-471b-a7fe-986dc9b9fde8"}
{"asctime": "2025-07-25 15:57:14,123", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: complete_test_6a040ab7 -> active", "session_id": "complete_test_6a040ab7", "user_id": null, "new_status": "active", "correlation_id": "0274e09a-4a95-471b-a7fe-986dc9b9fde8"}
{"asctime": "2025-07-25 15:57:14,123", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Text input sent to AI", "session_id": "complete_test_6a040ab7", "user_id": null, "text_length": 55, "correlation_id": "0274e09a-4a95-471b-a7fe-986dc9b9fde8"}
{"asctime": "2025-07-25 15:57:32,185", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "complete_test_6a040ab7", "user_id": null, "event_type": "dtmf_received", "call_type": "inbound", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "41d02062-baa4-4475-9b55-c6abb896cbac"}
{"asctime": "2025-07-25 15:57:32,185", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: dtmf_received", "session_id": "complete_test_6a040ab7", "user_id": null, "event_type": "dtmf_received", "call_status": "active", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "41d02062-baa4-4475-9b55-c6abb896cbac"}
{"asctime": "2025-07-25 15:57:32,185", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: dtmf_received", "session_id": "complete_test_6a040ab7", "user_id": null, "correlation_id": "41d02062-baa4-4475-9b55-c6abb896cbac"}
{"asctime": "2025-07-25 15:57:32,186", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: dtmf_received", "session_id": "complete_test_6a040ab7", "user_id": null, "correlation_id": "41d02062-baa4-4475-9b55-c6abb896cbac"}
{"asctime": "2025-07-25 15:57:32,186", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Text input sent to AI", "session_id": "complete_test_6a040ab7", "user_id": null, "text_length": 31, "correlation_id": "41d02062-baa4-4475-9b55-c6abb896cbac"}
{"asctime": "2025-07-25 15:57:32,186", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "DTMF input sent to AI", "session_id": "complete_test_6a040ab7", "user_id": null, "dtmf_digits": "1234*", "correlation_id": "41d02062-baa4-4475-9b55-c6abb896cbac"}
{"asctime": "2025-07-25 15:57:37,057", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "complete_test_6a040ab7", "user_id": null, "event_type": "agent_status_change", "call_type": "inbound", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "31b12f75-1bf4-4219-9eba-fd6b6abe8813"}
{"asctime": "2025-07-25 15:57:37,057", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: agent_status_change", "session_id": "complete_test_6a040ab7", "user_id": null, "event_type": "agent_status_change", "call_status": "answered", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "31b12f75-1bf4-4219-9eba-fd6b6abe8813"}
{"asctime": "2025-07-25 15:57:37,057", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: agent_status_change", "session_id": "complete_test_6a040ab7", "user_id": null, "correlation_id": "31b12f75-1bf4-4219-9eba-fd6b6abe8813"}
{"asctime": "2025-07-25 15:57:37,058", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: agent_status_change", "session_id": "complete_test_6a040ab7", "user_id": null, "correlation_id": "31b12f75-1bf4-4219-9eba-fd6b6abe8813"}
{"asctime": "2025-07-25 15:57:41,715", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "complete_test_6a040ab7", "user_id": null, "event_type": "call_hangup", "call_type": "inbound", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "133b1124-4eba-4950-bc47-c18eec1b5707"}
{"asctime": "2025-07-25 15:57:41,715", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_hangup", "session_id": "complete_test_6a040ab7", "user_id": null, "event_type": "call_hangup", "call_status": "ended", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "133b1124-4eba-4950-bc47-c18eec1b5707"}
{"asctime": "2025-07-25 15:57:41,715", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_hangup", "session_id": "complete_test_6a040ab7", "user_id": null, "correlation_id": "133b1124-4eba-4950-bc47-c18eec1b5707"}
{"asctime": "2025-07-25 15:57:41,716", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_hangup", "session_id": "complete_test_6a040ab7", "user_id": null, "correlation_id": "133b1124-4eba-4950-bc47-c18eec1b5707"}
{"asctime": "2025-07-25 15:57:41,716", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "AI call ended", "session_id": "complete_test_6a040ab7", "user_id": null, "correlation_id": "133b1124-4eba-4950-bc47-c18eec1b5707"}
{"asctime": "2025-07-25 15:57:41,716", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: complete_test_6a040ab7 -> ended", "session_id": "complete_test_6a040ab7", "user_id": null, "new_status": "ended", "correlation_id": "133b1124-4eba-4950-bc47-c18eec1b5707"}
{"asctime": "2025-07-25 15:57:41,716", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session ended: complete_test_6a040ab7", "session_id": "complete_test_6a040ab7", "user_id": null, "call_duration": 27, "session_duration": 27.650456, "correlation_id": "133b1124-4eba-4950-bc47-c18eec1b5707"}
{"asctime": "2025-07-25 15:57:43,767", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "quality_test_b5d293e7", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "b2fe7ad3-19d7-4cea-881f-bd2e96fcc4b5"}
{"asctime": "2025-07-25 15:57:43,768", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "quality_test_b5d293e7", "user_id": null, "event_type": "call_received", "call_status": "active", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "b2fe7ad3-19d7-4cea-881f-bd2e96fcc4b5"}
{"asctime": "2025-07-25 15:57:43,768", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "quality_test_b5d293e7", "user_id": null, "correlation_id": "b2fe7ad3-19d7-4cea-881f-bd2e96fcc4b5"}
{"asctime": "2025-07-25 15:57:43,768", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "quality_test_b5d293e7", "user_id": null, "correlation_id": "b2fe7ad3-19d7-4cea-881f-bd2e96fcc4b5"}
{"asctime": "2025-07-25 15:57:43,768", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: quality_test_b5d293e7", "session_id": "quality_test_b5d293e7", "user_id": null, "caller_number": "+1-555-VOICE-TEST", "called_number": "+1-800-AI-HELP", "active_sessions": 1, "correlation_id": "b2fe7ad3-19d7-4cea-881f-bd2e96fcc4b5"}
{"asctime": "2025-07-25 15:57:43,769", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "quality_test_b5d293e7", "user_id": "quality_test_b5d293e7", "correlation_id": "b2fe7ad3-19d7-4cea-881f-bd2e96fcc4b5"}
{"asctime": "2025-07-25 15:57:43,769", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: quality_test_b5d293e7 -> active", "session_id": "quality_test_b5d293e7", "user_id": null, "new_status": "active", "correlation_id": "b2fe7ad3-19d7-4cea-881f-bd2e96fcc4b5"}
{"asctime": "2025-07-25 15:57:43,795", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "quality_test_b5d293e7", "user_id": null, "event_type": "call_answered_ivr", "call_type": "inbound", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "8cc1d084-a2a1-4b64-8b0a-c8041596284f"}
{"asctime": "2025-07-25 15:57:43,795", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_answered_ivr", "session_id": "quality_test_b5d293e7", "user_id": null, "event_type": "call_answered_ivr", "call_status": "active", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "8cc1d084-a2a1-4b64-8b0a-c8041596284f"}
{"asctime": "2025-07-25 15:57:43,795", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_answered_ivr", "session_id": "quality_test_b5d293e7", "user_id": null, "correlation_id": "8cc1d084-a2a1-4b64-8b0a-c8041596284f"}
{"asctime": "2025-07-25 15:57:43,795", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_answered_ivr", "session_id": "quality_test_b5d293e7", "user_id": null, "correlation_id": "8cc1d084-a2a1-4b64-8b0a-c8041596284f"}
{"asctime": "2025-07-25 15:57:43,795", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "AI call started", "session_id": "quality_test_b5d293e7", "user_id": null, "correlation_id": "8cc1d084-a2a1-4b64-8b0a-c8041596284f"}
{"asctime": "2025-07-25 15:57:43,796", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: quality_test_b5d293e7 -> active", "session_id": "quality_test_b5d293e7", "user_id": null, "new_status": "active", "correlation_id": "8cc1d084-a2a1-4b64-8b0a-c8041596284f"}
{"asctime": "2025-07-25 15:57:43,796", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Text input sent to AI", "session_id": "quality_test_b5d293e7", "user_id": null, "text_length": 55, "correlation_id": "8cc1d084-a2a1-4b64-8b0a-c8041596284f"}
{"asctime": "2025-07-25 15:58:09,401", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "quality_test_b5d293e7", "user_id": null, "event_type": "call_hangup", "call_type": "inbound", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "2b670c1f-4f7a-45c1-8bd9-2fc9763178ac"}
{"asctime": "2025-07-25 15:58:09,402", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_hangup", "session_id": "quality_test_b5d293e7", "user_id": null, "event_type": "call_hangup", "call_status": "active", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "2b670c1f-4f7a-45c1-8bd9-2fc9763178ac"}
{"asctime": "2025-07-25 15:58:09,402", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_hangup", "session_id": "quality_test_b5d293e7", "user_id": null, "correlation_id": "2b670c1f-4f7a-45c1-8bd9-2fc9763178ac"}
{"asctime": "2025-07-25 15:58:09,402", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_hangup", "session_id": "quality_test_b5d293e7", "user_id": null, "correlation_id": "2b670c1f-4f7a-45c1-8bd9-2fc9763178ac"}
{"asctime": "2025-07-25 15:58:09,402", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "AI call ended", "session_id": "quality_test_b5d293e7", "user_id": null, "correlation_id": "2b670c1f-4f7a-45c1-8bd9-2fc9763178ac"}
{"asctime": "2025-07-25 15:58:09,402", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: quality_test_b5d293e7 -> ended", "session_id": "quality_test_b5d293e7", "user_id": null, "new_status": "ended", "correlation_id": "2b670c1f-4f7a-45c1-8bd9-2fc9763178ac"}
{"asctime": "2025-07-25 15:58:09,402", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session ended: quality_test_b5d293e7", "session_id": "quality_test_b5d293e7", "user_id": null, "call_duration": 30, "session_duration": 25.634156, "correlation_id": "2b670c1f-4f7a-45c1-8bd9-2fc9763178ac"}
{"asctime": "2025-07-25 15:58:11,490", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "concurrent_test_00_e1fa08", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "fb0cec2a-4056-494b-b6b1-93d1c5580e30"}
{"asctime": "2025-07-25 15:58:11,490", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "concurrent_test_00_e1fa08", "user_id": null, "event_type": "call_received", "call_status": "active", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "fb0cec2a-4056-494b-b6b1-93d1c5580e30"}
{"asctime": "2025-07-25 15:58:11,490", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "concurrent_test_00_e1fa08", "user_id": null, "correlation_id": "fb0cec2a-4056-494b-b6b1-93d1c5580e30"}
{"asctime": "2025-07-25 15:58:11,490", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "concurrent_test_00_e1fa08", "user_id": null, "correlation_id": "fb0cec2a-4056-494b-b6b1-93d1c5580e30"}
{"asctime": "2025-07-25 15:58:11,491", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: concurrent_test_00_e1fa08", "session_id": "concurrent_test_00_e1fa08", "user_id": null, "caller_number": "+1-555-VOICE-TEST", "called_number": "+1-800-AI-HELP", "active_sessions": 1, "correlation_id": "fb0cec2a-4056-494b-b6b1-93d1c5580e30"}
{"asctime": "2025-07-25 15:58:11,491", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "concurrent_test_00_e1fa08", "user_id": "concurrent_test_00_e1fa08", "correlation_id": "fb0cec2a-4056-494b-b6b1-93d1c5580e30"}
{"asctime": "2025-07-25 15:58:11,491", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: concurrent_test_00_e1fa08 -> active", "session_id": "concurrent_test_00_e1fa08", "user_id": null, "new_status": "active", "correlation_id": "fb0cec2a-4056-494b-b6b1-93d1c5580e30"}
{"asctime": "2025-07-25 15:58:11,492", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "concurrent_test_01_e2d0c3", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "45efe6c4-9a19-4797-9880-dcf554b88724"}
{"asctime": "2025-07-25 15:58:11,492", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "concurrent_test_01_e2d0c3", "user_id": null, "event_type": "call_received", "call_status": "active", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "45efe6c4-9a19-4797-9880-dcf554b88724"}
{"asctime": "2025-07-25 15:58:11,492", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "concurrent_test_01_e2d0c3", "user_id": null, "correlation_id": "45efe6c4-9a19-4797-9880-dcf554b88724"}
{"asctime": "2025-07-25 15:58:11,492", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "concurrent_test_01_e2d0c3", "user_id": null, "correlation_id": "45efe6c4-9a19-4797-9880-dcf554b88724"}
{"asctime": "2025-07-25 15:58:11,492", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: concurrent_test_01_e2d0c3", "session_id": "concurrent_test_01_e2d0c3", "user_id": null, "caller_number": "+1-555-VOICE-TEST", "called_number": "+1-800-AI-HELP", "active_sessions": 2, "correlation_id": "45efe6c4-9a19-4797-9880-dcf554b88724"}
{"asctime": "2025-07-25 15:58:11,493", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "concurrent_test_01_e2d0c3", "user_id": "concurrent_test_01_e2d0c3", "correlation_id": "45efe6c4-9a19-4797-9880-dcf554b88724"}
{"asctime": "2025-07-25 15:58:11,493", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: concurrent_test_01_e2d0c3 -> active", "session_id": "concurrent_test_01_e2d0c3", "user_id": null, "new_status": "active", "correlation_id": "45efe6c4-9a19-4797-9880-dcf554b88724"}
{"asctime": "2025-07-25 15:58:11,494", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "concurrent_test_02_15488f", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "25c92a20-d396-4525-bbd0-f62a0ba95e9d"}
{"asctime": "2025-07-25 15:58:11,494", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "concurrent_test_02_15488f", "user_id": null, "event_type": "call_received", "call_status": "active", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "25c92a20-d396-4525-bbd0-f62a0ba95e9d"}
{"asctime": "2025-07-25 15:58:11,494", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "concurrent_test_02_15488f", "user_id": null, "correlation_id": "25c92a20-d396-4525-bbd0-f62a0ba95e9d"}
{"asctime": "2025-07-25 15:58:11,494", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "concurrent_test_02_15488f", "user_id": null, "correlation_id": "25c92a20-d396-4525-bbd0-f62a0ba95e9d"}
{"asctime": "2025-07-25 15:58:11,494", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: concurrent_test_02_15488f", "session_id": "concurrent_test_02_15488f", "user_id": null, "caller_number": "+1-555-VOICE-TEST", "called_number": "+1-800-AI-HELP", "active_sessions": 3, "correlation_id": "25c92a20-d396-4525-bbd0-f62a0ba95e9d"}
{"asctime": "2025-07-25 15:58:11,495", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "concurrent_test_02_15488f", "user_id": "concurrent_test_02_15488f", "correlation_id": "25c92a20-d396-4525-bbd0-f62a0ba95e9d"}
{"asctime": "2025-07-25 15:58:11,495", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: concurrent_test_02_15488f -> active", "session_id": "concurrent_test_02_15488f", "user_id": null, "new_status": "active", "correlation_id": "25c92a20-d396-4525-bbd0-f62a0ba95e9d"}
{"asctime": "2025-07-25 15:58:15,780", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "concurrent_test_01_e2d0c3", "user_id": null, "event_type": "call_hangup", "call_type": "inbound", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "248a6607-e4dc-45cc-9b9a-6036699edc9e"}
{"asctime": "2025-07-25 15:58:15,781", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_hangup", "session_id": "concurrent_test_01_e2d0c3", "user_id": null, "event_type": "call_hangup", "call_status": "active", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "248a6607-e4dc-45cc-9b9a-6036699edc9e"}
{"asctime": "2025-07-25 15:58:15,781", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_hangup", "session_id": "concurrent_test_01_e2d0c3", "user_id": null, "correlation_id": "248a6607-e4dc-45cc-9b9a-6036699edc9e"}
{"asctime": "2025-07-25 15:58:15,781", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_hangup", "session_id": "concurrent_test_01_e2d0c3", "user_id": null, "correlation_id": "248a6607-e4dc-45cc-9b9a-6036699edc9e"}
{"asctime": "2025-07-25 15:58:15,781", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "AI call ended", "session_id": "concurrent_test_01_e2d0c3", "user_id": null, "correlation_id": "248a6607-e4dc-45cc-9b9a-6036699edc9e"}
{"asctime": "2025-07-25 15:58:15,782", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: concurrent_test_01_e2d0c3 -> ended", "session_id": "concurrent_test_01_e2d0c3", "user_id": null, "new_status": "ended", "correlation_id": "248a6607-e4dc-45cc-9b9a-6036699edc9e"}
{"asctime": "2025-07-25 15:58:15,782", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session ended: concurrent_test_01_e2d0c3", "session_id": "concurrent_test_01_e2d0c3", "user_id": null, "call_duration": 10, "session_duration": 4.289477, "correlation_id": "248a6607-e4dc-45cc-9b9a-6036699edc9e"}
{"asctime": "2025-07-25 15:58:15,783", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "concurrent_test_00_e1fa08", "user_id": null, "event_type": "call_hangup", "call_type": "inbound", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "0376d2d8-2a14-42a5-aefd-833262e3cd88"}
{"asctime": "2025-07-25 15:58:15,783", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_hangup", "session_id": "concurrent_test_00_e1fa08", "user_id": null, "event_type": "call_hangup", "call_status": "active", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "0376d2d8-2a14-42a5-aefd-833262e3cd88"}
{"asctime": "2025-07-25 15:58:15,783", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_hangup", "session_id": "concurrent_test_00_e1fa08", "user_id": null, "correlation_id": "0376d2d8-2a14-42a5-aefd-833262e3cd88"}
{"asctime": "2025-07-25 15:58:15,783", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_hangup", "session_id": "concurrent_test_00_e1fa08", "user_id": null, "correlation_id": "0376d2d8-2a14-42a5-aefd-833262e3cd88"}
{"asctime": "2025-07-25 15:58:15,784", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "AI call ended", "session_id": "concurrent_test_00_e1fa08", "user_id": null, "correlation_id": "0376d2d8-2a14-42a5-aefd-833262e3cd88"}
{"asctime": "2025-07-25 15:58:15,784", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: concurrent_test_00_e1fa08 -> ended", "session_id": "concurrent_test_00_e1fa08", "user_id": null, "new_status": "ended", "correlation_id": "0376d2d8-2a14-42a5-aefd-833262e3cd88"}
{"asctime": "2025-07-25 15:58:15,784", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session ended: concurrent_test_00_e1fa08", "session_id": "concurrent_test_00_e1fa08", "user_id": null, "call_duration": 10, "session_duration": 4.293389, "correlation_id": "0376d2d8-2a14-42a5-aefd-833262e3cd88"}
{"asctime": "2025-07-25 15:58:15,825", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "concurrent_test_02_15488f", "user_id": null, "event_type": "call_hangup", "call_type": "inbound", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "d63ccb9b-e321-42b4-b990-5e7430429cb9"}
{"asctime": "2025-07-25 15:58:15,825", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_hangup", "session_id": "concurrent_test_02_15488f", "user_id": null, "event_type": "call_hangup", "call_status": "active", "caller_number": "+1-555-VOICE-TEST", "correlation_id": "d63ccb9b-e321-42b4-b990-5e7430429cb9"}
{"asctime": "2025-07-25 15:58:15,825", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_hangup", "session_id": "concurrent_test_02_15488f", "user_id": null, "correlation_id": "d63ccb9b-e321-42b4-b990-5e7430429cb9"}
{"asctime": "2025-07-25 15:58:15,826", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_hangup", "session_id": "concurrent_test_02_15488f", "user_id": null, "correlation_id": "d63ccb9b-e321-42b4-b990-5e7430429cb9"}
{"asctime": "2025-07-25 15:58:15,826", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "AI call ended", "session_id": "concurrent_test_02_15488f", "user_id": null, "correlation_id": "d63ccb9b-e321-42b4-b990-5e7430429cb9"}
{"asctime": "2025-07-25 15:58:15,826", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: concurrent_test_02_15488f -> ended", "session_id": "concurrent_test_02_15488f", "user_id": null, "new_status": "ended", "correlation_id": "d63ccb9b-e321-42b4-b990-5e7430429cb9"}
{"asctime": "2025-07-25 15:58:15,826", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session ended: concurrent_test_02_15488f", "session_id": "concurrent_test_02_15488f", "user_id": null, "call_duration": 10, "session_duration": 4.332041, "correlation_id": "d63ccb9b-e321-42b4-b990-5e7430429cb9"}
{"asctime": "2025-07-25 16:03:17,276", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: complete_test_6a040ab7", "session_id": "complete_test_6a040ab7", "user_id": null, "final_status": "ended", "duration": 363.210818, "correlation_id": null}
{"asctime": "2025-07-25 16:03:17,277", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: quality_test_b5d293e7", "session_id": "quality_test_b5d293e7", "user_id": null, "final_status": "ended", "duration": 333.508651, "correlation_id": null}
{"asctime": "2025-07-25 16:03:17,277", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: concurrent_test_00_e1fa08", "session_id": "concurrent_test_00_e1fa08", "user_id": null, "final_status": "ended", "duration": 305.786645, "correlation_id": null}
{"asctime": "2025-07-25 16:03:17,277", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: concurrent_test_01_e2d0c3", "session_id": "concurrent_test_01_e2d0c3", "user_id": null, "final_status": "ended", "duration": 305.785214, "correlation_id": null}
{"asctime": "2025-07-25 16:03:17,278", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: concurrent_test_02_15488f", "session_id": "concurrent_test_02_15488f", "user_id": null, "final_status": "ended", "duration": 305.783248, "correlation_id": null}
{"asctime": "2025-07-25 16:03:17,278", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Cleaned up 5 expired sessions", "session_id": null, "user_id": null, "expired_session_count": 5, "correlation_id": null}
{"asctime": "2025-07-25 16:06:39,613", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "WARNING", "message": "Connection closed by AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:06:39,614", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 1/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:06:44,615", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 16:06:44,616", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "ERROR", "message": "Reconnection attempt 1 failed: Failed to connect to AI Voice Mate: [Errno 111] Connection refused", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:06:44,616", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 2/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:06:55,929", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 16:06:55,930", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "ERROR", "message": "Reconnection attempt 2 failed: Failed to connect to AI Voice Mate: [Errno 111] Connection refused", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:06:55,931", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 3/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:07:10,931", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 16:07:10,933", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 16:25:34,300", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 16:25:34,320", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 16:25:34,321", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session cleanup task cancelled", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:25:34,321", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session manager shutdown completed", "session_id": null, "user_id": null, "ended_sessions": 0, "correlation_id": null}
{"asctime": "2025-07-25 16:25:34,326", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "WARNING", "message": "Connection closed by AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:25:34,327", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 1/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:25:34,333", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Disconnected from AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:26:54,169", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 16:26:54,184", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 16:26:54,185", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session cleanup task cancelled", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:26:54,186", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session manager shutdown completed", "session_id": null, "user_id": null, "ended_sessions": 0, "correlation_id": null}
{"asctime": "2025-07-25 16:26:54,186", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "WARNING", "message": "Connection closed by AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:26:54,186", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 1/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:26:54,187", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Disconnected from AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:28:00,045", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 16:28:00,052", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 16:28:00,053", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session cleanup task cancelled", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:28:00,054", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session manager shutdown completed", "session_id": null, "user_id": null, "ended_sessions": 0, "correlation_id": null}
{"asctime": "2025-07-25 16:28:00,054", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "WARNING", "message": "Connection closed by AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:28:00,054", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 1/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:28:00,054", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Disconnected from AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:34:58,526", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 16:34:58,535", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 16:34:58,536", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session cleanup task cancelled", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:34:58,537", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session manager shutdown completed", "session_id": null, "user_id": null, "ended_sessions": 0, "correlation_id": null}
{"asctime": "2025-07-25 16:34:58,538", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "WARNING", "message": "Connection closed by AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:34:58,538", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 1/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:34:58,538", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Disconnected from AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:45:30,825", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session cleanup task cancelled", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:45:30,827", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session manager shutdown completed", "session_id": null, "user_id": null, "ended_sessions": 0, "correlation_id": null}
{"asctime": "2025-07-25 16:45:30,831", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "WARNING", "message": "Connection closed by AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:45:30,831", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 1/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:45:30,831", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Disconnected from AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 16:45:47,199", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 16:45:47,207", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 16:45:49,740", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "9590d6b3-0e97-4f1d-b32e-692470e46f65"}
{"asctime": "2025-07-25 16:45:49,740", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567890", "correlation_id": "9590d6b3-0e97-4f1d-b32e-692470e46f65"}
{"asctime": "2025-07-25 16:45:49,740", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "9590d6b3-0e97-4f1d-b32e-692470e46f65"}
{"asctime": "2025-07-25 16:45:49,741", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "9590d6b3-0e97-4f1d-b32e-692470e46f65"}
{"asctime": "2025-07-25 16:45:49,741", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "caller_number": "+1234567890", "called_number": "+0987654321", "active_sessions": 1, "correlation_id": "9590d6b3-0e97-4f1d-b32e-692470e46f65"}
{"asctime": "2025-07-25 16:45:49,741", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "test_startup_001", "user_id": "test_startup_001", "correlation_id": "9590d6b3-0e97-4f1d-b32e-692470e46f65"}
{"asctime": "2025-07-25 16:45:49,742", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: test_startup_001 -> active", "session_id": "test_startup_001", "user_id": null, "new_status": "active", "correlation_id": "9590d6b3-0e97-4f1d-b32e-692470e46f65"}
{"asctime": "2025-07-25 16:51:00,895", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "final_status": "active", "duration": 311.154409, "correlation_id": null}
{"asctime": "2025-07-25 16:51:00,897", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Cleaned up 1 expired sessions", "session_id": null, "user_id": null, "expired_session_count": 1, "correlation_id": null}
{"asctime": "2025-07-25 17:11:40,372", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session cleanup task cancelled", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 17:11:40,373", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session manager shutdown completed", "session_id": null, "user_id": null, "ended_sessions": 0, "correlation_id": null}
{"asctime": "2025-07-25 17:11:40,374", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "WARNING", "message": "Connection closed by AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 17:11:40,374", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 1/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 17:11:40,374", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Disconnected from AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 17:12:24,644", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 17:12:24,651", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 17:12:27,223", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "ea88ef1c-c12c-4e86-88c5-1971930c30f3"}
{"asctime": "2025-07-25 17:12:27,223", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567890", "correlation_id": "ea88ef1c-c12c-4e86-88c5-1971930c30f3"}
{"asctime": "2025-07-25 17:12:27,223", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "ea88ef1c-c12c-4e86-88c5-1971930c30f3"}
{"asctime": "2025-07-25 17:12:27,223", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "ea88ef1c-c12c-4e86-88c5-1971930c30f3"}
{"asctime": "2025-07-25 17:12:27,224", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "caller_number": "+1234567890", "called_number": "+0987654321", "active_sessions": 1, "correlation_id": "ea88ef1c-c12c-4e86-88c5-1971930c30f3"}
{"asctime": "2025-07-25 17:12:27,224", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "test_startup_001", "user_id": "test_startup_001", "correlation_id": "ea88ef1c-c12c-4e86-88c5-1971930c30f3"}
{"asctime": "2025-07-25 17:12:27,224", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: test_startup_001 -> active", "session_id": "test_startup_001", "user_id": null, "new_status": "active", "correlation_id": "ea88ef1c-c12c-4e86-88c5-1971930c30f3"}
{"asctime": "2025-07-25 17:17:39,142", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "final_status": "active", "duration": 311.918421, "correlation_id": null}
{"asctime": "2025-07-25 17:17:39,143", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Cleaned up 1 expired sessions", "session_id": null, "user_id": null, "expired_session_count": 1, "correlation_id": null}
{"asctime": "2025-07-25 17:21:55,923", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "7ec88302-33b6-4b4f-8605-6c53e434591a"}
{"asctime": "2025-07-25 17:21:55,924", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "test_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567890", "correlation_id": "7ec88302-33b6-4b4f-8605-6c53e434591a"}
{"asctime": "2025-07-25 17:21:55,924", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "test_001", "user_id": null, "correlation_id": "7ec88302-33b6-4b4f-8605-6c53e434591a"}
{"asctime": "2025-07-25 17:21:55,925", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "test_001", "user_id": null, "correlation_id": "7ec88302-33b6-4b4f-8605-6c53e434591a"}
{"asctime": "2025-07-25 17:21:55,925", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: test_001", "session_id": "test_001", "user_id": null, "caller_number": "+1234567890", "called_number": "+0987654321", "active_sessions": 1, "correlation_id": "7ec88302-33b6-4b4f-8605-6c53e434591a"}
{"asctime": "2025-07-25 17:21:55,925", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "test_001", "user_id": "test_001", "correlation_id": "7ec88302-33b6-4b4f-8605-6c53e434591a"}
{"asctime": "2025-07-25 17:21:55,925", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: test_001 -> active", "session_id": "test_001", "user_id": null, "new_status": "active", "correlation_id": "7ec88302-33b6-4b4f-8605-6c53e434591a"}
{"asctime": "2025-07-25 17:27:07,184", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: test_001", "session_id": "test_001", "user_id": null, "final_status": "active", "duration": 311.259588, "correlation_id": null}
{"asctime": "2025-07-25 17:27:07,185", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Cleaned up 1 expired sessions", "session_id": null, "user_id": null, "expired_session_count": 1, "correlation_id": null}
{"asctime": "2025-07-25 17:32:24,429", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "c6a24dcc-38da-4d6b-8a6e-bc00cfaa82c8"}
{"asctime": "2025-07-25 17:32:24,429", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "test_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567890", "correlation_id": "c6a24dcc-38da-4d6b-8a6e-bc00cfaa82c8"}
{"asctime": "2025-07-25 17:32:24,429", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "test_001", "user_id": null, "correlation_id": "c6a24dcc-38da-4d6b-8a6e-bc00cfaa82c8"}
{"asctime": "2025-07-25 17:32:24,429", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "test_001", "user_id": null, "correlation_id": "c6a24dcc-38da-4d6b-8a6e-bc00cfaa82c8"}
{"asctime": "2025-07-25 17:32:24,429", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: test_001", "session_id": "test_001", "user_id": null, "caller_number": "+1234567890", "called_number": "+0987654321", "active_sessions": 1, "correlation_id": "c6a24dcc-38da-4d6b-8a6e-bc00cfaa82c8"}
{"asctime": "2025-07-25 17:32:24,430", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "test_001", "user_id": "test_001", "correlation_id": "c6a24dcc-38da-4d6b-8a6e-bc00cfaa82c8"}
{"asctime": "2025-07-25 17:32:24,430", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: test_001 -> active", "session_id": "test_001", "user_id": null, "new_status": "active", "correlation_id": "c6a24dcc-38da-4d6b-8a6e-bc00cfaa82c8"}
{"asctime": "2025-07-25 17:37:36,947", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: test_001", "session_id": "test_001", "user_id": null, "final_status": "active", "duration": 312.517987, "correlation_id": null}
{"asctime": "2025-07-25 17:37:36,949", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Cleaned up 1 expired sessions", "session_id": null, "user_id": null, "expired_session_count": 1, "correlation_id": null}
{"asctime": "2025-07-25 21:30:36,915", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "WARNING", "message": "Connection closed by AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 21:30:36,925", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 1/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 21:30:43,632", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 21:30:43,649", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "ERROR", "message": "Reconnection attempt 1 failed: Failed to connect to AI Voice Mate: [Errno 111] Connection refused", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 21:30:43,650", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 2/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 21:30:53,651", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-25 21:30:53,657", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "ERROR", "message": "Reconnection attempt 2 failed: Failed to connect to AI Voice Mate: [Errno 111] Connection refused", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-25 21:30:53,657", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 3/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 07:59:09,503", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 07:59:09,513", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 07:59:12,053", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "92e2fa68-86d6-400d-9964-c3083a0a8d35"}
{"asctime": "2025-07-26 07:59:12,053", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567890", "correlation_id": "92e2fa68-86d6-400d-9964-c3083a0a8d35"}
{"asctime": "2025-07-26 07:59:12,053", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "92e2fa68-86d6-400d-9964-c3083a0a8d35"}
{"asctime": "2025-07-26 07:59:12,053", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "92e2fa68-86d6-400d-9964-c3083a0a8d35"}
{"asctime": "2025-07-26 07:59:12,054", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "caller_number": "+1234567890", "called_number": "+0987654321", "active_sessions": 1, "correlation_id": "92e2fa68-86d6-400d-9964-c3083a0a8d35"}
{"asctime": "2025-07-26 07:59:12,054", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "test_startup_001", "user_id": "test_startup_001", "correlation_id": "92e2fa68-86d6-400d-9964-c3083a0a8d35"}
{"asctime": "2025-07-26 07:59:12,054", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: test_startup_001 -> active", "session_id": "test_startup_001", "user_id": null, "new_status": "active", "correlation_id": "92e2fa68-86d6-400d-9964-c3083a0a8d35"}
{"asctime": "2025-07-26 08:04:01,181", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "WARNING", "message": "Connection closed by AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:04:01,182", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 1/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:04:06,182", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 08:04:06,184", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "ERROR", "message": "Reconnection attempt 1 failed: Failed to connect to AI Voice Mate: [Errno 111] Connection refused", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:04:06,184", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 2/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:04:18,759", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 08:04:18,760", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "ERROR", "message": "Reconnection attempt 2 failed: Failed to connect to AI Voice Mate: [Errno 111] Connection refused", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:04:18,760", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 3/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:04:33,760", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 08:04:33,761", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "ERROR", "message": "Reconnection attempt 3 failed: Failed to connect to AI Voice Mate: [Errno 111] Connection refused", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:04:33,762", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "CRITICAL", "message": "All reconnection attempts failed", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:04:33,905", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "final_status": "active", "duration": 321.851621, "correlation_id": null}
{"asctime": "2025-07-26 08:04:33,906", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Cleaned up 1 expired sessions", "session_id": null, "user_id": null, "expired_session_count": 1, "correlation_id": null}
{"asctime": "2025-07-26 08:28:39,914", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session cleanup task cancelled", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:28:39,916", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session manager shutdown completed", "session_id": null, "user_id": null, "ended_sessions": 0, "correlation_id": null}
{"asctime": "2025-07-26 08:28:39,916", "name": "smartflo_ai_caller.bidirectional_ws_handler.BiDirectionalWSHandler", "levelname": "ERROR", "message": "Error closing vendor connection: Cannot call \"send\" once a close message has been sent.", "correlation_id": null}
{"asctime": "2025-07-26 08:28:39,916", "name": "smartflo_ai_caller.bidirectional_ws_handler.BiDirectionalWSHandler", "levelname": "ERROR", "message": "Error closing vendor connection: Cannot call \"send\" once a close message has been sent.", "correlation_id": null}
{"asctime": "2025-07-26 08:28:39,916", "name": "smartflo_ai_caller.bidirectional_ws_handler.BiDirectionalWSHandler", "levelname": "ERROR", "message": "Error closing vendor connection: Cannot call \"send\" once a close message has been sent.", "correlation_id": null}
{"asctime": "2025-07-26 08:28:39,917", "name": "smartflo_ai_caller.bidirectional_ws_handler.BiDirectionalWSHandler", "levelname": "ERROR", "message": "Error closing vendor connection: Cannot call \"send\" once a close message has been sent.", "correlation_id": null}
{"asctime": "2025-07-26 08:28:39,917", "name": "smartflo_ai_caller.bidirectional_ws_handler.BiDirectionalWSHandler", "levelname": "ERROR", "message": "Error closing vendor connection: Cannot call \"send\" once a close message has been sent.", "correlation_id": null}
{"asctime": "2025-07-26 08:28:39,917", "name": "smartflo_ai_caller.bidirectional_ws_handler.BiDirectionalWSHandler", "levelname": "INFO", "message": "BiDirectional WebSocket handler shutdown completed", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:28:44,220", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 08:28:44,227", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 08:28:46,716", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "affc74c6-bd95-4877-bd99-bb3a3749bd82"}
{"asctime": "2025-07-26 08:28:46,717", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567890", "correlation_id": "affc74c6-bd95-4877-bd99-bb3a3749bd82"}
{"asctime": "2025-07-26 08:28:46,717", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "affc74c6-bd95-4877-bd99-bb3a3749bd82"}
{"asctime": "2025-07-26 08:28:46,718", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "affc74c6-bd95-4877-bd99-bb3a3749bd82"}
{"asctime": "2025-07-26 08:28:46,718", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "caller_number": "+1234567890", "called_number": "+0987654321", "active_sessions": 1, "correlation_id": "affc74c6-bd95-4877-bd99-bb3a3749bd82"}
{"asctime": "2025-07-26 08:28:46,718", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "test_startup_001", "user_id": "test_startup_001", "correlation_id": "affc74c6-bd95-4877-bd99-bb3a3749bd82"}
{"asctime": "2025-07-26 08:28:46,718", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: test_startup_001 -> active", "session_id": "test_startup_001", "user_id": null, "new_status": "active", "correlation_id": "affc74c6-bd95-4877-bd99-bb3a3749bd82"}
{"asctime": "2025-07-26 08:34:02,135", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "final_status": "active", "duration": 315.417, "correlation_id": null}
{"asctime": "2025-07-26 08:34:02,137", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Cleaned up 1 expired sessions", "session_id": null, "user_id": null, "expired_session_count": 1, "correlation_id": null}
{"asctime": "2025-07-26 08:35:51,256", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session cleanup task cancelled", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:35:51,256", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session manager shutdown completed", "session_id": null, "user_id": null, "ended_sessions": 0, "correlation_id": null}
{"asctime": "2025-07-26 08:35:51,257", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "WARNING", "message": "Connection closed by AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:35:51,257", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 1/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:35:51,258", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Disconnected from AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:35:51,258", "name": "smartflo_ai_caller.bidirectional_ws_handler.BiDirectionalWSHandler", "levelname": "ERROR", "message": "Error closing vendor connection: Cannot call \"send\" once a close message has been sent.", "correlation_id": null}
{"asctime": "2025-07-26 08:35:51,259", "name": "smartflo_ai_caller.bidirectional_ws_handler.BiDirectionalWSHandler", "levelname": "INFO", "message": "BiDirectional WebSocket handler shutdown completed", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:35:55,876", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 08:35:55,882", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 08:35:58,570", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "dd79e692-4399-48a6-8cae-4803a69caebd"}
{"asctime": "2025-07-26 08:35:58,570", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567890", "correlation_id": "dd79e692-4399-48a6-8cae-4803a69caebd"}
{"asctime": "2025-07-26 08:35:58,570", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "dd79e692-4399-48a6-8cae-4803a69caebd"}
{"asctime": "2025-07-26 08:35:58,571", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "dd79e692-4399-48a6-8cae-4803a69caebd"}
{"asctime": "2025-07-26 08:35:58,571", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "caller_number": "+1234567890", "called_number": "+0987654321", "active_sessions": 1, "correlation_id": "dd79e692-4399-48a6-8cae-4803a69caebd"}
{"asctime": "2025-07-26 08:35:58,571", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "test_startup_001", "user_id": "test_startup_001", "correlation_id": "dd79e692-4399-48a6-8cae-4803a69caebd"}
{"asctime": "2025-07-26 08:35:58,571", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: test_startup_001 -> active", "session_id": "test_startup_001", "user_id": null, "new_status": "active", "correlation_id": "dd79e692-4399-48a6-8cae-4803a69caebd"}
{"asctime": "2025-07-26 08:41:18,367", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "final_status": "active", "duration": 319.796348, "correlation_id": null}
{"asctime": "2025-07-26 08:41:18,369", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Cleaned up 1 expired sessions", "session_id": null, "user_id": null, "expired_session_count": 1, "correlation_id": null}
{"asctime": "2025-07-26 08:41:49,865", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session cleanup task cancelled", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:41:49,865", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session manager shutdown completed", "session_id": null, "user_id": null, "ended_sessions": 0, "correlation_id": null}
{"asctime": "2025-07-26 08:41:49,866", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "WARNING", "message": "Connection closed by AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:41:49,866", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 1/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:41:49,866", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Disconnected from AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:41:49,866", "name": "smartflo_ai_caller.bidirectional_ws_handler.BiDirectionalWSHandler", "levelname": "ERROR", "message": "Error closing vendor connection: Cannot call \"send\" once a close message has been sent.", "correlation_id": null}
{"asctime": "2025-07-26 08:41:49,866", "name": "smartflo_ai_caller.bidirectional_ws_handler.BiDirectionalWSHandler", "levelname": "INFO", "message": "BiDirectional WebSocket handler shutdown completed", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:41:53,516", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 08:41:53,522", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 08:41:56,226", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "02d7a5aa-fd96-4ec3-a193-eceb32a050a5"}
{"asctime": "2025-07-26 08:41:56,227", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567890", "correlation_id": "02d7a5aa-fd96-4ec3-a193-eceb32a050a5"}
{"asctime": "2025-07-26 08:41:56,227", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "02d7a5aa-fd96-4ec3-a193-eceb32a050a5"}
{"asctime": "2025-07-26 08:41:56,227", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "02d7a5aa-fd96-4ec3-a193-eceb32a050a5"}
{"asctime": "2025-07-26 08:41:56,227", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "caller_number": "+1234567890", "called_number": "+0987654321", "active_sessions": 1, "correlation_id": "02d7a5aa-fd96-4ec3-a193-eceb32a050a5"}
{"asctime": "2025-07-26 08:41:56,228", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "test_startup_001", "user_id": "test_startup_001", "correlation_id": "02d7a5aa-fd96-4ec3-a193-eceb32a050a5"}
{"asctime": "2025-07-26 08:41:56,228", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: test_startup_001 -> active", "session_id": "test_startup_001", "user_id": null, "new_status": "active", "correlation_id": "02d7a5aa-fd96-4ec3-a193-eceb32a050a5"}
{"asctime": "2025-07-26 08:47:09,136", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Session cleanup task cancelled", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 08:47:24,184", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 08:47:24,190", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 08:47:26,900", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+1234567890", "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:47:26,901", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+1234567890", "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:47:26,901", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:47:26,901", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:47:26,901", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "caller_number": "+1234567890", "called_number": "+0987654321", "active_sessions": 1, "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:47:26,902", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "test_startup_001", "user_id": "test_startup_001", "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:47:26,902", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: test_startup_001 -> active", "session_id": "test_startup_001", "user_id": null, "new_status": "active", "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:52:49,370", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "final_status": "active", "duration": 322.468703, "correlation_id": null}
{"asctime": "2025-07-26 08:52:49,370", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Cleaned up 1 expired sessions", "session_id": null, "user_id": null, "expired_session_count": 1, "correlation_id": null}
{"asctime": "2025-07-26 09:01:01,278", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "WARNING", "message": "Connection closed by AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 09:01:01,278", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 1/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 09:01:08,527", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 09:01:18,528", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "ERROR", "message": "Reconnection attempt 1 failed: Connection timeout after 30s", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 09:01:18,528", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 2/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 09:01:28,529", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 09:01:40,806", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "ERROR", "message": "Reconnection attempt 2 failed: Connection timeout after 30s", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 09:01:40,806", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 3/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 09:01:55,807", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 09:02:05,808", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "ERROR", "message": "Reconnection attempt 3 failed: Connection timeout after 30s", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 09:02:05,809", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "CRITICAL", "message": "All reconnection attempts failed", "session_id": null, "user_id": null, "correlation_id": null}
