/usr/lib/python3.10/runpy.py:126: RuntimeWarning: 'smartflo_ai_caller.main' found in sys.modules after import of package 'smartflo_ai_caller', but prior to execution of 'smartflo_ai_caller.main'; this may result in unpredictable behaviour
  warn(RuntimeWarning(msg))
INFO:     Started server process [43445]
INFO:     Waiting for application startup.
{"asctime": "2025-07-26 08:47:24,183", "name": "__main__", "levelname": "INFO", "message": "Starting Smartflo AI Caller middleware...", "correlation_id": null}
{"asctime": "2025-07-26 08:47:24,184", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 08:47:24,190", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 08:47:24,190", "name": "__main__", "levelname": "INFO", "message": "Successfully connected to AI Voice Mate", "correlation_id": null}
{"asctime": "2025-07-26 08:47:24,190", "name": "__main__", "levelname": "INFO", "message": "Smartflo AI Caller middleware started successfully", "correlation_id": null}
{"asctime": "2025-07-26 08:47:24,190", "name": "uvicorn.error", "levelname": "INFO", "message": "Application startup complete.", "correlation_id": null}
{"asctime": "2025-07-26 08:47:24,191", "name": "uvicorn.error", "levelname": "INFO", "message": "Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)", "color_message": "Uvicorn running on \u001b[1m%s://%s:%d\u001b[0m (Press CTRL+C to quit)", "correlation_id": null}
{"asctime": "2025-07-26 08:47:26,862", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:53884 - \"GET / HTTP/1.1\" 200", "correlation_id": "95855633-2054-4dd0-9fc1-0775da6728c3"}
{"asctime": "2025-07-26 08:47:26,872", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:53896 - \"GET / HTTP/1.1\" 200", "correlation_id": "dec426c0-d214-4a4e-a02b-531a2cad1a12"}
{"asctime": "2025-07-26 08:47:26,885", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:53898 - \"GET /health HTTP/1.1\" 200", "correlation_id": "5ddfdf02-5358-4c5c-8fa9-c1964a1819ef"}
{"asctime": "2025-07-26 08:47:26,900", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+**********", "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:47:26,901", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+**********", "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:47:26,901", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:47:26,901", "name": "__main__", "levelname": "INFO", "message": "Processing Smartflo event: call_received", "session_id": "test_startup_001", "user_id": null, "action": "call_received", "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:47:26,901", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:47:26,901", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "caller_number": "+**********", "called_number": "+0987654321", "active_sessions": 1, "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:47:26,902", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "User registered with AI Voice Mate", "session_id": "test_startup_001", "user_id": "test_startup_001", "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:47:26,902", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Updated session status: test_startup_001 -> active", "session_id": "test_startup_001", "user_id": null, "new_status": "active", "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:47:26,902", "name": "__main__", "levelname": "INFO", "message": "Call received and user registered", "session_id": "test_startup_001", "user_id": null, "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:47:26,902", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:53906 - \"POST /webhook/smartflo HTTP/1.1\" 200", "correlation_id": "89a78329-3a23-4913-9cce-aa9204bb6339"}
{"asctime": "2025-07-26 08:47:26,959", "name": "__main__", "levelname": "INFO", "message": "Store user response received", "session_id": null, "user_id": null, "response": "user update successfully", "correlation_id": null}
{"asctime": "2025-07-26 08:47:26,959", "name": "__main__", "levelname": "INFO", "message": "User registration confirmed by AI Voice Mate", "correlation_id": null}
{"asctime": "2025-07-26 08:47:50,252", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:37384 - \"GET /health HTTP/1.1\" 200", "correlation_id": "a43cf51e-e4b5-421a-9ee4-cf17da49f0a3"}
{"asctime": "2025-07-26 08:47:52,576", "name": "uvicorn.error", "levelname": "INFO", "message": "127.0.0.1:37398 - \"WebSocket /audio-stream\" [accepted]", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x717e57f03220>", "correlation_id": null}
{"asctime": "2025-07-26 08:47:52,577", "name": "__main__", "levelname": "ERROR", "message": "Error in audio stream WebSocket: 'WebSocket' object has no attribute 'remote_address'", "correlation_id": null}
{"asctime": "2025-07-26 08:47:52,577", "name": "uvicorn.error", "levelname": "INFO", "message": "connection open", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x717e57f03220>", "correlation_id": null}
{"asctime": "2025-07-26 08:47:52,581", "name": "uvicorn.error", "levelname": "INFO", "message": "connection closed", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x717e57f03220>", "correlation_id": null}
{"asctime": "2025-07-26 08:52:49,370", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "final_status": "active", "duration": 322.468703, "correlation_id": null}
{"asctime": "2025-07-26 08:52:49,370", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Cleaned up 1 expired sessions", "session_id": null, "user_id": null, "expired_session_count": 1, "correlation_id": null}
{"asctime": "2025-07-26 08:53:26,722", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:35934 - \"GET /health HTTP/1.1\" 200", "correlation_id": "8d185a38-9962-4f5d-8a39-a427ecb10b0f"}
{"asctime": "2025-07-26 08:53:31,651", "name": "uvicorn.error", "levelname": "INFO", "message": "127.0.0.1:35944 - \"WebSocket /audio-stream\" [accepted]", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x717e57f4ce20>", "correlation_id": null}
{"asctime": "2025-07-26 08:53:31,651", "name": "__main__", "levelname": "ERROR", "message": "Error in audio stream WebSocket: 'WebSocket' object has no attribute 'remote_address'", "correlation_id": null}
{"asctime": "2025-07-26 08:53:31,651", "name": "uvicorn.error", "levelname": "INFO", "message": "connection open", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x717e57f4ce20>", "correlation_id": null}
{"asctime": "2025-07-26 08:53:31,655", "name": "uvicorn.error", "levelname": "INFO", "message": "connection closed", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x717e57f4ce20>", "correlation_id": null}
{"asctime": "2025-07-26 08:59:29,663", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:47046 - \"GET /health HTTP/1.1\" 200", "correlation_id": "dcd99d9b-37a6-4a35-a786-2161e2a318f4"}
{"asctime": "2025-07-26 08:59:35,868", "name": "uvicorn.error", "levelname": "INFO", "message": "127.0.0.1:40544 - \"WebSocket /audio-stream\" [accepted]", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x717e57f03010>", "correlation_id": null}
{"asctime": "2025-07-26 08:59:35,868", "name": "__main__", "levelname": "ERROR", "message": "Error in audio stream WebSocket: 'WebSocket' object has no attribute 'remote_address'", "correlation_id": null}
{"asctime": "2025-07-26 08:59:35,868", "name": "uvicorn.error", "levelname": "INFO", "message": "connection open", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x717e57f03010>", "correlation_id": null}
{"asctime": "2025-07-26 08:59:35,873", "name": "uvicorn.error", "levelname": "INFO", "message": "connection closed", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x717e57f03010>", "correlation_id": null}
{"asctime": "2025-07-26 09:01:01,278", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "WARNING", "message": "Connection closed by AI Voice Mate", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 09:01:01,278", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 1/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 09:01:08,527", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 09:01:18,528", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "ERROR", "message": "Reconnection attempt 1 failed: Connection timeout after 30s", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 09:01:18,528", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 2/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 09:01:28,529", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 09:01:40,806", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "ERROR", "message": "Reconnection attempt 2 failed: Connection timeout after 30s", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 09:01:40,806", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Attempting to reconnect (attempt 3/3)", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 09:01:55,807", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 09:02:05,808", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "ERROR", "message": "Reconnection attempt 3 failed: Connection timeout after 30s", "session_id": null, "user_id": null, "correlation_id": null}
{"asctime": "2025-07-26 09:02:05,809", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "CRITICAL", "message": "All reconnection attempts failed", "session_id": null, "user_id": null, "correlation_id": null}
