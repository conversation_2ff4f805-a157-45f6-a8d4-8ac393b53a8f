/usr/lib/python3.10/runpy.py:126: RuntimeWarning: 'smartflo_ai_caller.main' found in sys.modules after import of package 'smartflo_ai_caller', but prior to execution of 'smartflo_ai_caller.main'; this may result in unpredictable behaviour
  warn(RuntimeWarning(msg))
INFO:     Started server process [78360]
INFO:     Waiting for application startup.
{"asctime": "2025-07-26 10:14:59,788", "name": "__main__", "levelname": "INFO", "message": "Starting Smartflo AI Caller middleware...", "correlation_id": null}
{"asctime": "2025-07-26 10:14:59,788", "name": "smartflo_ai_caller.ai_voice_client.AIVoiceMateClient", "levelname": "INFO", "message": "Connecting to AI Voice Mate at ws://localhost:5010", "session_id": null, "user_id": null, "ws_url": "ws://localhost:5010", "correlation_id": null}
{"asctime": "2025-07-26 10:15:09,801", "name": "__main__", "levelname": "ERROR", "message": "Failed to connect to AI Voice Mate: Connection timeout after 30s", "correlation_id": null}
{"asctime": "2025-07-26 10:15:09,801", "name": "__main__", "levelname": "INFO", "message": "Smartflo AI Caller middleware started successfully", "correlation_id": null}
{"asctime": "2025-07-26 10:15:09,801", "name": "uvicorn.error", "levelname": "INFO", "message": "Application startup complete.", "correlation_id": null}
{"asctime": "2025-07-26 10:15:09,801", "name": "uvicorn.error", "levelname": "INFO", "message": "Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)", "color_message": "Uvicorn running on \u001b[1m%s://%s:%d\u001b[0m (Press CTRL+C to quit)", "correlation_id": null}
{"asctime": "2025-07-26 10:15:10,349", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:42530 - \"GET / HTTP/1.1\" 200", "correlation_id": "88978db2-ecb4-4308-bad2-5dd5debdd42d"}
{"asctime": "2025-07-26 10:15:10,356", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:42546 - \"GET / HTTP/1.1\" 200", "correlation_id": "028e6fd6-24d0-4eb3-b80d-8bc0ef04358d"}
{"asctime": "2025-07-26 10:15:10,365", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:42554 - \"GET /health HTTP/1.1\" 200", "correlation_id": "90186f50-a674-445d-8a3e-69436d9c02ef"}
{"asctime": "2025-07-26 10:15:10,375", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook payload validated successfully", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_type": "inbound", "caller_number": "+**********", "correlation_id": "dcb82223-5c84-4221-ac3b-62afaa292100"}
{"asctime": "2025-07-26 10:15:10,375", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Processing webhook event: call_received", "session_id": "test_startup_001", "user_id": null, "event_type": "call_received", "call_status": "ringing", "caller_number": "+**********", "correlation_id": "dcb82223-5c84-4221-ac3b-62afaa292100"}
{"asctime": "2025-07-26 10:15:10,376", "name": "smartflo_ai_caller.webhook_handler.WebhookHandler", "levelname": "INFO", "message": "Webhook event processed successfully: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "dcb82223-5c84-4221-ac3b-62afaa292100"}
{"asctime": "2025-07-26 10:15:10,376", "name": "__main__", "levelname": "INFO", "message": "Processing Smartflo event: call_received", "session_id": "test_startup_001", "user_id": null, "action": "call_received", "correlation_id": "dcb82223-5c84-4221-ac3b-62afaa292100"}
{"asctime": "2025-07-26 10:15:10,376", "name": "smartflo_ai_caller.event_transformer.EventTransformer", "levelname": "INFO", "message": "Event transformation completed: call_received", "session_id": "test_startup_001", "user_id": null, "correlation_id": "dcb82223-5c84-4221-ac3b-62afaa292100"}
{"asctime": "2025-07-26 10:15:10,376", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Created new session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "caller_number": "+**********", "called_number": "+0987654321", "active_sessions": 1, "correlation_id": "dcb82223-5c84-4221-ac3b-62afaa292100"}
{"asctime": "2025-07-26 10:15:10,376", "name": "__main__", "levelname": "ERROR", "message": "Error processing Smartflo event: Not connected to AI Voice Mate", "session_id": "test_startup_001", "user_id": null, "action": "call_received", "error": "Not connected to AI Voice Mate", "correlation_id": "dcb82223-5c84-4221-ac3b-62afaa292100"}
{"asctime": "2025-07-26 10:15:10,377", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:42562 - \"POST /webhook/smartflo HTTP/1.1\" 200", "correlation_id": "dcb82223-5c84-4221-ac3b-62afaa292100"}
{"asctime": "2025-07-26 10:15:32,815", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:55242 - \"GET /health HTTP/1.1\" 200", "correlation_id": "f3ea5ad4-0e05-4e1b-83b0-8b0968e69bd5"}
{"asctime": "2025-07-26 10:15:34,736", "name": "uvicorn.error", "levelname": "INFO", "message": "127.0.0.1:44070 - \"WebSocket /audio-stream\" [accepted]", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x7024956d0e20>", "correlation_id": null}
{"asctime": "2025-07-26 10:15:34,737", "name": "__main__", "levelname": "ERROR", "message": "Error in audio stream WebSocket: 'WebSocket' object has no attribute 'remote_address'", "correlation_id": null}
{"asctime": "2025-07-26 10:15:34,739", "name": "uvicorn.error", "levelname": "INFO", "message": "connection open", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x7024956d0e20>", "correlation_id": null}
{"asctime": "2025-07-26 10:15:34,743", "name": "uvicorn.error", "levelname": "INFO", "message": "connection closed", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x7024956d0e20>", "correlation_id": null}
{"asctime": "2025-07-26 10:16:09,091", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:51944 - \"GET /health HTTP/1.1\" 200", "correlation_id": "880eb259-f2aa-4f8c-9cda-9e4ef18d31c2"}
{"asctime": "2025-07-26 10:16:10,569", "name": "uvicorn.error", "levelname": "INFO", "message": "127.0.0.1:51954 - \"WebSocket /audio-stream\" [accepted]", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x702493500ee0>", "correlation_id": null}
{"asctime": "2025-07-26 10:16:10,570", "name": "__main__", "levelname": "ERROR", "message": "Error in audio stream WebSocket: 'WebSocket' object has no attribute 'remote_address'", "correlation_id": null}
{"asctime": "2025-07-26 10:16:10,571", "name": "uvicorn.error", "levelname": "INFO", "message": "connection open", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x702493500ee0>", "correlation_id": null}
{"asctime": "2025-07-26 10:16:10,575", "name": "uvicorn.error", "levelname": "INFO", "message": "connection closed", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x702493500ee0>", "correlation_id": null}
{"asctime": "2025-07-26 10:17:24,380", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:37786 - \"GET /health HTTP/1.1\" 200", "correlation_id": "bc9d88c3-ae58-4ff6-934c-215e9d76c625"}
{"asctime": "2025-07-26 10:17:37,377", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:42826 - \"GET /health HTTP/1.1\" 200", "correlation_id": "16b25160-49e7-4fcd-b18c-5601dca2bb0a"}
{"asctime": "2025-07-26 10:17:53,420", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:50154 - \"GET /health HTTP/1.1\" 200", "correlation_id": "0ae865cf-e499-4438-8346-3ba05ea7a51d"}
{"asctime": "2025-07-26 10:17:57,635", "name": "uvicorn.error", "levelname": "INFO", "message": "127.0.0.1:58380 - \"WebSocket /audio-stream\" [accepted]", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x702493502020>", "correlation_id": null}
{"asctime": "2025-07-26 10:17:57,636", "name": "__main__", "levelname": "ERROR", "message": "Error in audio stream WebSocket: 'WebSocket' object has no attribute 'remote_address'", "correlation_id": null}
{"asctime": "2025-07-26 10:17:57,636", "name": "uvicorn.error", "levelname": "INFO", "message": "connection open", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x702493502020>", "correlation_id": null}
{"asctime": "2025-07-26 10:17:57,641", "name": "uvicorn.error", "levelname": "INFO", "message": "connection closed", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x702493502020>", "correlation_id": null}
{"asctime": "2025-07-26 10:18:51,650", "name": "uvicorn.error", "levelname": "INFO", "message": "127.0.0.1:51106 - \"WebSocket /audio-stream\" [accepted]", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x702493502710>", "correlation_id": null}
{"asctime": "2025-07-26 10:18:51,650", "name": "__main__", "levelname": "ERROR", "message": "Error in audio stream WebSocket: 'WebSocket' object has no attribute 'remote_address'", "correlation_id": null}
{"asctime": "2025-07-26 10:18:51,651", "name": "uvicorn.error", "levelname": "INFO", "message": "connection open", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x702493502710>", "correlation_id": null}
{"asctime": "2025-07-26 10:18:51,656", "name": "uvicorn.error", "levelname": "INFO", "message": "connection closed", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x702493502710>", "correlation_id": null}
{"asctime": "2025-07-26 10:19:18,569", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:43864 - \"GET /health HTTP/1.1\" 200", "correlation_id": "833c9685-906e-4d39-beff-e96417945de8"}
{"asctime": "2025-07-26 10:19:20,203", "name": "uvicorn.error", "levelname": "INFO", "message": "127.0.0.1:43874 - \"WebSocket /audio-stream\" [accepted]", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x702493503250>", "correlation_id": null}
{"asctime": "2025-07-26 10:19:20,203", "name": "__main__", "levelname": "ERROR", "message": "Error in audio stream WebSocket: 'WebSocket' object has no attribute 'remote_address'", "correlation_id": null}
{"asctime": "2025-07-26 10:19:20,203", "name": "uvicorn.error", "levelname": "INFO", "message": "connection open", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x702493503250>", "correlation_id": null}
{"asctime": "2025-07-26 10:19:20,207", "name": "uvicorn.error", "levelname": "INFO", "message": "connection closed", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x702493503250>", "correlation_id": null}
{"asctime": "2025-07-26 10:19:49,158", "name": "uvicorn.access", "levelname": "INFO", "message": "127.0.0.1:60830 - \"GET /health HTTP/1.1\" 200", "correlation_id": "1fe2f8c7-f146-4252-9d4a-d8a8816d0ab0"}
{"asctime": "2025-07-26 10:19:53,030", "name": "uvicorn.error", "levelname": "INFO", "message": "127.0.0.1:60834 - \"WebSocket /audio-stream\" [accepted]", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x702493503070>", "correlation_id": null}
{"asctime": "2025-07-26 10:19:53,030", "name": "__main__", "levelname": "ERROR", "message": "Error in audio stream WebSocket: 'WebSocket' object has no attribute 'remote_address'", "correlation_id": null}
{"asctime": "2025-07-26 10:19:53,030", "name": "uvicorn.error", "levelname": "INFO", "message": "connection open", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x702493503070>", "correlation_id": null}
{"asctime": "2025-07-26 10:19:53,034", "name": "uvicorn.error", "levelname": "INFO", "message": "connection closed", "websocket": "<uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol object at 0x702493503070>", "correlation_id": null}
{"asctime": "2025-07-26 10:20:25,410", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Removed session: test_startup_001", "session_id": "test_startup_001", "user_id": null, "final_status": "initialized", "duration": 315.033799, "correlation_id": null}
{"asctime": "2025-07-26 10:20:25,411", "name": "smartflo_ai_caller.session_manager.SessionManager", "levelname": "INFO", "message": "Cleaned up 1 expired sessions", "session_id": null, "user_id": null, "expired_session_count": 1, "correlation_id": null}
