#!/usr/bin/env python3
"""Simple test to verify basic service functionality."""

import asyncio
import json
import httpx


async def test_basic_endpoints():
    """Test basic endpoints."""
    
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient() as client:
        # Test root endpoint
        print("Testing root endpoint...")
        response = await client.get(f"{base_url}/")
        print(f"GET /: {response.status_code} - {response.json()}")
        
        # Test webhook endpoint  
        print("\nTesting webhook endpoint...")
        webhook_payload = {
            "call_id": "test_001",
            "call_type": "inbound",
            "caller_number": "+1234567890",
            "called_number": "+0987654321",
            "event_type": "call_received",
            "event_timestamp": "2024-01-15T10:30:00Z",
            "call_status": "ringing"
        }
        
        response = await client.post(
            f"{base_url}/webhook/smartflo",
            json=webhook_payload,
            headers={"Content-Type": "application/json"}
        )
        print(f"POST /webhook/smartflo: {response.status_code}")
        if response.status_code != 404:
            print(f"Response: {response.json()}")
        else:
            print("Response: 404 - Route not found")
        
        # Test sessions endpoint
        print("\nTesting sessions endpoint...")
        response = await client.get(f"{base_url}/sessions")
        print(f"GET /sessions: {response.status_code}")
        if response.status_code != 404:
            print(f"Response: {response.json()}")
        else:
            print("Response: 404 - Route not found")
        
        # Test health endpoint with different variations
        print("\nTesting health endpoints...")
        for path in ["/health", "/metrics"]:
            response = await client.get(f"{base_url}{path}")
            print(f"GET {path}: {response.status_code}")
            if response.status_code != 404:
                print(f"Response: {response.json()}")
            else:
                print(f"Response: 404 - Route not found")


if __name__ == "__main__":
    asyncio.run(test_basic_endpoints())