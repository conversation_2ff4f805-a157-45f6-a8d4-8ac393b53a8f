#!/usr/bin/env python3
"""Comprehensive test suite for Smartflo AI Caller middleware."""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List, Any
import httpx
import websockets


class SmartfloAICallerTester:
    """Comprehensive tester for Smartflo AI Caller middleware."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.webhook_url = f"{base_url}/webhook/smartflo"
        self.health_url = f"{base_url}/health"
        self.metrics_url = f"{base_url}/metrics"
        self.sessions_url = f"{base_url}/sessions"
        
        self.test_results = []
        self.ai_voice_mate_url = "ws://localhost:5010"
    
    def log_test(self, test_name: str, status: str, details: str = ""):
        """Log test result."""
        result = {
            "test": test_name,
            "status": status,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        status_emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_emoji} {test_name}: {status} {details}")
    
    async def test_service_startup(self):
        """Test if the service is running."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/")
                if response.status_code == 200:
                    data = response.json()
                    if data.get("service") == "smartflo-ai-caller":
                        self.log_test("Service Startup", "PASS", f"Version: {data.get('version')}")
                    else:
                        self.log_test("Service Startup", "FAIL", "Invalid service response")
                else:
                    self.log_test("Service Startup", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Service Startup", "FAIL", str(e))
    
    async def test_health_endpoint(self):
        """Test health check endpoint."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(self.health_url)
                if response.status_code == 200:
                    data = response.json()
                    status = data.get("status")
                    ai_status = data.get("ai_voice_mate")
                    
                    if status in ["healthy", "degraded"]:
                        details = f"Status: {status}, AI: {ai_status}"
                        self.log_test("Health Endpoint", "PASS", details)
                    else:
                        self.log_test("Health Endpoint", "FAIL", f"Invalid status: {status}")
                else:
                    self.log_test("Health Endpoint", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Health Endpoint", "FAIL", str(e))
    
    async def test_metrics_endpoint(self):
        """Test metrics endpoint."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(self.metrics_url)
                if response.status_code == 200:
                    data = response.json()
                    if "sessions" in data and "ai_connection" in data:
                        self.log_test("Metrics Endpoint", "PASS", "All metrics present")
                    else:
                        self.log_test("Metrics Endpoint", "FAIL", "Missing metrics data")
                else:
                    self.log_test("Metrics Endpoint", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Metrics Endpoint", "FAIL", str(e))
    
    async def test_webhook_call_received(self):
        """Test call_received webhook event."""
        payload = {
            "call_id": "test_call_001",
            "call_type": "inbound", 
            "caller_number": "+1234567890",
            "called_number": "+0987654321",
            "event_type": "call_received",
            "event_timestamp": datetime.now().isoformat(),
            "call_status": "ringing",
            "call_duration": None,
            "agent_id": None,
            "dtmf_digits": None,
            "recording_url": None,
            "billing_info": {},
            "custom_data": {}
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.webhook_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("status") == "success":
                        self.log_test("Webhook Call Received", "PASS", "Event processed successfully")
                        
                        # Wait a moment for processing
                        await asyncio.sleep(1)
                        
                        # Check if session was created
                        await self.verify_session_created("test_call_001")
                    else:
                        self.log_test("Webhook Call Received", "FAIL", f"Response: {data}")
                else:
                    self.log_test("Webhook Call Received", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Webhook Call Received", "FAIL", str(e))
    
    async def test_webhook_call_answered_ivr(self):
        """Test call_answered_ivr webhook event."""
        payload = {
            "call_id": "test_call_001",
            "call_type": "inbound",
            "caller_number": "+1234567890", 
            "called_number": "+0987654321",
            "event_type": "call_answered_ivr",
            "event_timestamp": datetime.now().isoformat(),
            "call_status": "answered",
            "call_duration": None,
            "agent_id": None,
            "dtmf_digits": None,
            "recording_url": None,
            "billing_info": {},
            "custom_data": {}
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.webhook_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    self.log_test("Webhook Call Answered IVR", "PASS", "Event processed successfully")
                else:
                    self.log_test("Webhook Call Answered IVR", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Webhook Call Answered IVR", "FAIL", str(e))
    
    async def test_webhook_dtmf_received(self):
        """Test dtmf_received webhook event."""
        payload = {
            "call_id": "test_call_001",
            "call_type": "inbound",
            "caller_number": "+1234567890",
            "called_number": "+0987654321", 
            "event_type": "dtmf_received",
            "event_timestamp": datetime.now().isoformat(),
            "call_status": "answered",
            "call_duration": None,
            "agent_id": None,
            "dtmf_digits": "123*",
            "recording_url": None,
            "billing_info": {},
            "custom_data": {}
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.webhook_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    self.log_test("Webhook DTMF Received", "PASS", "Event processed successfully")
                else:
                    self.log_test("Webhook DTMF Received", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Webhook DTMF Received", "FAIL", str(e))
    
    async def test_webhook_call_hangup(self):
        """Test call_hangup webhook event."""
        payload = {
            "call_id": "test_call_001",
            "call_type": "inbound",
            "caller_number": "+1234567890",
            "called_number": "+0987654321",
            "event_type": "call_hangup",
            "event_timestamp": datetime.now().isoformat(),
            "call_status": "ended",
            "call_duration": 120,
            "agent_id": None,
            "dtmf_digits": None,
            "recording_url": "https://example.com/recording.wav",
            "billing_info": {"duration": 120, "cost": 0.05},
            "custom_data": {}
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.webhook_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    self.log_test("Webhook Call Hangup", "PASS", "Event processed successfully")
                else:
                    self.log_test("Webhook Call Hangup", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Webhook Call Hangup", "FAIL", str(e))
    
    async def test_webhook_validation_errors(self):
        """Test webhook validation error scenarios."""
        test_cases = [
            {
                "name": "Invalid JSON",
                "payload": "invalid json",
                "content_type": "application/json",
                "expected_status": 400
            },
            {
                "name": "Missing Required Fields",
                "payload": {"call_id": "test"},
                "content_type": "application/json", 
                "expected_status": 400
            },
            {
                "name": "Invalid Content-Type",
                "payload": {"call_id": "test_call_002", "event_type": "call_received"},
                "content_type": "text/plain",
                "expected_status": 400
            }
        ]
        
        for case in test_cases:
            try:
                async with httpx.AsyncClient() as client:
                    if isinstance(case["payload"], dict):
                        response = await client.post(
                            self.webhook_url,
                            json=case["payload"],
                            headers={"Content-Type": case["content_type"]}
                        )
                    else:
                        response = await client.post(
                            self.webhook_url,
                            content=case["payload"],
                            headers={"Content-Type": case["content_type"]}
                        )
                    
                    if response.status_code == case["expected_status"]:
                        self.log_test(f"Validation Error - {case['name']}", "PASS", 
                                    f"Expected {case['expected_status']}, got {response.status_code}")
                    else:
                        self.log_test(f"Validation Error - {case['name']}", "FAIL",
                                    f"Expected {case['expected_status']}, got {response.status_code}")
            except Exception as e:
                self.log_test(f"Validation Error - {case['name']}", "FAIL", str(e))
    
    async def verify_session_created(self, session_id: str):
        """Verify that a session was created."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.sessions_url}/{session_id}")
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("session_id") == session_id:
                        self.log_test("Session Creation Verification", "PASS", 
                                    f"Session {session_id} found")
                    else:
                        self.log_test("Session Creation Verification", "FAIL",
                                    f"Session data mismatch")
                elif response.status_code == 404:
                    self.log_test("Session Creation Verification", "FAIL",
                                f"Session {session_id} not found")
                else:
                    self.log_test("Session Creation Verification", "FAIL",
                                f"Unexpected status: {response.status_code}")
        except Exception as e:
            self.log_test("Session Creation Verification", "FAIL", str(e))
    
    async def test_session_management(self):
        """Test session management endpoints."""
        try:
            # List all sessions
            async with httpx.AsyncClient() as client:
                response = await client.get(self.sessions_url)
                
                if response.status_code == 200:
                    sessions = response.json()
                    self.log_test("List Sessions", "PASS", f"Found {len(sessions)} sessions")
                    
                    # Test session filtering
                    response = await client.get(f"{self.sessions_url}?status=active")
                    if response.status_code == 200:
                        active_sessions = response.json()
                        self.log_test("Filter Sessions by Status", "PASS", 
                                    f"Found {len(active_sessions)} active sessions")
                    else:
                        self.log_test("Filter Sessions by Status", "FAIL",
                                    f"Status: {response.status_code}")
                else:
                    self.log_test("List Sessions", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Session Management", "FAIL", str(e))
    
    async def test_session_cleanup(self):
        """Test manual session cleanup."""
        try:
            async with httpx.AsyncClient() as client:
                # Try to end a session
                response = await client.delete(f"{self.sessions_url}/test_call_001")
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("status") == "success":
                        self.log_test("Manual Session Cleanup", "PASS", "Session ended successfully")
                    else:
                        self.log_test("Manual Session Cleanup", "FAIL", f"Response: {data}")
                elif response.status_code == 404:
                    self.log_test("Manual Session Cleanup", "PASS", "Session not found (already cleaned)")
                else:
                    self.log_test("Manual Session Cleanup", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Manual Session Cleanup", "FAIL", str(e))
    
    async def test_ai_voice_mate_integration(self):
        """Test AI Voice Mate integration by monitoring WebSocket messages."""
        try:
            # Connect to AI Voice Mate to monitor messages
            websocket = await websockets.connect(self.ai_voice_mate_url)
            self.log_test("AI Voice Mate Connection", "PASS", "Connected successfully")
            
            # Send a webhook that should trigger AI interaction
            payload = {
                "call_id": "test_integration_001",
                "call_type": "inbound",
                "caller_number": "+1111111111",
                "called_number": "+2222222222", 
                "event_type": "call_received",
                "event_timestamp": datetime.now().isoformat(),
                "call_status": "ringing"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.webhook_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    # Wait for AI message (with timeout)
                    try:
                        await asyncio.sleep(2)  # Give time for processing
                        self.log_test("AI Integration Test", "PASS", 
                                    "Webhook processed and should have sent message to AI")
                    except asyncio.TimeoutError:
                        self.log_test("AI Integration Test", "WARN", 
                                    "No AI message received (may be normal)")
                else:
                    self.log_test("AI Integration Test", "FAIL", 
                                f"Webhook failed: {response.status_code}")
            
            await websocket.close()
            
        except Exception as e:
            self.log_test("AI Integration Test", "FAIL", str(e))
    
    async def test_concurrent_requests(self):
        """Test handling concurrent webhook requests."""
        try:
            # Create multiple concurrent webhook requests
            tasks = []
            for i in range(5):
                payload = {
                    "call_id": f"concurrent_test_{i:03d}",
                    "call_type": "inbound",
                    "caller_number": f"+123456789{i}",
                    "called_number": "+0987654321",
                    "event_type": "call_received", 
                    "event_timestamp": datetime.now().isoformat(),
                    "call_status": "ringing"
                }
                
                async def send_webhook(payload):
                    async with httpx.AsyncClient() as client:
                        return await client.post(
                            self.webhook_url,
                            json=payload,
                            headers={"Content-Type": "application/json"}
                        )
                
                tasks.append(send_webhook(payload))
            
            # Execute all requests concurrently
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            successful = sum(1 for r in responses 
                           if not isinstance(r, Exception) and r.status_code == 200)
            
            if successful == len(tasks):
                self.log_test("Concurrent Requests", "PASS", 
                            f"All {len(tasks)} requests processed successfully")
            else:
                self.log_test("Concurrent Requests", "FAIL",
                            f"Only {successful}/{len(tasks)} requests succeeded")
                
        except Exception as e:
            self.log_test("Concurrent Requests", "FAIL", str(e))
    
    async def run_all_tests(self):
        """Run all test cases."""
        print("🚀 Starting Smartflo AI Caller comprehensive tests...\n")
        
        # Basic connectivity tests
        await self.test_service_startup()
        await self.test_health_endpoint()
        await self.test_metrics_endpoint()
        
        print("\n📋 Testing webhook endpoints...")
        # Webhook tests
        await self.test_webhook_call_received()
        await self.test_webhook_call_answered_ivr()
        await self.test_webhook_dtmf_received()
        await self.test_webhook_call_hangup()
        
        print("\n❌ Testing error scenarios...")
        # Error handling tests
        await self.test_webhook_validation_errors()
        
        print("\n👥 Testing session management...")
        # Session management tests
        await self.test_session_management()
        await self.test_session_cleanup()
        
        print("\n🔗 Testing AI integration...")
        # AI integration tests
        await self.test_ai_voice_mate_integration()
        
        print("\n⚡ Testing performance...")
        # Performance tests
        await self.test_concurrent_requests()
        
        # Print summary
        self.print_test_summary()
    
    def print_test_summary(self):
        """Print test results summary."""
        print("\n" + "="*60)
        print("📊 TEST RESULTS SUMMARY")
        print("="*60)
        
        passed = sum(1 for r in self.test_results if r["status"] == "PASS")
        failed = sum(1 for r in self.test_results if r["status"] == "FAIL")
        warnings = sum(1 for r in self.test_results if r["status"] == "WARN")
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"⚠️  Warnings: {warnings}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if failed > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if result["status"] == "FAIL":
                    print(f"  - {result['test']}: {result['details']}")
        
        if warnings > 0:
            print("\n⚠️  WARNINGS:")
            for result in self.test_results:
                if result["status"] == "WARN":
                    print(f"  - {result['test']}: {result['details']}")
        
        print("\n" + "="*60)
        
        # Save detailed results to file
        with open("test_results.json", "w") as f:
            json.dump(self.test_results, f, indent=2)
        print("💾 Detailed results saved to test_results.json")


async def main():
    """Main test function."""
    print("🔍 Checking if Smartflo AI Caller service is running...")
    
    # Wait for service to be ready
    max_retries = 30
    for i in range(max_retries):
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:8000/")
                if response.status_code == 200:
                    print("✅ Service is ready!")
                    break
        except:
            pass
        
        print(f"⏳ Waiting for service... ({i+1}/{max_retries})")
        await asyncio.sleep(2)
    else:
        print("❌ Service not ready after 60 seconds. Please start the service first.")
        print("Run: source .venv/bin/activate && python -m smartflo_ai_caller.main")
        return
    
    # Run tests
    tester = SmartfloAICallerTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())