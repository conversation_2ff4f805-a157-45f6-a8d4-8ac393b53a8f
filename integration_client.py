#!/usr/bin/env python3
"""
Direct integration client for testing with the actual smartflo-ai-caller system.
This client uses the existing infrastructure directly rather than through WebSocket connections.
"""

import asyncio
import json
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, Optional
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from smartflo_ai_caller.audio_streaming import AudioStreamManager, StreamSession
from smartflo_ai_caller.bidirectional_ws_handler import BiDirectionalWSHandler
from smartflo_ai_caller.audio_utils import AudioChunk, AudioProcessor
from smartflo_ai_caller.ai_voice_client import AIVoiceClient
from smartflo_ai_caller.utils.logging import get_logger

logger = get_logger(__name__)


class DirectIntegrationClient:
    """Direct integration with smartflo-ai-caller components."""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
        # Initialize core components
        self.audio_stream_manager = AudioStreamManager()
        self.ws_handler = BiDirectionalWSHandler(self.audio_stream_manager)
        self.ai_voice_client = None
        
        # Session tracking
        self.active_sessions: Dict[str, StreamSession] = {}
        
        # Setup callbacks
        self._setup_callbacks()
    
    def _setup_callbacks(self):
        """Setup callbacks for audio and event forwarding."""
        # Register audio forwarding callback
        self.audio_stream_manager.register_audio_forward_callback(
            self._forward_audio_to_ai
        )
        
        # Register DTMF forwarding callback
        self.audio_stream_manager.register_dtmf_forward_callback(
            self._forward_dtmf_to_ai
        )
        
        # Register mark synchronization callback
        self.audio_stream_manager.register_mark_sync_callback(
            self._handle_mark_sync
        )
    
    async def initialize_ai_connection(self):
        """Initialize connection to AI Voice Mate."""
        try:
            self.ai_voice_client = AIVoiceClient()
            await self.ai_voice_client.connect()
            self.logger.info("AI Voice Client connected successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to connect to AI Voice Client: {e}")
            return False
    
    async def start_call_session(
        self, 
        caller: str, 
        callee: str, 
        custom_parameters: Optional[Dict[str, Any]] = None
    ) -> str:
        """Start a new call session with direct integration."""
        try:
            # Generate unique IDs
            call_id = f"CA{uuid.uuid4().hex[:30]}"
            stream_id = f"MZ{uuid.uuid4().hex[:30]}"
            account_id = f"AC{uuid.uuid4().hex[:30]}"
            
            # Create stream session using actual AudioStreamManager
            session = await self.audio_stream_manager.create_stream_session(
                stream_id=stream_id,
                account_id=account_id,
                call_id=call_id,
                from_number=caller,
                to_number=callee,
                custom_parameters=custom_parameters or {
                    "TestCall": "true",
                    "DirectIntegration": "true",
                    "Timestamp": datetime.now().isoformat()
                }
            )
            
            # Start audio stream using BiDirectionalWSHandler
            await self.ws_handler.start_audio_stream(
                stream_id=stream_id,
                account_id=account_id,
                call_id=call_id,
                from_number=caller,
                to_number=callee,
                custom_parameters=session.custom_parameters
            )
            
            # Store session
            self.active_sessions[call_id] = session
            
            # Initialize AI session if available
            if self.ai_voice_client:
                await self.ai_voice_client.start_session(
                    caller=caller,
                    callee=callee,
                    call_id=call_id
                )
            
            self.logger.info(f"Call session started: {call_id}, Stream: {stream_id}")
            
            return call_id
            
        except Exception as e:
            self.logger.error(f"Failed to start call session: {e}")
            raise
    
    async def send_audio_chunk(self, call_id: str, audio_data: bytes) -> bool:
        """Send audio chunk through the actual system."""
        try:
            session = self.active_sessions.get(call_id)
            if not session:
                self.logger.error(f"No session found for call: {call_id}")
                return False
            
            # Send audio using BiDirectionalWSHandler
            chunks_sent = await self.ws_handler.send_audio_to_vendors(
                session.stream_id, 
                audio_data
            )
            
            self.logger.debug(f"Sent {chunks_sent} audio chunks for call {call_id}")
            
            # Also forward to AI if available
            if self.ai_voice_client:
                await self.ai_voice_client.send_audio(call_id, audio_data)
            
            return chunks_sent > 0
            
        except Exception as e:
            self.logger.error(f"Failed to send audio chunk: {e}")
            return False
    
    async def send_mark(self, call_id: str, mark_name: str) -> bool:
        """Send mark event through the actual system."""
        try:
            session = self.active_sessions.get(call_id)
            if not session:
                self.logger.error(f"No session found for call: {call_id}")
                return False
            
            # Send mark using BiDirectionalWSHandler
            success = await self.ws_handler.send_mark_to_vendors(
                session.stream_id,
                mark_name
            )
            
            self.logger.info(f"Sent mark '{mark_name}' for call {call_id}: {success}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to send mark: {e}")
            return False
    
    async def send_clear_buffer(self, call_id: str) -> bool:
        """Send clear buffer event through the actual system."""
        try:
            session = self.active_sessions.get(call_id)
            if not session:
                self.logger.error(f"No session found for call: {call_id}")
                return False
            
            # Send clear using BiDirectionalWSHandler
            success = await self.ws_handler.send_clear_to_vendors(session.stream_id)
            
            self.logger.info(f"Sent clear buffer for call {call_id}: {success}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to send clear buffer: {e}")
            return False
    
    async def end_call_session(self, call_id: str, reason: str = "User ended call") -> bool:
        """End call session using actual system."""
        try:
            session = self.active_sessions.get(call_id)
            if not session:
                self.logger.error(f"No session found for call: {call_id}")
                return False
            
            # Stop audio stream using BiDirectionalWSHandler
            success = await self.ws_handler.stop_audio_stream(
                session.stream_id,
                reason
            )
            
            # End AI session if available
            if self.ai_voice_client:
                await self.ai_voice_client.end_session(call_id)
            
            # Remove from active sessions
            del self.active_sessions[call_id]
            
            self.logger.info(f"Call session ended: {call_id}, Reason: {reason}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to end call session: {e}")
            return False
    
    async def get_session_statistics(self, call_id: str) -> Optional[Dict[str, Any]]:
        """Get session statistics."""
        try:
            session = self.active_sessions.get(call_id)
            if not session:
                return None
            
            # Get statistics from AudioStreamManager
            stream_stats = await self.audio_stream_manager.get_session_stats()
            
            # Get connection statistics from BiDirectionalWSHandler
            connection_stats = await self.ws_handler.get_connection_stats()
            
            # Combine statistics
            return {
                "call_id": call_id,
                "session": session.to_dict(),
                "stream_stats": stream_stats,
                "connection_stats": connection_stats,
                "duration": (datetime.now() - session.created_at).total_seconds()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get session statistics: {e}")
            return None
    
    async def _forward_audio_to_ai(self, stream_id: str, pcm_data: bytes, audio_chunk: AudioChunk):
        """Forward audio to AI Voice Mate."""
        try:
            if not self.ai_voice_client:
                return
            
            # Find call_id from stream_id
            call_id = None
            for cid, session in self.active_sessions.items():
                if session.stream_id == stream_id:
                    call_id = cid
                    break
            
            if call_id:
                await self.ai_voice_client.send_audio(call_id, pcm_data)
                self.logger.debug(f"Forwarded audio to AI for call {call_id}")
            
        except Exception as e:
            self.logger.error(f"Error forwarding audio to AI: {e}")
    
    async def _forward_dtmf_to_ai(self, stream_id: str, digit: str):
        """Forward DTMF to AI Voice Mate."""
        try:
            if not self.ai_voice_client:
                return
            
            # Find call_id from stream_id
            call_id = None
            for cid, session in self.active_sessions.items():
                if session.stream_id == stream_id:
                    call_id = cid
                    break
            
            if call_id:
                await self.ai_voice_client.send_dtmf(call_id, digit)
                self.logger.info(f"Forwarded DTMF '{digit}' to AI for call {call_id}")
            
        except Exception as e:
            self.logger.error(f"Error forwarding DTMF to AI: {e}")
    
    async def _handle_mark_sync(self, stream_id: str, mark_name: str):
        """Handle mark synchronization."""
        try:
            # Find call_id from stream_id
            call_id = None
            for cid, session in self.active_sessions.items():
                if session.stream_id == stream_id:
                    call_id = cid
                    break
            
            if call_id:
                self.logger.info(f"Mark synchronized: '{mark_name}' for call {call_id}")
                
                # Notify about mark completion if needed
                # This could trigger callbacks to the Flask application
            
        except Exception as e:
            self.logger.error(f"Error handling mark sync: {e}")
    
    async def shutdown(self):
        """Shutdown the integration client."""
        try:
            # End all active sessions
            for call_id in list(self.active_sessions.keys()):
                await self.end_call_session(call_id, "System shutdown")
            
            # Shutdown components
            if self.ai_voice_client:
                await self.ai_voice_client.disconnect()
            
            await self.ws_handler.shutdown()
            
            self.logger.info("Direct integration client shutdown completed")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")


class TestScenarios:
    """Test scenarios for direct integration."""
    
    def __init__(self, client: DirectIntegrationClient):
        self.client = client
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    async def test_basic_call_flow(self):
        """Test basic call flow: start -> audio -> mark -> end."""
        self.logger.info("Starting basic call flow test")
        
        try:
            # Start call
            call_id = await self.client.start_call_session(
                caller="+1234567890",
                callee="+0987654321",
                custom_parameters={"test_scenario": "basic_call_flow"}
            )
            
            # Wait a bit for setup
            await asyncio.sleep(1)
            
            # Send some audio chunks
            for i in range(5):
                # Create dummy audio data (160 bytes for 20ms at 8kHz)
                audio_data = b'\x7f' * 160  # μ-law silence
                success = await self.client.send_audio_chunk(call_id, audio_data)
                self.logger.info(f"Audio chunk {i+1} sent: {success}")
                await asyncio.sleep(0.02)  # 20ms delay
            
            # Send mark
            mark_success = await self.client.send_mark(call_id, "test_mark_1")
            self.logger.info(f"Mark sent: {mark_success}")
            
            # Wait for mark processing
            await asyncio.sleep(0.5)
            
            # Get statistics
            stats = await self.client.get_session_statistics(call_id)
            if stats:
                self.logger.info(f"Session statistics: {json.dumps(stats, indent=2, default=str)}")
            
            # End call
            end_success = await self.client.end_call_session(call_id, "Test completed")
            self.logger.info(f"Call ended: {end_success}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Basic call flow test failed: {e}")
            return False
    
    async def test_pdf_compliance(self):
        """Test PDF specification compliance."""
        self.logger.info("Starting PDF compliance test")
        
        try:
            # Start call
            call_id = await self.client.start_call_session(
                caller="+1111111111",
                callee="+2222222222",
                custom_parameters={
                    "test_scenario": "pdf_compliance",
                    "encoding": "audio/x-mulaw",
                    "sample_rate": 8000,
                    "bit_rate": 64,
                    "bit_depth": 8
                }
            )
            
            # Test sequence: connected -> start -> media -> mark -> clear -> stop
            
            # Send media chunks
            self.logger.info("Testing media events (audio chunks)")
            for chunk_num in range(10):
                # Proper 20ms chunk at 8kHz (160 bytes)
                audio_data = bytes([
                    (chunk_num * 10 + i) % 256 for i in range(160)
                ])  # Varying μ-law data
                
                success = await self.client.send_audio_chunk(call_id, audio_data)
                if not success:
                    self.logger.error(f"Failed to send chunk {chunk_num}")
                
                await asyncio.sleep(0.020)  # Exact 20ms timing
            
            # Test mark synchronization
            self.logger.info("Testing mark events")
            for mark_num in range(3):
                mark_name = f"compliance_mark_{mark_num}"
                success = await self.client.send_mark(call_id, mark_name)
                if not success:
                    self.logger.error(f"Failed to send mark {mark_name}")
                
                await asyncio.sleep(0.1)  # Wait between marks
            
            # Test clear buffer
            self.logger.info("Testing clear buffer event")
            clear_success = await self.client.send_clear_buffer(call_id)
            if not clear_success:
                self.logger.error("Failed to send clear buffer")
            
            await asyncio.sleep(0.5)  # Wait for clear processing
            
            # Get final statistics
            stats = await self.client.get_session_statistics(call_id)
            if stats:
                self.logger.info("PDF Compliance Test Statistics:")
                session_data = stats.get("session", {})
                self.logger.info(f"  Encoding: {session_data.get('encoding')}")
                self.logger.info(f"  Sample Rate: {session_data.get('sample_rate')}")
                self.logger.info(f"  Bit Rate: {session_data.get('bit_rate')}")
                self.logger.info(f"  Bit Depth: {session_data.get('bit_depth')}")
                self.logger.info(f"  Chunks Sent: {session_data.get('metrics', {}).get('chunks_sent', 0)}")
            
            # End call
            end_success = await self.client.end_call_session(call_id, "PDF compliance test completed")
            self.logger.info(f"PDF compliance test ended: {end_success}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"PDF compliance test failed: {e}")
            return False
    
    async def test_ai_integration(self):
        """Test AI Voice Mate integration."""
        self.logger.info("Starting AI integration test")
        
        try:
            # Initialize AI connection
            ai_connected = await self.client.initialize_ai_connection()
            if not ai_connected:
                self.logger.warning("AI connection failed - testing without AI")
                return False
            
            # Start call with AI
            call_id = await self.client.start_call_session(
                caller="+3333333333",
                callee="+4444444444",
                custom_parameters={
                    "test_scenario": "ai_integration",
                    "ai_enabled": True
                }
            )
            
            # Send audio that simulates speech
            self.logger.info("Sending speech-like audio to AI")
            
            # Create audio pattern that simulates speech
            for i in range(50):  # 1 second of audio (50 * 20ms)
                # Create varying audio data to simulate speech
                speech_pattern = []
                for j in range(160):
                    # Simple sine-like pattern with variation
                    sample = int(127 + 100 * (i / 50) * (j / 160))
                    speech_pattern.append(sample % 256)
                
                audio_data = bytes(speech_pattern)
                await self.client.send_audio_chunk(call_id, audio_data)
                await asyncio.sleep(0.020)
            
            # Send mark to indicate end of speech
            await self.client.send_mark(call_id, "speech_end")
            
            # Wait for AI processing
            await asyncio.sleep(2)
            
            # Get final statistics
            stats = await self.client.get_session_statistics(call_id)
            if stats:
                self.logger.info(f"AI Integration Test Statistics: {json.dumps(stats, indent=2, default=str)}")
            
            # End call
            await self.client.end_call_session(call_id, "AI integration test completed")
            
            return True
            
        except Exception as e:
            self.logger.error(f"AI integration test failed: {e}")
            return False


async def main():
    """Main test function."""
    logging.basicConfig(level=logging.INFO)
    
    print("🎙️ Starting Direct Integration Test")
    print("🔗 Using actual smartflo-ai-caller components")
    print("=" * 50)
    
    # Create integration client
    client = DirectIntegrationClient()
    
    try:
        # Initialize AI connection
        print("📋 Initializing AI connection...")
        await client.initialize_ai_connection()
        
        # Create test scenarios
        scenarios = TestScenarios(client)
        
        # Run tests
        print("\n🧪 Running Test Scenarios...")
        
        print("\n1. Basic Call Flow Test")
        success1 = await scenarios.test_basic_call_flow()
        print(f"   Result: {'✅ PASSED' if success1 else '❌ FAILED'}")
        
        print("\n2. PDF Compliance Test")
        success2 = await scenarios.test_pdf_compliance()
        print(f"   Result: {'✅ PASSED' if success2 else '❌ FAILED'}")
        
        print("\n3. AI Integration Test")
        success3 = await scenarios.test_ai_integration()
        print(f"   Result: {'✅ PASSED' if success3 else '❌ FAILED'}")
        
        # Summary
        total_tests = 3
        passed_tests = sum([success1, success2, success3])
        
        print(f"\n📊 Test Summary: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("🎉 All tests passed! System integration is working correctly.")
        else:
            print("⚠️ Some tests failed. Please check the logs for details.")
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
    
    finally:
        # Clean shutdown
        print("\n🔄 Shutting down...")
        await client.shutdown()
        print("✅ Shutdown completed")


if __name__ == "__main__":
    asyncio.run(main())