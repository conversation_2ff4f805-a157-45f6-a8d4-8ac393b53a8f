#!/usr/bin/env python3
"""
Test script for bi-directional audio streaming implementation.
This script validates the core functionality without requiring external connections.
"""

import asyncio
import base64
import json
import sys
import os
from typing import Dict, Any

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from smartflo_ai_caller.audio_streaming import AudioStreamManager, StreamSession
from smartflo_ai_caller.audio_utils import AudioCodec, AudioProcessor, AudioChunk
from smartflo_ai_caller.bidirectional_ws_handler import BiDirectionalWSHandler


class MockAIClient:
    """Mock AI client for testing."""
    
    def __init__(self):
        self.is_connected = True
        self.received_audio = []
        self.received_dtmf = []
    
    async def send_audio_data(self, stream_id: str, pcm_data: bytes) -> None:
        """Mock audio data sending."""
        self.received_audio.append({
            'stream_id': stream_id,
            'data_length': len(pcm_data)
        })
        print(f"✓ AI Client received {len(pcm_data)} bytes of audio for stream {stream_id}")
    
    async def send_dtmf_input(self, stream_id: str, digit: str) -> None:
        """Mock DTMF sending."""
        self.received_dtmf.append({
            'stream_id': stream_id,
            'digit': digit
        })
        print(f"✓ AI Client received DTMF digit '{digit}' for stream {stream_id}")
    
    async def clear_audio_buffer(self, stream_id: str) -> None:
        """Mock buffer clearing."""
        print(f"✓ AI Client cleared audio buffer for stream {stream_id}")


async def test_audio_codec():
    """Test audio codec functionality."""
    print("\n=== Testing Audio Codec ===")
    
    codec = AudioCodec()
    
    # Test with sample PCM data (16-bit signed integers)
    pcm_data = bytes(range(0, 256, 2)) + bytes(range(0, 256, 2))  # 256 bytes
    
    try:
        # Encode PCM to μ-law
        mulaw_data = codec.encode_pcm_to_mulaw(pcm_data)
        print(f"✓ Encoded {len(pcm_data)} bytes PCM to {len(mulaw_data)} bytes μ-law")
        
        # Decode μ-law back to PCM
        decoded_pcm = codec.decode_mulaw_to_pcm(mulaw_data)
        print(f"✓ Decoded {len(mulaw_data)} bytes μ-law to {len(decoded_pcm)} bytes PCM")
        
        # Test base64 encoding
        b64_data = codec.encode_to_base64(pcm_data)
        print(f"✓ Encoded to base64: {len(b64_data)} characters")
        
        # Test base64 decoding
        decoded_from_b64 = codec.decode_from_base64(b64_data)
        print(f"✓ Decoded from base64: {len(decoded_from_b64)} bytes")
        
        return True
        
    except Exception as e:
        print(f"✗ Audio codec test failed: {e}")
        return False


async def test_audio_streaming():
    """Test audio streaming functionality."""
    print("\n=== Testing Audio Streaming ===")
    
    try:
        # Create components
        audio_manager = AudioStreamManager()
        mock_ai = MockAIClient()
        
        # Setup callbacks
        async def forward_audio(stream_id: str, pcm_data: bytes, audio_chunk) -> None:
            await mock_ai.send_audio_data(stream_id, pcm_data)
        
        async def forward_dtmf(stream_id: str, digit: str) -> None:
            await mock_ai.send_dtmf_input(stream_id, digit)
        
        async def handle_mark(stream_id: str, mark_name: str) -> None:
            print(f"✓ Mark event handled: {mark_name} for stream {stream_id}")
        
        audio_manager.register_audio_forward_callback(forward_audio)
        audio_manager.register_dtmf_forward_callback(forward_dtmf)
        audio_manager.register_mark_sync_callback(handle_mark)
        
        # Create test session
        stream_id = "test_stream_123"
        session = await audio_manager.create_stream_session(
            stream_id=stream_id,
            account_id="TEST_ACCOUNT",
            call_id="TEST_CALL",
            from_number="+**********",
            to_number="+**********",
            custom_parameters={"test": "true"}
        )
        print(f"✓ Created stream session: {stream_id}")
        
        # Test connected event
        connected_event = await audio_manager.create_connected_event()
        assert connected_event["event"] == "connected"
        print("✓ Connected event created")
        
        # Test start event
        start_event = await audio_manager.create_start_event(session)
        assert start_event["event"] == "start"
        assert start_event["streamSid"] == stream_id
        print("✓ Start event created")
        
        # Test media event with sample audio
        codec = AudioCodec()
        sample_pcm = bytes(range(0, 160, 2)) * 2  # 160 bytes of sample data
        mulaw_data = codec.encode_pcm_to_mulaw(sample_pcm)
        
        audio_chunk = AudioChunk(
            chunk_number=1,
            timestamp=100,
            payload=mulaw_data,
            sequence_number=1,
            stream_id=stream_id
        )
        
        media_event = await audio_manager.create_media_event(session, audio_chunk)
        assert media_event["event"] == "media"
        assert media_event["streamSid"] == stream_id
        print("✓ Media event created")
        
        # Test inbound media processing
        vendor_media_message = {
            "event": "media",
            "streamSid": stream_id,
            "sequenceNumber": "2",
            "media": {
                "chunk": "1",
                "timestamp": "200",
                "payload": base64.b64encode(mulaw_data).decode('utf-8')
            }
        }
        
        await audio_manager.process_inbound_message(vendor_media_message)
        print("✓ Inbound media processed and forwarded to AI")
        
        # Test DTMF event
        dtmf_message = {
            "event": "dtmf",
            "streamSid": stream_id,
            "sequenceNumber": "3",
            "dtmf": {"digit": "5"}
        }
        
        await audio_manager.process_inbound_message(dtmf_message)
        print("✓ DTMF event processed and forwarded to AI")
        
        # Test mark event
        mark_message = {
            "event": "mark",
            "streamSid": stream_id,
            "sequenceNumber": "4",
            "mark": {"name": "test_mark"}
        }
        
        await audio_manager.process_inbound_message(mark_message)
        print("✓ Mark event processed")
        
        # Test stop event
        stop_event = await audio_manager.create_stop_event(session, "Test completed")
        assert stop_event["event"] == "stop"
        print("✓ Stop event created")
        
        # Verify AI client received data
        assert len(mock_ai.received_audio) == 1
        assert len(mock_ai.received_dtmf) == 1
        print(f"✓ AI client received {len(mock_ai.received_audio)} audio chunks and {len(mock_ai.received_dtmf)} DTMF digits")
        
        return True
        
    except Exception as e:
        print(f"✗ Audio streaming test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_bidirectional_handler():
    """Test bidirectional WebSocket handler."""
    print("\n=== Testing Bi-directional Handler ===")
    
    try:
        # Create components
        audio_manager = AudioStreamManager()
        ws_handler = BiDirectionalWSHandler(audio_manager)
        
        # Test stream start
        session = await ws_handler.start_audio_stream(
            stream_id="test_ws_stream",
            account_id="TEST_WS_ACCOUNT",
            call_id="TEST_WS_CALL",
            from_number="+**********",
            to_number="+**********"
        )
        
        print(f"✓ Started audio stream via WebSocket handler: {session.stream_id}")
        
        # Test connection stats
        stats = await ws_handler.get_connection_stats()
        assert "streams" in stats
        print("✓ Connection stats retrieved")
        
        # Test audio sending (without actual WebSocket connections)
        sample_audio = bytes(range(256))
        try:
            # This will attempt to broadcast but should handle no connections gracefully
            chunks_sent = await ws_handler.send_audio_to_vendors("test_ws_stream", sample_audio)
            print(f"✓ Audio broadcast attempted (sent to {chunks_sent} vendors)")
        except Exception as e:
            print(f"✓ Audio broadcast handled connection absence: {type(e).__name__}")
        
        # Test stream stop
        success = await ws_handler.stop_audio_stream("test_ws_stream")
        print(f"✓ Stream stopped successfully: {success}")
        
        return True
        
    except Exception as e:
        print(f"✗ Bidirectional handler test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def run_all_tests():
    """Run all tests."""
    print("🎵 Starting Bi-directional Audio Streaming Tests 🎵")
    
    tests = [
        ("Audio Codec", test_audio_codec),
        ("Audio Streaming", test_audio_streaming),
        ("BiDirectional Handler", test_bidirectional_handler)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n✗ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("📊 TEST RESULTS SUMMARY")
    print(f"{'='*50}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Bi-directional audio streaming implementation is working correctly.")
        return True
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed. Please review the implementation.")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)