{"test_run_timestamp": "2025-07-26T07:55:28.738142", "total_categories": 8, "categories": {"Audio Format Tests": {"passed": true, "total_tests": 4, "passed_tests": 4, "tests": [{"name": "Sample Rate 8kHz", "passed": true, "details": "Generated 800 samples for 100ms at 8kHz"}, {"name": "μ-law Encoding", "passed": true, "details": "Converted 160 PCM samples to 160 μ-law bytes"}, {"name": "<PERSON><PERSON> (20ms)", "passed": true, "details": "20ms at 8kHz = 160 bytes"}, {"name": "Bit Depth (8-bit)", "passed": true, "details": "μ-law uses 8-bit encoding"}]}, "Message Format Tests": {"passed": true, "total_tests": 4, "passed_tests": 4, "tests": [{"name": "Connected Event Format", "passed": true, "details": "Connected event: {\"event\": \"connected\"}"}, {"name": "Start Event Format", "passed": true, "details": "Start event has all required fields: ['event', 'sequenceNumber', 'start', 'streamSid']"}, {"name": "Media Event Format", "passed": true, "details": "Media event has all required fields"}, {"name": "Stop Event Format", "passed": true, "details": "Stop event has all required fields"}]}, "Sequence Number Tests": {"passed": true, "total_tests": 2, "passed_tests": 2, "tests": [{"name": "Sequential Numbering", "passed": true, "details": "Sequence numbers: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]"}, {"name": "String Format", "passed": true, "details": "Sequence number type: <class 'str'>"}]}, "Timing Tests": {"passed": true, "total_tests": 2, "passed_tests": 2, "tests": [{"name": "Chunk Duration Calculation", "passed": true, "details": "20ms at 8000Hz = 160 samples"}, {"name": "Timestamp Incrementation", "passed": true, "details": "Timestamps: [20, 40, 60, 80, 100] (20ms steps)"}]}, "Audio Conversion Tests": {"passed": true, "total_tests": 3, "passed_tests": 3, "tests": [{"name": "PCM to μ-law Conversion", "passed": true, "details": "Converted 5 PCM samples to 5 μ-law bytes"}, {"name": "μ-law to PCM Conversion", "passed": true, "details": "Original: [0, 1000, -1000, 32767, -32768], Restored: [0, 988, -988, 32124, -32124]"}, {"name": "Audio Characteristic Preservation", "passed": true, "details": "Tone conversion: 320 → 160 → 320"}]}, "Base64 Encoding Tests": {"passed": true, "total_tests": 3, "passed_tests": 3, "tests": [{"name": "Base64 Encoding", "passed": true, "details": "Encoded 160 bytes to 216 base64 chars"}, {"name": "Base64 Decoding", "passed": true, "details": "Round trip successful: 160 bytes"}, {"name": "Media Event Payload", "passed": true, "details": "Payload can be base64 decoded: 216 chars"}]}, "DTMF Tests": {"passed": true, "total_tests": 13, "passed_tests": 13, "tests": [{"name": "DTMF Digit '0'", "passed": true, "details": "DTMF event for digit '0'"}, {"name": "DTMF Digit '1'", "passed": true, "details": "DTMF event for digit '1'"}, {"name": "DTMF Digit '2'", "passed": true, "details": "DTMF event for digit '2'"}, {"name": "DTMF Digit '3'", "passed": true, "details": "DTMF event for digit '3'"}, {"name": "DTMF Digit '4'", "passed": true, "details": "DTMF event for digit '4'"}, {"name": "DTMF Digit '5'", "passed": true, "details": "DTMF event for digit '5'"}, {"name": "DTMF Digit '6'", "passed": true, "details": "DTMF event for digit '6'"}, {"name": "DTMF Digit '7'", "passed": true, "details": "DTMF event for digit '7'"}, {"name": "DTMF Digit '8'", "passed": true, "details": "DTMF event for digit '8'"}, {"name": "DTMF Digit '9'", "passed": true, "details": "DTMF event for digit '9'"}, {"name": "DTMF Digit '*'", "passed": true, "details": "DTMF event for digit '*'"}, {"name": "DTMF Digit '#'", "passed": true, "details": "DTMF event for digit '#'"}, {"name": "DTMF Event Format", "passed": true, "details": "DTMF event has all required fields"}]}, "Mark Synchronization Tests": {"passed": true, "total_tests": 6, "passed_tests": 6, "tests": [{"name": "Mark Event Format", "passed": true, "details": "Mark event has all required fields"}, {"name": "Mark Name 'audio_end'", "passed": true, "details": "Mark name preserved: 'audio_end'"}, {"name": "Mark Name 'playback_complete'", "passed": true, "details": "Mark name preserved: 'playback_complete'"}, {"name": "<PERSON> 'sync_point_1'", "passed": true, "details": "Mark name preserved: 'sync_point_1'"}, {"name": "Mark Name 'mark_123'", "passed": true, "details": "Mark name preserved: 'mark_123'"}, {"name": "Clear Event Format", "passed": true, "details": "Clear event has required fields"}]}}, "overall_compliance": true, "summary": {"total_tests": 37, "passed_tests": 37, "failed_tests": 0, "pass_rate": 1.0, "compliance_status": "FULLY COMPLIANT"}}