"""Custom exceptions for the Smartflo AI Caller service."""

from typing import Any, Dict, Optional


class SmartfloAICallerException(Exception):
    """Base exception for Smartflo AI Caller service."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}


class WebhookValidationError(SmartfloAICallerException):
    """Raised when webhook payload validation fails."""
    
    def __init__(
        self,
        message: str = "Webhook payload validation failed",
        error_code: str = "WEBHOOK_VALIDATION_ERROR",
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message, error_code, details)


class AIVoiceMateConnectionError(SmartfloAICallerException):
    """Raised when connection to AI Voice Mate fails."""
    
    def __init__(
        self,
        message: str = "Failed to connect to AI Voice Mate",
        error_code: str = "AI_VOICE_MATE_CONNECTION_ERROR",
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message, error_code, details)


class AIVoiceMateTimeoutError(SmartfloAICallerException):
    """Raised when AI Voice Mate operation times out."""
    
    def __init__(
        self,
        message: str = "AI Voice Mate operation timed out",
        error_code: str = "AI_VOICE_MATE_TIMEOUT_ERROR",
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message, error_code, details)


class SessionNotFoundError(SmartfloAICallerException):
    """Raised when a session is not found."""
    
    def __init__(
        self,
        session_id: str,
        message: Optional[str] = None,
        error_code: str = "SESSION_NOT_FOUND",
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        if message is None:
            message = f"Session not found: {session_id}"
        details = details or {}
        details["session_id"] = session_id
        super().__init__(message, error_code, details)


class SessionExpiredError(SmartfloAICallerException):
    """Raised when a session has expired."""
    
    def __init__(
        self,
        session_id: str,
        message: Optional[str] = None,
        error_code: str = "SESSION_EXPIRED",
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        if message is None:
            message = f"Session expired: {session_id}"
        details = details or {}
        details["session_id"] = session_id
        super().__init__(message, error_code, details)


class MaxSessionsExceededError(SmartfloAICallerException):
    """Raised when maximum concurrent sessions limit is exceeded."""
    
    def __init__(
        self,
        max_sessions: int,
        current_sessions: int,
        message: Optional[str] = None,
        error_code: str = "MAX_SESSIONS_EXCEEDED",
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        if message is None:
            message = f"Maximum sessions exceeded: {current_sessions}/{max_sessions}"
        details = details or {}
        details.update({
            "max_sessions": max_sessions,
            "current_sessions": current_sessions
        })
        super().__init__(message, error_code, details)


class EventTransformationError(SmartfloAICallerException):
    """Raised when event transformation fails."""
    
    def __init__(
        self,
        event_type: str,
        message: Optional[str] = None,
        error_code: str = "EVENT_TRANSFORMATION_ERROR",
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        if message is None:
            message = f"Failed to transform event: {event_type}"
        details = details or {}
        details["event_type"] = event_type
        super().__init__(message, error_code, details)


class ConfigurationError(SmartfloAICallerException):
    """Raised when configuration is invalid."""
    
    def __init__(
        self,
        config_key: str,
        message: Optional[str] = None,
        error_code: str = "CONFIGURATION_ERROR",
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        if message is None:
            message = f"Invalid configuration: {config_key}"
        details = details or {}
        details["config_key"] = config_key
        super().__init__(message, error_code, details)


class RetryableError(SmartfloAICallerException):
    """Base class for errors that can be retried."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        retry_after: Optional[int] = None
    ) -> None:
        super().__init__(message, error_code, details)
        self.retry_after = retry_after


class NonRetryableError(SmartfloAICallerException):
    """Base class for errors that should not be retried."""
    pass