"""Logging utilities for the Smartflo AI Caller service."""

import logging
import logging.config
import os
import sys
from contextvars import Context<PERSON><PERSON>
from typing import Any, Dict, Optional
from uuid import uuid4

import yaml
from pythonjsonlogger import jsonlogger

from ..config import settings

# Context variable for correlation ID
correlation_id: ContextVar[Optional[str]] = ContextVar("correlation_id", default=None)


class CorrelationFilter(logging.Filter):
    """Add correlation ID to log records."""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Add correlation ID to the log record."""
        record.correlation_id = correlation_id.get()
        return True


class CustomJsonFormatter(jsonlogger.JsonFormatter):
    """Custom JSON formatter with additional fields."""
    
    def add_fields(
        self,
        log_record: Dict[str, Any],
        record: logging.LogRecord,
        message_dict: Dict[str, Any]
    ) -> None:
        """Add custom fields to log record."""
        super().add_fields(log_record, record, message_dict)
        
        # Add service information
        log_record["service"] = "smartflo-ai-caller"
        log_record["version"] = "1.0.0"
        
        # Add correlation ID if available
        if hasattr(record, "correlation_id") and record.correlation_id:
            log_record["correlation_id"] = record.correlation_id
        
        # Add additional context if available
        if hasattr(record, "session_id") and record.session_id:
            log_record["session_id"] = record.session_id
        
        if hasattr(record, "user_id") and record.user_id:
            log_record["user_id"] = record.user_id


def setup_logging() -> None:
    """Set up logging configuration."""
    # Create logs directory if it doesn't exist
    if settings.logging.file:
        log_dir = os.path.dirname(settings.logging.file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
    
    # Load logging configuration from YAML file
    config_path = "config/logging.yaml"
    if os.path.exists(config_path):
        with open(config_path, "r") as f:
            logging_config = yaml.safe_load(f)
        
        # Update configuration based on settings
        if settings.logging.format == "json":
            for handler in logging_config.get("handlers", {}).values():
                if handler.get("formatter") == "default":
                    handler["formatter"] = "json"
        
        # Update log level
        logging_config["root"]["level"] = settings.logging.level
        for logger_config in logging_config.get("loggers", {}).values():
            if "level" in logger_config:
                logger_config["level"] = settings.logging.level
        
        # Update file handler if configured
        if settings.logging.file and "file" in logging_config.get("handlers", {}):
            logging_config["handlers"]["file"]["filename"] = settings.logging.file
        
        logging.config.dictConfig(logging_config)
    else:
        # Fallback configuration
        setup_fallback_logging()
    
    # Add correlation filter to all handlers
    root_logger = logging.getLogger()
    correlation_filter = CorrelationFilter()
    for handler in root_logger.handlers:
        handler.addFilter(correlation_filter)


def setup_fallback_logging() -> None:
    """Set up fallback logging configuration."""
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, settings.logging.level))
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, settings.logging.level))
    
    if settings.logging.format == "json":
        formatter = CustomJsonFormatter(
            "%(asctime)s %(name)s %(levelname)s %(message)s"
        )
    else:
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
    
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler if configured
    if settings.logging.file:
        file_handler = logging.FileHandler(settings.logging.file)
        file_handler.setLevel(logging.INFO)
        
        json_formatter = CustomJsonFormatter(
            "%(asctime)s %(name)s %(levelname)s %(message)s"
        )
        file_handler.setFormatter(json_formatter)
        logger.addHandler(file_handler)


def get_logger(name: str) -> logging.Logger:
    """Get a logger with the specified name."""
    return logging.getLogger(name)


def set_correlation_id(cid: Optional[str] = None) -> str:
    """Set correlation ID for the current context."""
    if cid is None:
        cid = str(uuid4())
    correlation_id.set(cid)
    return cid


def get_correlation_id() -> Optional[str]:
    """Get the current correlation ID."""
    return correlation_id.get()


def log_with_context(
    logger: logging.Logger,
    level: int,
    message: str,
    session_id: Optional[str] = None,
    user_id: Optional[str] = None,
    **kwargs: Any
) -> None:
    """Log a message with additional context."""
    extra = {
        "session_id": session_id,
        "user_id": user_id,
        **kwargs
    }
    logger.log(level, message, extra=extra)