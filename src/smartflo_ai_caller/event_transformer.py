"""Event transformation logic between Smartflo and AI Voice Mate."""

from typing import Any, Dict, Optional, <PERSON><PERSON>
from datetime import datetime

from .utils.exceptions import EventTransformationError
from .utils.logging import get_logger, log_with_context

logger = get_logger(__name__)


class EventTransformer:
    """Transforms events between Smartflo webhooks and AI Voice Mate messages."""
    
    def __init__(self) -> None:
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    def transform_webhook_to_ai_actions(
        self, 
        webhook_event: Dict[str, Any]
    ) -> Tuple[Optional[Dict[str, Any]], Optional[Dict[str, Any]]]:
        """
        Transform webhook event to AI Voice Mate actions.
        
        Returns:
            Tuple of (user_registration_data, ai_action_data)
        """
        try:
            action = webhook_event.get("action")
            session_id = webhook_event.get("session_id")
            
            if not session_id:
                raise EventTransformationError(
                    "missing_session_id",
                    "Session ID is required for transformation",
                    details={"webhook_event": webhook_event}
                )
            
            log_with_context(
                self.logger,
                level=10,  # DEBUG
                message=f"Transforming webhook event: {action}",
                session_id=session_id,
                **{"action": action}
            )
            
            # Route to specific transformation method
            transformation_map = {
                "call_received": self._transform_call_received,
                "call_answered_ivr": self._transform_call_answered_ivr,
                "call_answered_agent": self._transform_call_answered_agent,
                "call_hangup": self._transform_call_hangup,
                "dtmf_received": self._transform_dtmf_received,
                "agent_status_change": self._transform_agent_status_change,
                "call_disposition": self._transform_call_disposition,
            }
            
            transformer = transformation_map.get(action) if action else None
            if not transformer:
                log_with_context(
                    self.logger,
                    level=30,  # WARNING
                    message=f"No transformer found for action: {action}",
                    session_id=session_id
                )
                return None, None
            
            result = transformer(webhook_event)
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message=f"Event transformation completed: {action}",
                session_id=session_id
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error transforming webhook event: {e}")
            raise EventTransformationError(
                webhook_event.get("action", "unknown"),
                f"Failed to transform webhook event: {e}",
                details={"webhook_event": webhook_event, "error": str(e)}
            )
    
    def _transform_call_received(
        self, 
        webhook_event: Dict[str, Any]
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Transform call_received event."""
        session_id = webhook_event["session_id"]
        caller_number = webhook_event.get("caller_number", "Unknown")
        called_number = webhook_event.get("called_number", "Unknown")
        
        # Generate user registration data
        user_data = self._create_user_registration_data(
            session_id=session_id,
            caller_number=caller_number,
            called_number=called_number,
            name=f"Caller {caller_number[-4:]}" if len(caller_number) >= 4 else "Unknown Caller"
        )
        
        # Generate AI action to start call
        ai_action = {
            "type": "start_call",
            "session_id": session_id,
            "caller_number": caller_number,
            "called_number": called_number,
            "timestamp": webhook_event.get("timestamp")
        }
        
        return user_data, ai_action
    
    def _transform_call_answered_ivr(
        self, 
        webhook_event: Dict[str, Any]
    ) -> Tuple[None, Dict[str, Any]]:
        """Transform call_answered_ivr event."""
        session_id = webhook_event["session_id"]
        
        # IVR answered - start AI interaction
        ai_action = {
            "type": "start_ai_interaction",
            "session_id": session_id,
            "caller_number": webhook_event.get("caller_number"),
            "timestamp": webhook_event.get("timestamp"),
            "greeting_message": "Hello! Thank you for calling. How can I help you today?"
        }
        
        return None, ai_action
    
    def _transform_call_answered_agent(
        self, 
        webhook_event: Dict[str, Any]
    ) -> Tuple[None, Dict[str, Any]]:
        """Transform call_answered_agent event."""
        session_id = webhook_event["session_id"]
        agent_id = webhook_event.get("agent_id")
        
        # Agent answered - update AI context
        ai_action = {
            "type": "agent_takeover",
            "session_id": session_id,
            "agent_id": agent_id,
            "caller_number": webhook_event.get("caller_number"),
            "timestamp": webhook_event.get("timestamp"),
            "context_message": f"Call transferred to agent {agent_id}"
        }
        
        return None, ai_action
    
    def _transform_call_hangup(
        self, 
        webhook_event: Dict[str, Any]
    ) -> Tuple[None, Dict[str, Any]]:
        """Transform call_hangup event."""
        session_id = webhook_event["session_id"]
        call_duration = webhook_event.get("call_duration")
        
        # End AI call
        ai_action = {
            "type": "end_call",
            "session_id": session_id,
            "call_duration": call_duration,
            "timestamp": webhook_event.get("timestamp")
        }
        
        return None, ai_action
    
    def _transform_dtmf_received(
        self, 
        webhook_event: Dict[str, Any]
    ) -> Tuple[None, Dict[str, Any]]:
        """Transform dtmf_received event."""
        session_id = webhook_event["session_id"]
        dtmf_digits = webhook_event.get("dtmf_digits", "")
        
        # Send DTMF as text input to AI
        ai_action = {
            "type": "dtmf_input",
            "session_id": session_id,
            "dtmf_digits": dtmf_digits,
            "text_equivalent": self._dtmf_to_text(dtmf_digits),
            "timestamp": webhook_event.get("timestamp")
        }
        
        return None, ai_action
    
    def _transform_agent_status_change(
        self, 
        webhook_event: Dict[str, Any]
    ) -> Tuple[None, Optional[Dict[str, Any]]]:
        """Transform agent_status_change event."""
        session_id = webhook_event["session_id"]
        agent_id = webhook_event.get("agent_id")
        call_status = webhook_event.get("call_status")
        
        # Only create action if status change affects AI interaction
        if call_status in ["busy", "unavailable", "offline"]:
            ai_action = {
                "type": "agent_unavailable",
                "session_id": session_id,
                "agent_id": agent_id,
                "status": call_status,
                "timestamp": webhook_event.get("timestamp"),
                "fallback_message": "I'll help you while we try to connect you with an agent."
            }
            return None, ai_action
        
        return None, None
    
    def _transform_call_disposition(
        self, 
        webhook_event: Dict[str, Any]
    ) -> Tuple[None, Dict[str, Any]]:
        """Transform call_disposition event."""
        session_id = webhook_event["session_id"]
        recording_url = webhook_event.get("recording_url")
        
        # Final cleanup action
        ai_action = {
            "type": "call_disposition",
            "session_id": session_id,
            "recording_url": recording_url,
            "timestamp": webhook_event.get("timestamp")
        }
        
        return None, ai_action
    
    def _create_user_registration_data(
        self,
        session_id: str,
        caller_number: str,
        called_number: str,
        name: str
    ) -> Dict[str, Any]:
        """Create user registration data for AI Voice Mate."""
        return {
            "session_id": session_id,
            "name": name,
            "mobile": caller_number,
            "userId": session_id,
            "called_number": called_number,
            "sentences": [],
            "sessionType": "call",
            "target": "english_tutor",
            "registration_timestamp": datetime.now().isoformat()
        }
    
    def _dtmf_to_text(self, dtmf_digits: str) -> str:
        """Convert DTMF digits to text representation."""
        dtmf_map = {
            "0": "zero",
            "1": "one", 
            "2": "two",
            "3": "three",
            "4": "four",
            "5": "five",
            "6": "six",
            "7": "seven",
            "8": "eight",
            "9": "nine",
            "*": "star",
            "#": "hash"
        }
        
        if not dtmf_digits:
            return "no digits pressed"
        
        # Convert each digit
        text_parts = []
        for digit in dtmf_digits:
            text_parts.append(dtmf_map.get(digit, f"unknown digit {digit}"))
        
        if len(text_parts) == 1:
            return f"User pressed {text_parts[0]}"
        else:
            return f"User pressed {', '.join(text_parts[:-1])} and {text_parts[-1]}"
    
    def create_ai_greeting_message(
        self,
        context: Optional[str] = None
    ) -> str:
        """Create personalized greeting message for AI."""
        base_greeting = "Hello! Thank you for calling."
        
        # Add context if available
        if context:
            return f"{base_greeting} {context} How can I help you today?"
        
        return f"{base_greeting} How can I help you today?"
    
    def create_error_response_message(
        self,
        error_type: str,
        session_id: str,
        caller_friendly: bool = True
    ) -> str:
        """Create error response message."""
        if caller_friendly:
            friendly_messages = {
                "connection_error": "I'm having trouble connecting right now. Please hold on.",
                "timeout_error": "I'm taking a moment to process. Please wait.",
                "transformation_error": "Let me try that again.",
                "session_error": "I'm having trouble with your session. Let me restart.",
                "default": "I'm experiencing a technical issue. Please hold on."
            }
            return friendly_messages.get(error_type, friendly_messages["default"])
        else:
            return f"Error in session {session_id}: {error_type}"