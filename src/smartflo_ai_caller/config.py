"""Configuration management for Smartflo AI Caller service."""

import os
from typing import Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class AIVoiceMateConfig(BaseSettings):
    """Configuration for AI Voice Mate WebSocket connection."""
    
    ws_url: str = Field(default="ws://localhost:5010", env="AI_VOICE_MATE_WS_URL")
    connection_timeout: int = Field(default=30, env="AI_VOICE_MATE_CONNECTION_TIMEOUT")
    retry_attempts: int = Field(default=3, env="AI_VOICE_MATE_RETRY_ATTEMPTS")
    retry_delay: int = Field(default=5, env="AI_VOICE_MATE_RETRY_DELAY")


class WebhookConfig(BaseSettings):
    """Configuration for webhook handling."""
    
    path: str = Field(default="/webhook/smartflo", env="WEBHOOK_PATH")
    secret: Optional[str] = Field(default=None, env="WEBHOOK_SECRET")


class ServerConfig(BaseSettings):
    """Server configuration."""
    
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    debug: bool = Field(default=False, env="DEBUG")
    environment: str = Field(default="production", env="ENVIRONMENT")


class LoggingConfig(BaseSettings):
    """Logging configuration."""
    
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(default="json", env="LOG_FORMAT")
    file: Optional[str] = Field(default=None, env="LOG_FILE")
    
    @validator("level")
    def validate_log_level(cls, v: str) -> str:
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()
    
    @validator("format")
    def validate_log_format(cls, v: str) -> str:
        """Validate log format."""
        valid_formats = ["json", "text"]
        if v.lower() not in valid_formats:
            raise ValueError(f"Log format must be one of: {valid_formats}")
        return v.lower()


class SessionConfig(BaseSettings):
    """Session management configuration."""
    
    timeout: int = Field(default=300, env="SESSION_TIMEOUT")
    max_concurrent_sessions: int = Field(default=100, env="MAX_CONCURRENT_SESSIONS")


class HealthCheckConfig(BaseSettings):
    """Health check configuration."""
    
    enabled: bool = Field(default=True, env="HEALTH_CHECK_ENABLED")
    path: str = Field(default="/health", env="HEALTH_CHECK_PATH")


class MetricsConfig(BaseSettings):
    """Metrics configuration."""
    
    enabled: bool = Field(default=True, env="METRICS_ENABLED")
    path: str = Field(default="/metrics", env="METRICS_PATH")


class Settings(BaseSettings):
    """Main application settings."""
    
    server: ServerConfig = Field(default_factory=ServerConfig)
    ai_voice_mate: AIVoiceMateConfig = Field(default_factory=AIVoiceMateConfig)
    webhook: WebhookConfig = Field(default_factory=WebhookConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    session: SessionConfig = Field(default_factory=SessionConfig)
    health_check: HealthCheckConfig = Field(default_factory=HealthCheckConfig)
    metrics: MetricsConfig = Field(default_factory=MetricsConfig)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"


def get_settings() -> Settings:
    """Get application settings."""
    return Settings()


# Global settings instance
settings = get_settings()