"""Audio streaming manager for bi-directional audio streaming."""

import asyncio
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable, AsyncGenerator
from dataclasses import dataclass, field
from enum import Enum

from .audio_utils import (
    AudioChunk, AudioCodec, AudioBuffer, AudioProcessor, 
    DTMFProcessor, AudioStreamMetrics
)
from .mark_synchronizer import MarkSynchronizer, PendingMark
from .utils.logging import get_logger, log_with_context
from .utils.exceptions import SmartfloAICallerException

logger = get_logger(__name__)


class StreamState(Enum):
    """Audio stream states."""
    INITIALIZING = "initializing"
    CONNECTED = "connected"
    STARTED = "started"
    STREAMING = "streaming"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class StreamSession:
    """Represents an active audio stream session."""
    
    stream_id: str
    account_id: str
    call_id: str
    from_number: str
    to_number: str
    state: StreamState = StreamState.INITIALIZING
    sequence_number: int = 1
    chunk_number: int = 1
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None
    custom_parameters: Dict[str, Any] = field(default_factory=dict)
    
    # Audio format (from specification)
    encoding: str = "audio/x-mulaw"
    sample_rate: int = 8000
    bit_rate: int = 64
    bit_depth: int = 8
    
    # Metrics
    metrics: AudioStreamMetrics = field(default_factory=AudioStreamMetrics)
    
    def next_sequence(self) -> int:
        """Get next sequence number and increment."""
        current = self.sequence_number
        self.sequence_number += 1
        return current
    
    def get_current_sequence(self) -> int:
        """Get current sequence number without incrementing."""
        return self.sequence_number
    
    def next_chunk(self) -> int:
        """Get next chunk number."""
        current = self.chunk_number
        self.chunk_number += 1
        return current
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "stream_id": self.stream_id,
            "account_id": self.account_id,
            "call_id": self.call_id,
            "from_number": self.from_number,
            "to_number": self.to_number,
            "state": self.state.value,
            "sequence_number": self.sequence_number,
            "chunk_number": self.chunk_number,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "ended_at": self.ended_at.isoformat() if self.ended_at else None,
            "custom_parameters": self.custom_parameters,
            "encoding": self.encoding,
            "sample_rate": self.sample_rate,
            "bit_rate": self.bit_rate,
            "bit_depth": self.bit_depth,
            "metrics": self.metrics.get_stats()
        }


class AudioStreamManager:
    """Manages bi-directional audio streaming sessions."""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self.sessions: Dict[str, StreamSession] = {}
        self.session_lock = asyncio.Lock()
        
        # Components - Use vendor-specific settings (8kHz, μ-law, 8-bit)
        self.audio_processor = AudioProcessor(sample_rate=8000, channels=1, chunk_duration_ms=20)
        self.dtmf_processor = DTMFProcessor(sample_rate=8000)
        self.mark_synchronizer = MarkSynchronizer()
        
        # Event handlers
        self.message_handlers: Dict[str, Callable] = {}
        
        # Audio forwarding callbacks
        self.audio_forward_callback: Optional[Callable] = None
        self.dtmf_forward_callback: Optional[Callable] = None
        self.mark_sync_callback: Optional[Callable] = None
        
        # Register default handlers
        self._register_default_handlers()
    
    def _register_default_handlers(self) -> None:
        """Register default message handlers."""
        self.register_handler("connected", self._handle_connected)
        self.register_handler("start", self._handle_start)
        self.register_handler("media", self._handle_media)
        self.register_handler("stop", self._handle_stop)
        self.register_handler("dtmf", self._handle_dtmf)
        self.register_handler("mark", self._handle_mark)
        self.register_handler("clear", self._handle_clear)
    
    def register_handler(self, event_type: str, handler: Callable) -> None:
        """Register handler for specific event type."""
        self.message_handlers[event_type] = handler
        self.logger.debug(f"Registered handler for event: {event_type}")
    
    def register_audio_forward_callback(self, callback: Callable) -> None:
        """Register callback for forwarding audio to external system (e.g., AI Voice Mate)."""
        self.audio_forward_callback = callback
        self.logger.debug("Registered audio forward callback")
    
    def register_dtmf_forward_callback(self, callback: Callable) -> None:
        """Register callback for forwarding DTMF to external system."""
        self.dtmf_forward_callback = callback
        self.logger.debug("Registered DTMF forward callback")
    
    def register_mark_sync_callback(self, callback: Callable) -> None:
        """Register callback for mark synchronization."""
        self.mark_sync_callback = callback
        self.logger.debug("Registered mark sync callback")
    
    async def create_stream_session(
        self,
        stream_id: str,
        account_id: str,
        call_id: str,
        from_number: str,
        to_number: str,
        custom_parameters: Optional[Dict[str, Any]] = None
    ) -> StreamSession:
        """Create new audio stream session."""
        async with self.session_lock:
            if stream_id in self.sessions:
                existing = self.sessions[stream_id]
                log_with_context(
                    self.logger,
                    level=30,  # WARNING
                    message=f"Stream session already exists: {stream_id}",
                    stream_id=stream_id,
                    **{"existing_state": existing.state.value}
                )
                return existing
            
            session = StreamSession(
                stream_id=stream_id,
                account_id=account_id,
                call_id=call_id,
                from_number=from_number,
                to_number=to_number,
                custom_parameters=custom_parameters or {}
            )
            
            self.sessions[stream_id] = session
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Created audio stream session",
                stream_id=stream_id,
                **{
                    "account_id": account_id,
                    "call_id": call_id,
                    "from_number": from_number,
                    "to_number": to_number
                }
            )
            
            return session
    
    async def get_session(self, stream_id: str) -> Optional[StreamSession]:
        """Get stream session by ID."""
        async with self.session_lock:
            return self.sessions.get(stream_id)
    
    async def end_stream_session(self, stream_id: str, reason: str = "Normal termination") -> bool:
        """End audio stream session."""
        async with self.session_lock:
            session = self.sessions.get(stream_id)
            if not session:
                return False
            
            session.state = StreamState.STOPPED
            session.ended_at = datetime.now()
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Ended audio stream session",
                stream_id=stream_id,
                **{"reason": reason, "duration": (session.ended_at - session.created_at).total_seconds()}
            )
            
            return True
    
    async def create_connected_event(self) -> Dict[str, Any]:
        """Create connected event message."""
        return {"event": "connected"}
    
    async def create_start_event(self, session: StreamSession) -> Dict[str, Any]:
        """Create start event message exactly matching PDF specification."""
        return {
            "event": "start",
            "sequenceNumber": str(session.next_sequence()),
            "start": {
                "streamSid": session.stream_id,
                "accountSid": session.account_id,
                "callSid": session.call_id,
                "from": session.from_number,
                "to": session.to_number,
                "mediaFormat": {
                    "encoding": session.encoding,
                    "sampleRate": session.sample_rate,
                    "bitRate": session.bit_rate,
                    "bitDepth": session.bit_depth
                },
                "customParameters": session.custom_parameters
            },
            "streamSid": session.stream_id
        }
    
    async def create_media_event(self, session: StreamSession, audio_chunk: AudioChunk) -> Dict[str, Any]:
        """Create media event message exactly matching PDF specification."""
        return {
            "event": "media",
            "sequenceNumber": str(session.next_sequence()),
            "media": {
                "chunk": str(audio_chunk.chunk_number),
                "timestamp": str(audio_chunk.timestamp),
                "payload": audio_chunk.to_base64()
            },
            "streamSid": session.stream_id
        }
    
    async def create_stop_event(self, session: StreamSession, reason: str = "Normal termination") -> Dict[str, Any]:
        """Create stop event message exactly matching PDF specification."""
        return {
            "event": "stop",
            "sequenceNumber": str(session.next_sequence()),
            "stop": {
                "accountSid": session.account_id,
                "callSid": session.call_id,
                "reason": reason
            },
            "streamSid": session.stream_id
        }
    
    async def create_dtmf_event(self, session: StreamSession, digit: str) -> Dict[str, Any]:
        """Create DTMF event message exactly matching PDF specification."""
        return {
            "event": "dtmf",
            "streamSid": session.stream_id,
            "sequenceNumber": str(session.next_sequence()),
            "dtmf": {
                "digit": digit
            }
        }
    
    async def create_mark_event(self, session: StreamSession, mark_name: str) -> Dict[str, Any]:
        """Create mark event message exactly matching PDF specification."""
        return {
            "event": "mark",
            "sequenceNumber": str(session.next_sequence()),
            "streamSid": session.stream_id,
            "mark": {
                "name": mark_name
            }
        }
    
    async def create_clear_event(self, session: StreamSession) -> Dict[str, Any]:
        """Create clear event message exactly matching PDF specification."""
        return {
            "event": "clear",
            "streamSid": session.stream_id
        }
    
    async def send_mark_with_sync(self, session: StreamSession, mark_name: str, callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Send mark event and register it for synchronization."""
        mark_event = await self.create_mark_event(session, mark_name)
        
        # Register mark for synchronization
        await self.mark_synchronizer.add_pending_mark(
            stream_id=session.stream_id,
            mark_name=mark_name,
            sequence_number=session.sequence_number - 1,  # Already incremented in create_mark_event
            callback=callback,
            timeout_seconds=30
        )
        
        log_with_context(
            self.logger,
            level=10,  # DEBUG
            message="Mark sent with synchronization tracking",
            stream_id=session.stream_id,
            **{"mark_name": mark_name}
        )
        
        return mark_event
    
    async def process_inbound_message(self, message: Dict[str, Any]) -> None:
        """Process inbound message from vendor."""
        try:
            event_type = message.get("event")
            if not event_type:
                self.logger.error("No event type in inbound message")
                return
            
            stream_id = message.get("streamSid")
            if not stream_id:
                self.logger.error(f"No streamSid in {event_type} message")
                return
            
            log_with_context(
                self.logger,
                level=10,  # DEBUG
                message=f"Processing inbound {event_type} message",
                stream_id=stream_id
            )
            
            # Get handler for event type
            handler = self.message_handlers.get(event_type)
            if handler:
                await handler(message)
            else:
                log_with_context(
                    self.logger,
                    level=30,  # WARNING
                    message=f"No handler for event type: {event_type}",
                    stream_id=stream_id
                )
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error processing inbound message: {e}",
                **{"error": str(e)}
            )
    
    async def stream_audio_chunks(
        self, 
        session: StreamSession, 
        audio_data: bytes
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream audio data as chunks with proper vendor format compliance."""
        if not audio_data:
            log_with_context(
                self.logger,
                level=30,  # WARNING
                message="No audio data to stream",
                stream_id=session.stream_id
            )
            return
            
        try:
            session.state = StreamState.STREAMING
            
            # Ensure audio data is in proper format for vendor (8kHz, μ-law)
            formatted_audio = await self._prepare_vendor_audio(audio_data, session)
            
            chunk_count = 0
            failed_chunks = 0
            max_consecutive_failures = 5
            consecutive_failures = 0
            
            async for audio_chunk in self.audio_processor.chunk_audio_stream(
                formatted_audio, session.stream_id, session.sequence_number
            ):
                try:
                    # Update chunk number
                    audio_chunk.chunk_number = session.next_chunk()
                    
                    # Ensure chunk is exactly in μ-law format as per specification
                    if not self._is_mulaw_format(audio_chunk.payload):
                        # Convert to μ-law if not already
                        pcm_data = audio_chunk.payload
                        mulaw_data = self.audio_processor.codec.encode_pcm_to_mulaw(pcm_data)
                        audio_chunk.payload = mulaw_data
                    
                    # Create media event
                    media_event = await self.create_media_event(session, audio_chunk)
                    
                    # Update metrics
                    session.metrics.record_chunk_sent(audio_chunk)
                    
                    chunk_count += 1
                    consecutive_failures = 0  # Reset on success
                    
                    yield media_event
                    
                    # Add precise timing for 20ms chunks (as per specification)
                    await asyncio.sleep(0.02)  # 20ms between chunks
                    
                except Exception as chunk_error:
                    failed_chunks += 1
                    consecutive_failures += 1
                    
                    log_with_context(
                        self.logger,
                        level=40,  # ERROR
                        message=f"Error processing audio chunk: {chunk_error}",
                        stream_id=session.stream_id,
                        **{"chunk_number": audio_chunk.chunk_number if hasattr(audio_chunk, 'chunk_number') else 'unknown'}
                    )
                    
                    # Stop if too many consecutive failures
                    if consecutive_failures >= max_consecutive_failures:
                        log_with_context(
                            self.logger,
                            level=40,  # ERROR
                            message=f"Too many consecutive failures ({consecutive_failures}), stopping stream",
                            stream_id=session.stream_id
                        )
                        break
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Completed audio streaming with vendor format compliance",
                stream_id=session.stream_id,
                **{
                    "chunks_sent": chunk_count,
                    "failed_chunks": failed_chunks,
                    "success_rate": (chunk_count / max(chunk_count + failed_chunks, 1)) * 100,
                    "audio_format": f"{session.encoding}, {session.sample_rate}Hz, {session.bit_depth}-bit"
                }
            )
            
        except Exception as e:
            session.state = StreamState.ERROR
            session.metrics.record_error()
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error streaming audio: {e}",
                stream_id=session.stream_id
            )
            raise AudioStreamException(f"Audio streaming failed: {e}", session.stream_id)
    
    async def _prepare_vendor_audio(self, audio_data: bytes, session: StreamSession) -> bytes:
        """Prepare audio data for vendor streaming (8kHz, μ-law, 8-bit)."""
        try:
            # If input is not at 8kHz, resample it
            if hasattr(self, 'input_sample_rate') and self.input_sample_rate != 8000:
                audio_data = self.audio_processor.resample_audio(audio_data, self.input_sample_rate, 8000)
            
            # Ensure audio is in PCM format before μ-law encoding
            # The audio_processor will handle PCM to μ-law conversion during chunking
            
            log_with_context(
                self.logger,
                level=10,  # DEBUG
                message="Prepared audio for vendor streaming",
                stream_id=session.stream_id,
                **{
                    "input_bytes": len(audio_data),
                    "target_format": f"{session.encoding}, {session.sample_rate}Hz, {session.bit_depth}-bit"
                }
            )
            
            return audio_data
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error preparing vendor audio: {e}",
                stream_id=session.stream_id
            )
            raise AudioStreamException(f"Failed to prepare vendor audio: {e}", session.stream_id)
    
    def _is_mulaw_format(self, audio_data: bytes) -> bool:
        """Check if audio data is in μ-law format (simple heuristic)."""
        # μ-law data has characteristic patterns - this is a simple check
        # In production, you might want more sophisticated detection
        return len(audio_data) > 0 and all(0 <= b <= 255 for b in audio_data[:10])
    
    # Event handlers
    async def _handle_connected(self, message: Dict[str, Any]) -> None:
        """Handle connected event from vendor."""
        self.logger.info("Received connected event from vendor")
    
    async def _handle_start(self, message: Dict[str, Any]) -> None:
        """Handle start event from vendor."""
        stream_id = message.get("streamSid")
        start_data = message.get("start", {})
        
        session = await self.get_session(stream_id)
        if session:
            session.state = StreamState.STARTED
            session.started_at = datetime.now()
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Stream started by vendor",
                stream_id=stream_id,
                **{"account_sid": start_data.get("accountSid")}
            )
    
    async def _handle_media(self, message: Dict[str, Any]) -> None:
        """Handle media event from vendor."""
        stream_id = message.get("streamSid")
        media_data = message.get("media", {})
        
        session = await self.get_session(stream_id)
        if not session:
            self.logger.error(f"No session found for media: {stream_id}")
            return
        
        try:
            # Create audio chunk from inbound media
            chunk_number = int(media_data.get("chunk", 1))
            timestamp = int(media_data.get("timestamp", 0))
            payload_b64 = media_data.get("payload", "")
            
            audio_chunk = AudioChunk.from_base64(
                chunk_number=chunk_number,
                timestamp=timestamp,
                payload_b64=payload_b64,
                sequence_number=int(message.get("sequenceNumber", 1)),
                stream_id=stream_id
            )
            
            # Process audio chunk
            pcm_data = await self.audio_processor.process_inbound_audio(audio_chunk)
            
            # Update metrics
            session.metrics.record_chunk_received(audio_chunk)
            
            # Forward PCM data to AI Voice Mate or application
            if self.audio_forward_callback:
                try:
                    await self.audio_forward_callback(stream_id, pcm_data, audio_chunk)
                except Exception as e:
                    log_with_context(
                        self.logger,
                        level=40,  # ERROR
                        message=f"Error in audio forward callback: {e}",
                        stream_id=stream_id,
                        **{"chunk_number": chunk_number}
                    )
            
            log_with_context(
                self.logger,
                level=10,  # DEBUG
                message="Processed inbound media chunk",
                stream_id=stream_id,
                **{
                    "chunk_number": chunk_number,
                    "timestamp": timestamp,
                    "payload_size": len(audio_chunk.payload)
                }
            )
            
        except Exception as e:
            session.metrics.record_error()
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error processing media chunk: {e}",
                stream_id=stream_id
            )
    
    async def _handle_stop(self, message: Dict[str, Any]) -> None:
        """Handle stop event from vendor."""
        stream_id = message.get("streamSid")
        stop_data = message.get("stop", {})
        reason = stop_data.get("reason", "Vendor initiated stop")
        
        await self.end_stream_session(stream_id, reason)
        
        log_with_context(
            self.logger,
            level=20,  # INFO
            message="Stream stopped by vendor",
            stream_id=stream_id,
            **{"reason": reason}
        )
    
    async def _handle_dtmf(self, message: Dict[str, Any]) -> None:
        """Handle DTMF event from vendor."""
        stream_id = message.get("streamSid")
        dtmf_data = message.get("dtmf", {})
        digit = dtmf_data.get("digit")
        
        session = await self.get_session(stream_id)
        if session:
            session.metrics.record_dtmf_detection(digit)
        
        log_with_context(
            self.logger,
            level=20,  # INFO
            message="DTMF digit received from vendor",
            stream_id=stream_id,
            **{"digit": digit}
        )
        
        # Forward DTMF to application
        if self.dtmf_forward_callback:
            try:
                await self.dtmf_forward_callback(stream_id, digit)
            except Exception as e:
                log_with_context(
                    self.logger,
                    level=40,  # ERROR
                    message=f"Error in DTMF forward callback: {e}",
                    stream_id=stream_id,
                    **{"digit": digit}
                )
    
    async def _handle_mark(self, message: Dict[str, Any]) -> None:
        """Handle mark event from vendor - indicates audio playback completion."""
        stream_id = message.get("streamSid")
        mark_data = message.get("mark", {})
        mark_name = mark_data.get("name")
        
        # Complete the mark in synchronizer (indicates audio has finished playing)
        completed = await self.mark_synchronizer.complete_mark(stream_id, mark_name)
        
        log_with_context(
            self.logger,
            level=20,  # INFO
            message="Mark completion received from vendor",
            stream_id=stream_id,
            **{
                "mark_name": mark_name,
                "was_synchronized": completed
            }
        )
        
        # Handle additional mark synchronization logic
        if self.mark_sync_callback:
            try:
                await self.mark_sync_callback(stream_id, mark_name)
            except Exception as e:
                log_with_context(
                    self.logger,
                    level=40,  # ERROR
                    message=f"Error in mark sync callback: {e}",
                    stream_id=stream_id,
                    **{"mark_name": mark_name, "error": str(e)}
                )
    
    async def _handle_clear(self, message: Dict[str, Any]) -> None:
        """Handle clear event from vendor - clears buffers and sends mark responses."""
        stream_id = message.get("streamSid")
        
        session = await self.get_session(stream_id)
        if session:
            # Clear audio buffers
            await self.audio_processor.inbound_buffer.clear()
            await self.audio_processor.outbound_buffer.clear()
            
            # Clear and complete all pending marks for this stream
            cleared_marks = await self.mark_synchronizer.clear_stream_marks(stream_id)
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Audio buffers and marks cleared by vendor",
                stream_id=stream_id,
                **{
                    "cleared_marks_count": len(cleared_marks),
                    "cleared_mark_names": [mark.mark_name for mark in cleared_marks]
                }
            )
            
            # Send mark completion events back to vendor for cleared marks
            for mark in cleared_marks:
                mark_event = await self.create_mark_event(session, mark.mark_name)
                
                # Call mark sync callback to send the mark back to vendor
                if self.mark_sync_callback:
                    try:
                        await self.mark_sync_callback(stream_id, mark_event)
                    except Exception as e:
                        log_with_context(
                            self.logger,
                            level=40,  # ERROR
                            message=f"Error sending cleared mark to vendor: {e}",
                            stream_id=stream_id,
                            **{"mark_name": mark.mark_name, "error": str(e)}
                        )
    
    async def get_session_stats(self) -> Dict[str, Any]:
        """Get statistics for all sessions."""
        async with self.session_lock:
            total_sessions = len(self.sessions)
            active_sessions = len([s for s in self.sessions.values() 
                                 if s.state not in [StreamState.STOPPED, StreamState.ERROR]])
            
            states = {}
            for session in self.sessions.values():
                state = session.state.value
                states[state] = states.get(state, 0) + 1
            
            return {
                "total_sessions": total_sessions,
                "active_sessions": active_sessions,
                "session_states": states,
                "sessions": [s.to_dict() for s in self.sessions.values()]
            }
    
    async def cleanup_ended_sessions(self, max_age_hours: int = 24) -> int:
        """Clean up old ended sessions."""
        async with self.session_lock:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            
            to_remove = []
            for stream_id, session in self.sessions.items():
                if (session.state in [StreamState.STOPPED, StreamState.ERROR] and
                    session.ended_at and session.ended_at < cutoff_time):
                    to_remove.append(stream_id)
            
            for stream_id in to_remove:
                del self.sessions[stream_id]
            
            if to_remove:
                self.logger.info(f"Cleaned up {len(to_remove)} old sessions")
            
            return len(to_remove)


class AudioStreamException(SmartfloAICallerException):
    """Audio streaming specific exception."""
    
    def __init__(self, message: str, stream_id: str = None, details: Dict[str, Any] = None):
        super().__init__(message, "audio_stream_error", details or {})
        self.stream_id = stream_id