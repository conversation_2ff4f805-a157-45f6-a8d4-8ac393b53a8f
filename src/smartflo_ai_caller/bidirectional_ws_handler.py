"""Bi-directional WebSocket handler for audio streaming with vendors."""

import asyncio
import json
import websockets
from typing import Dict, Any, Optional, Callable, Set
from datetime import datetime
from websockets.exceptions import ConnectionClosed, InvalidURI
from contextlib import asynccontextmanager

from .audio_streaming import AudioStreamManager, StreamSession, StreamState, AudioStreamException
from .audio_utils import AudioChunk
from .utils.logging import get_logger, log_with_context
from .utils.exceptions import SmartfloAICallerException

logger = get_logger(__name__)


class BiDirectionalWSHandler:
    """Handles bi-directional WebSocket connections for audio streaming."""
    
    def __init__(self, audio_stream_manager: AudioStreamManager):
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self.audio_stream_manager = audio_stream_manager
        
        # Connection management
        self.vendor_connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.connection_lock = asyncio.Lock()
        
        # Event handlers for vendor connections
        self.vendor_event_handlers: Dict[str, Callable] = {}
        
        # Statistics
        self.connection_stats = {
            "total_connections": 0,
            "active_connections": 0,
            "messages_sent": 0,
            "messages_received": 0,
            "errors": 0
        }
    
    def register_vendor_handler(self, event_type: str, handler: Callable) -> None:
        """Register handler for vendor-specific events."""
        self.vendor_event_handlers[event_type] = handler
        self.logger.debug(f"Registered vendor handler for event: {event_type}")
    
    async def handle_vendor_connection(self, websocket: websockets.WebSocketServerProtocol, path: str) -> None:
        """Handle incoming vendor WebSocket connection."""
        connection_id = f"vendor_{id(websocket)}"
        
        async with self.connection_lock:
            self.vendor_connections[connection_id] = websocket
            self.connection_stats["total_connections"] += 1
            self.connection_stats["active_connections"] += 1
        
        log_with_context(
            self.logger,
            level=20,  # INFO
            message="Vendor WebSocket connection established",
            connection_id=connection_id,
            **{"path": path, "remote_address": websocket.remote_address}
        )
        
        try:
            # Send initial connected event
            connected_event = await self.audio_stream_manager.create_connected_event()
            await self._send_to_vendor(websocket, connected_event)
            
            # Handle messages from vendor
            async for message in websocket:
                await self._handle_vendor_message(websocket, message, connection_id)
                
        except ConnectionClosed:
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Vendor WebSocket connection closed",
                connection_id=connection_id
            )
        except Exception as e:
            self.connection_stats["errors"] += 1
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error in vendor connection: {e}",
                connection_id=connection_id,
                **{"error": str(e)}
            )
        finally:
            async with self.connection_lock:
                self.vendor_connections.pop(connection_id, None)
                self.connection_stats["active_connections"] -= 1
    
    async def _handle_vendor_message(
        self, 
        websocket: websockets.WebSocketServerProtocol, 
        message_raw: str, 
        connection_id: str
    ) -> None:
        """Handle message from vendor."""
        try:
            message = json.loads(message_raw)
            self.connection_stats["messages_received"] += 1
            
            event_type = message.get("event")
            stream_id = message.get("streamSid")
            
            log_with_context(
                self.logger,
                level=10,  # DEBUG
                message=f"Received vendor message: {event_type}",
                connection_id=connection_id,
                stream_id=stream_id
            )
            
            # Process through audio stream manager
            await self.audio_stream_manager.process_inbound_message(message)
            
            # Call custom handler if registered
            if event_type in self.vendor_event_handlers:
                try:
                    await self.vendor_event_handlers[event_type](message, websocket, connection_id)
                except Exception as e:
                    log_with_context(
                        self.logger,
                        level=40,  # ERROR
                        message=f"Error in vendor event handler: {e}",
                        connection_id=connection_id,
                        **{"event_type": event_type, "error": str(e)}
                    )
            
        except json.JSONDecodeError as e:
            self.connection_stats["errors"] += 1
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Invalid JSON from vendor: {e}",
                connection_id=connection_id
            )
        except Exception as e:
            self.connection_stats["errors"] += 1
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error handling vendor message: {e}",
                connection_id=connection_id,
                **{"error": str(e)}
            )
    
    async def _send_to_vendor(self, websocket: websockets.WebSocketServerProtocol, message: Dict[str, Any]) -> None:
        """Send message to vendor."""
        try:
            message_json = json.dumps(message)
            await websocket.send(message_json)
            self.connection_stats["messages_sent"] += 1
            
            log_with_context(
                self.logger,
                level=10,  # DEBUG
                message=f"Sent message to vendor: {message.get('event')}",
                **{"message_size": len(message_json)}
            )
            
        except ConnectionClosed:
            self.logger.warning("Cannot send to vendor: connection closed")
        except Exception as e:
            self.connection_stats["errors"] += 1
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error sending to vendor: {e}",
                **{"error": str(e)}
            )
            raise
    
    async def broadcast_to_vendors(self, message: Dict[str, Any], stream_id: Optional[str] = None) -> int:
        """Broadcast message to all connected vendors or specific stream."""
        sent_count = 0
        
        async with self.connection_lock:
            connections = list(self.vendor_connections.values())
        
        for websocket in connections:
            try:
                await self._send_to_vendor(websocket, message)
                sent_count += 1
            except Exception as e:
                log_with_context(
                    self.logger,
                    level=40,  # ERROR
                    message=f"Failed to broadcast to vendor: {e}",
                    stream_id=stream_id
                )
        
        return sent_count
    
    async def start_audio_stream(
        self,
        stream_id: str,
        account_id: str,
        call_id: str,
        from_number: str,
        to_number: str,
        custom_parameters: Optional[Dict[str, Any]] = None
    ) -> StreamSession:
        """Start new audio stream with vendors."""
        try:
            # Create stream session
            session = await self.audio_stream_manager.create_stream_session(
                stream_id, account_id, call_id, from_number, to_number, custom_parameters
            )
            
            # Send start event to vendors
            start_event = await self.audio_stream_manager.create_start_event(session)
            await self.broadcast_to_vendors(start_event, stream_id)
            
            # Update session state
            session.state = StreamState.STARTED
            session.started_at = datetime.now()
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Started audio stream with vendors",
                stream_id=stream_id,
                **{
                    "account_id": account_id,
                    "call_id": call_id,
                    "vendor_count": len(self.vendor_connections)
                }
            )
            
            return session
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Failed to start audio stream: {e}",
                stream_id=stream_id,
                **{"error": str(e)}
            )
            raise AudioStreamException(f"Failed to start audio stream: {e}", stream_id)
    
    async def stop_audio_stream(self, stream_id: str, reason: str = "Normal termination") -> bool:
        """Stop audio stream with vendors."""
        try:
            session = await self.audio_stream_manager.get_session(stream_id)
            if not session:
                self.logger.warning(f"No session found for stream: {stream_id}")
                return False
            
            # Send stop event to vendors
            stop_event = await self.audio_stream_manager.create_stop_event(session, reason)
            await self.broadcast_to_vendors(stop_event, stream_id)
            
            # End session
            await self.audio_stream_manager.end_stream_session(stream_id, reason)
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Stopped audio stream with vendors",
                stream_id=stream_id,
                **{"reason": reason}
            )
            
            return True
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Failed to stop audio stream: {e}",
                stream_id=stream_id,
                **{"error": str(e)}
            )
            return False
    
    async def send_audio_to_vendors(self, stream_id: str, audio_data: bytes) -> int:
        """Send audio data to vendors."""
        try:
            session = await self.audio_stream_manager.get_session(stream_id)
            if not session:
                raise AudioStreamException(f"No session found for stream: {stream_id}", stream_id)
            
            chunks_sent = 0
            async for media_event in self.audio_stream_manager.stream_audio_chunks(session, audio_data):
                await self.broadcast_to_vendors(media_event, stream_id)
                chunks_sent += 1
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Sent audio to vendors",
                stream_id=stream_id,
                **{
                    "chunks_sent": chunks_sent,
                    "audio_bytes": len(audio_data),
                    "vendor_count": len(self.vendor_connections)
                }
            )
            
            return chunks_sent
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Failed to send audio to vendors: {e}",
                stream_id=stream_id,
                **{"error": str(e)}
            )
            raise AudioStreamException(f"Failed to send audio to vendors: {e}", stream_id)
    
    async def send_dtmf_to_vendors(self, stream_id: str, digit: str) -> bool:
        """Send DTMF digit to vendors."""
        try:
            session = await self.audio_stream_manager.get_session(stream_id)
            if not session:
                raise AudioStreamException(f"No session found for stream: {stream_id}", stream_id)
            
            # Create DTMF event
            dtmf_event = await self.audio_stream_manager.create_dtmf_event(session, digit)
            await self.broadcast_to_vendors(dtmf_event, stream_id)
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Sent DTMF to vendors",
                stream_id=stream_id,
                **{"digit": digit}
            )
            
            return True
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Failed to send DTMF to vendors: {e}",
                stream_id=stream_id,
                **{"digit": digit, "error": str(e)}
            )
            return False
    
    async def send_mark_to_vendors(self, stream_id: str, mark_name: str) -> bool:
        """Send mark event to vendors with synchronization tracking."""
        try:
            session = await self.audio_stream_manager.get_session(stream_id)
            if not session:
                raise AudioStreamException(f"No session found for stream: {stream_id}", stream_id)
            
            # Create mark event with synchronization
            mark_event = await self.audio_stream_manager.send_mark_with_sync(session, mark_name)
            await self.broadcast_to_vendors(mark_event, stream_id)
            
            log_with_context(
                self.logger,
                level=10,  # DEBUG
                message="Sent mark to vendors with synchronization",
                stream_id=stream_id,
                **{"mark_name": mark_name}
            )
            
            return True
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Failed to send mark to vendors: {e}",
                stream_id=stream_id,
                **{"mark_name": mark_name, "error": str(e)}
            )
            return False
    
    async def send_clear_to_vendors(self, stream_id: str) -> bool:
        """Send clear event to vendors to interrupt audio."""
        try:
            session = await self.audio_stream_manager.get_session(stream_id)
            if not session:
                raise AudioStreamException(f"No session found for stream: {stream_id}", stream_id)
            
            # Create clear event
            clear_event = await self.audio_stream_manager.create_clear_event(session)
            await self.broadcast_to_vendors(clear_event, stream_id)
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Sent clear event to vendors",
                stream_id=stream_id
            )
            
            return True
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Failed to send clear to vendors: {e}",
                stream_id=stream_id,
                **{"error": str(e)}
            )
            return False
    
    @asynccontextmanager
    async def vendor_connection_context(self, vendor_url: str):
        """Context manager for vendor WebSocket connections."""
        websocket = None
        try:
            websocket = await websockets.connect(vendor_url)
            
            # Send connected event
            connected_event = await self.audio_stream_manager.create_connected_event()
            await self._send_to_vendor(websocket, connected_event)
            
            yield websocket
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Vendor connection error: {e}",
                **{"vendor_url": vendor_url, "error": str(e)}
            )
            raise
        finally:
            if websocket:
                await websocket.close()
    
    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        async with self.connection_lock:
            active_connections = len(self.vendor_connections)
        
        stats = {
            **self.connection_stats,
            "active_connections": active_connections,
            "connected_vendors": list(self.vendor_connections.keys())
        }
        
        # Add stream statistics
        stream_stats = await self.audio_stream_manager.get_session_stats()
        stats["streams"] = stream_stats
        
        return stats
    
    async def cleanup_connections(self) -> None:
        """Clean up stale connections."""
        async with self.connection_lock:
            stale_connections = []
            
            for connection_id, websocket in self.vendor_connections.items():
                if websocket.closed:
                    stale_connections.append(connection_id)
            
            for connection_id in stale_connections:
                del self.vendor_connections[connection_id]
                self.connection_stats["active_connections"] -= 1
            
            if stale_connections:
                log_with_context(
                    self.logger,
                    level=20,  # INFO
                    message=f"Cleaned up {len(stale_connections)} stale connections"
                )
    
    async def shutdown(self) -> None:
        """Shutdown all connections."""
        async with self.connection_lock:
            connections = list(self.vendor_connections.values())
            self.vendor_connections.clear()
        
        # Close all vendor connections
        for websocket in connections:
            try:
                await websocket.close()
            except Exception as e:
                self.logger.error(f"Error closing vendor connection: {e}")
        
        # Clean up sessions
        await self.audio_stream_manager.cleanup_ended_sessions(0)  # Clean all
        
        log_with_context(
            self.logger,
            level=20,  # INFO
            message="BiDirectional WebSocket handler shutdown completed"
        )