"""Audio processing utilities for bi-directional audio streaming."""

import base64
import struct
import asyncio
import math
import time
from typing import List, Generator, Optional, Dict, Any, AsyncGenerator
from dataclasses import dataclass, field
from datetime import datetime

from .utils.logging import get_logger, log_with_context

logger = get_logger(__name__)


@dataclass
class AudioChunk:
    """Represents an audio chunk with metadata."""
    
    chunk_number: int
    timestamp: int  # Milliseconds from stream start
    payload: bytes
    sequence_number: int
    stream_id: str
    created_at: datetime = field(default_factory=datetime.now)
    
    def to_base64(self) -> str:
        """Convert payload to base64 string."""
        return base64.b64encode(self.payload).decode('utf-8')
    
    @classmethod
    def from_base64(cls, chunk_number: int, timestamp: int, payload_b64: str, 
                   sequence_number: int, stream_id: str) -> 'AudioChunk':
        """Create AudioChunk from base64 payload."""
        payload = base64.b64decode(payload_b64.encode('utf-8'))
        return cls(
            chunk_number=chunk_number,
            timestamp=timestamp,
            payload=payload,
            sequence_number=sequence_number,
            stream_id=stream_id
        )


class AudioCodec:
    """Audio codec for μ-law (G.711 PCMU) encoding/decoding."""
    
    # μ-law constants
    MULAW_BIAS = 0x84
    MULAW_CLIP = 0x7FFF
    MULAW_TABLE = None
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        if AudioCodec.MULAW_TABLE is None:
            AudioCodec.MULAW_TABLE = self._build_mulaw_table()
    
    @staticmethod
    def _build_mulaw_table() -> List[int]:
        """Build μ-law encoding table."""
        table = []
        for i in range(256):
            # Decode μ-law to linear PCM
            sign = (i & 0x80) >> 7
            exponent = (i & 0x70) >> 4
            mantissa = i & 0x0F
            
            if exponent == 0:
                sample = mantissa << 4
            else:
                sample = ((mantissa << 4) + 0x108) << (exponent - 1)
            
            if sign == 1:
                sample = -sample
                
            table.append(sample)
        return table
    
    def linear_to_mulaw(self, pcm_sample: int) -> int:
        """Convert linear PCM sample to μ-law."""
        # Clamp sample to valid range
        if pcm_sample > self.MULAW_CLIP:
            pcm_sample = self.MULAW_CLIP
        elif pcm_sample < -self.MULAW_CLIP:
            pcm_sample = -self.MULAW_CLIP
        
        # Get sign and magnitude
        sign = 0x80 if pcm_sample < 0 else 0x00
        if pcm_sample < 0:
            pcm_sample = -pcm_sample
        
        # Add bias
        pcm_sample += self.MULAW_BIAS
        
        # Find exponent and mantissa
        if pcm_sample > 0x7FFF:
            pcm_sample = 0x7FFF
            
        exponent = 7
        for exp_lut in [0x4000, 0x2000, 0x1000, 0x0800, 0x0400, 0x0200, 0x0100]:
            if pcm_sample < exp_lut:
                exponent -= 1
            else:
                break
        
        mantissa = (pcm_sample >> (exponent + 3)) & 0x0F
        mulaw_byte = ~(sign | (exponent << 4) | mantissa) & 0xFF
        
        return mulaw_byte
    
    def mulaw_to_linear(self, mulaw_byte: int) -> int:
        """Convert μ-law sample to linear PCM."""
        return self.MULAW_TABLE[mulaw_byte & 0xFF]
    
    def encode_pcm_to_mulaw(self, pcm_data: bytes) -> bytes:
        """Encode PCM data to μ-law format."""
        if not pcm_data:
            return b''
            
        try:
            if len(pcm_data) % 2 != 0:
                # Pad with zero byte if odd length
                pcm_data += b'\x00'
            
            # Unpack as signed 16-bit integers
            pcm_samples = struct.unpack(f'<{len(pcm_data)//2}h', pcm_data)
            
            # Convert each sample to μ-law
            mulaw_samples = [self.linear_to_mulaw(sample) for sample in pcm_samples]
            
            # Pack as unsigned bytes
            return struct.pack(f'{len(mulaw_samples)}B', *mulaw_samples)
            
        except struct.error as e:
            logger.error(f"Error encoding PCM to μ-law: {e}")
            raise ValueError(f"Invalid PCM data format: {e}")
        except Exception as e:
            logger.error(f"Unexpected error in PCM to μ-law encoding: {e}")
            raise
    
    def decode_mulaw_to_pcm(self, mulaw_data: bytes) -> bytes:
        """Decode μ-law data to PCM format."""
        if not mulaw_data:
            return b''
            
        try:
            # Unpack as unsigned bytes
            mulaw_samples = struct.unpack(f'{len(mulaw_data)}B', mulaw_data)
            
            # Convert each sample to linear PCM
            pcm_samples = [self.mulaw_to_linear(sample) for sample in mulaw_samples]
            
            # Pack as signed 16-bit integers
            return struct.pack(f'<{len(pcm_samples)}h', *pcm_samples)
            
        except struct.error as e:
            logger.error(f"Error decoding μ-law to PCM: {e}")
            raise ValueError(f"Invalid μ-law data format: {e}")
        except Exception as e:
            logger.error(f"Unexpected error in μ-law to PCM decoding: {e}")
            raise
    
    def encode_to_base64(self, pcm_data: bytes) -> str:
        """Encode PCM data to base64 μ-law format."""
        mulaw_data = self.encode_pcm_to_mulaw(pcm_data)
        return base64.b64encode(mulaw_data).decode('utf-8')
    
    def decode_from_base64(self, base64_data: str) -> bytes:
        """Decode base64 μ-law data to PCM format."""
        mulaw_data = base64.b64decode(base64_data.encode('utf-8'))
        return self.decode_mulaw_to_pcm(mulaw_data)


class AudioBuffer:
    """Thread-safe audio buffer for streaming."""
    
    def __init__(self, max_size: int = 8192):
        self.max_size = max_size
        self.buffer = bytearray()
        self.lock = asyncio.Lock()
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    async def write(self, data: bytes) -> None:
        """Write data to buffer."""
        if not data:
            return
            
        async with self.lock:
            try:
                # If buffer would exceed max size, remove oldest data
                if len(self.buffer) + len(data) > self.max_size:
                    overflow = len(self.buffer) + len(data) - self.max_size
                    self.buffer = self.buffer[overflow:]
                    self.logger.warning(f"Audio buffer overflow, dropped {overflow} bytes")
                
                self.buffer.extend(data)
                
            except Exception as e:
                self.logger.error(f"Error writing to audio buffer: {e}")
                raise
    
    async def read(self, size: int) -> bytes:
        """Read data from buffer."""
        async with self.lock:
            if len(self.buffer) < size:
                # Return what's available
                data = bytes(self.buffer)
                self.buffer.clear()
                return data
            else:
                data = bytes(self.buffer[:size])
                self.buffer = self.buffer[size:]
                return data
    
    async def peek(self, size: int) -> bytes:
        """Peek at data without removing it."""
        async with self.lock:
            return bytes(self.buffer[:size])
    
    async def size(self) -> int:
        """Get current buffer size."""
        async with self.lock:
            return len(self.buffer)
    
    async def clear(self) -> None:
        """Clear buffer."""
        async with self.lock:
            cleared_size = len(self.buffer)
            self.buffer.clear()
            if cleared_size > 0:
                self.logger.debug(f"Cleared audio buffer: {cleared_size} bytes")


class AudioProcessor:
    """Process audio for bi-directional streaming."""
    
    def __init__(self, sample_rate: int = 16000, channels: int = 1, chunk_duration_ms: int = 20):
        # Updated to use 16kHz as per Voice Call API specification
        self.sample_rate = sample_rate
        self.channels = channels
        self.chunk_duration_ms = chunk_duration_ms
        self.chunk_size_bytes = int((sample_rate * chunk_duration_ms / 1000) * 2 * channels)  # 16-bit samples
        
        # For μ-law compatibility (still 8kHz for vendor communication)
        self.vendor_sample_rate = 8000
        self.vendor_chunk_size_bytes = int((self.vendor_sample_rate * chunk_duration_ms / 1000) * 2 * channels)
        
        self.codec = AudioCodec()
        self.inbound_buffer = AudioBuffer()
        self.outbound_buffer = AudioBuffer()
        
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    async def process_inbound_audio(self, audio_chunk: AudioChunk) -> Optional[bytes]:
        """Process inbound audio chunk from vendor."""
        try:
            # Decode from μ-law to PCM
            pcm_data = self.codec.decode_mulaw_to_pcm(audio_chunk.payload)
            
            # Add to inbound buffer
            await self.inbound_buffer.write(pcm_data)
            
            log_with_context(
                self.logger,
                level=10,  # DEBUG
                message="Processed inbound audio chunk",
                stream_id=audio_chunk.stream_id,
                **{
                    "chunk_number": audio_chunk.chunk_number,
                    "pcm_bytes": len(pcm_data),
                    "timestamp": audio_chunk.timestamp
                }
            )
            
            return pcm_data
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error processing inbound audio: {e}",
                stream_id=audio_chunk.stream_id,
                **{"chunk_number": audio_chunk.chunk_number}
            )
            return None
    
    async def prepare_outbound_audio(self, pcm_data: bytes, stream_id: str, 
                                   chunk_number: int, timestamp: int, 
                                   sequence_number: int) -> AudioChunk:
        """Prepare PCM audio for outbound streaming."""
        try:
            # Encode PCM to μ-law
            mulaw_data = self.codec.encode_pcm_to_mulaw(pcm_data)
            
            # Create audio chunk
            chunk = AudioChunk(
                chunk_number=chunk_number,
                timestamp=timestamp,
                payload=mulaw_data,
                sequence_number=sequence_number,
                stream_id=stream_id
            )
            
            log_with_context(
                self.logger,
                level=10,  # DEBUG
                message="Prepared outbound audio chunk",
                stream_id=stream_id,
                **{
                    "chunk_number": chunk_number,
                    "mulaw_bytes": len(mulaw_data),
                    "timestamp": timestamp
                }
            )
            
            return chunk
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error preparing outbound audio: {e}",
                stream_id=stream_id,
                **{"chunk_number": chunk_number}
            )
            raise
    
    async def chunk_audio_stream(self, pcm_data: bytes, stream_id: str, 
                                start_sequence: int = 1) -> AsyncGenerator[AudioChunk, None]:
        """Chunk PCM audio data into streaming chunks."""
        chunk_number = 1
        sequence_number = start_sequence
        start_time = time.time()
        
        # Use vendor chunk size for outbound streaming (8kHz μ-law compatibility)
        chunk_size = self.vendor_chunk_size_bytes
        
        for i in range(0, len(pcm_data), chunk_size):
            chunk_data = pcm_data[i:i + chunk_size]
            
            # Calculate timestamp in milliseconds
            timestamp = int((chunk_number - 1) * self.chunk_duration_ms)
            
            # Prepare audio chunk
            audio_chunk = await self.prepare_outbound_audio(
                chunk_data, stream_id, chunk_number, timestamp, sequence_number
            )
            
            yield audio_chunk
            
            chunk_number += 1
            sequence_number += 1
    
    def resample_audio(self, pcm_data: bytes, from_rate: int, to_rate: int) -> bytes:
        """Simple audio resampling (basic implementation)."""
        if from_rate == to_rate:
            return pcm_data
        
        try:
            import struct
            
            # Unpack as 16-bit signed integers
            samples = struct.unpack(f'<{len(pcm_data)//2}h', pcm_data)
            
            # Simple linear interpolation resampling
            ratio = to_rate / from_rate
            new_length = int(len(samples) * ratio)
            
            resampled = []
            for i in range(new_length):
                # Calculate source index
                src_idx = i / ratio
                
                # Simple nearest neighbor interpolation
                idx = int(src_idx)
                if idx < len(samples):
                    resampled.append(samples[idx])
                else:
                    resampled.append(0)
            
            # Pack back to bytes
            return struct.pack(f'<{len(resampled)}h', *resampled)
            
        except Exception as e:
            self.logger.error(f"Error resampling audio: {e}")
            return pcm_data  # Return original on error
    
    async def prepare_ai_audio(self, pcm_data: bytes, target_rate: int = 16000) -> bytes:
        """Prepare audio data for AI Voice Mate (16kHz PCM)."""
        try:
            # If the input is from vendor (8kHz), resample to 16kHz
            if self.vendor_sample_rate != target_rate:
                resampled_data = self.resample_audio(pcm_data, self.vendor_sample_rate, target_rate)
                
                log_with_context(
                    self.logger,
                    level=10,  # DEBUG
                    message="Audio resampled for AI",
                    **{
                        "from_rate": self.vendor_sample_rate,
                        "to_rate": target_rate,
                        "original_bytes": len(pcm_data),
                        "resampled_bytes": len(resampled_data)
                    }
                )
                
                return resampled_data
            
            return pcm_data
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error preparing AI audio: {e}"
            )
            raise


class DTMFProcessor:
    """Process DTMF tones for audio streaming."""
    
    # DTMF frequency pairs
    DTMF_FREQUENCIES = {
        '1': (697, 1209), '2': (697, 1336), '3': (697, 1477),
        '4': (770, 1209), '5': (770, 1336), '6': (770, 1477),
        '7': (852, 1209), '8': (852, 1336), '9': (852, 1477),
        '*': (941, 1209), '0': (941, 1336), '#': (941, 1477)
    }
    
    def __init__(self, sample_rate: int = 8000):
        self.sample_rate = sample_rate
        self.codec = AudioCodec()
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    def generate_dtmf_tone(self, digit: str, duration_ms: int = 100) -> bytes:
        """Generate DTMF tone as μ-law encoded audio."""
        if digit not in self.DTMF_FREQUENCIES:
            raise ValueError(f"Invalid DTMF digit: {digit}")
        
        freq1, freq2 = self.DTMF_FREQUENCIES[digit]
        num_samples = int(self.sample_rate * duration_ms / 1000)
        
        samples = []
        for i in range(num_samples):
            t = i / self.sample_rate
            
            # Generate dual-tone signal
            sample = 0.5 * (
                math.sin(2 * math.pi * freq1 * t) +
                math.sin(2 * math.pi * freq2 * t)
            )
            
            # Apply envelope (fade in/out)
            envelope = 1.0
            fade_samples = int(self.sample_rate * 0.01)  # 10ms fade
            if i < fade_samples:
                envelope = i / fade_samples
            elif i > num_samples - fade_samples:
                envelope = (num_samples - i) / fade_samples
            
            sample *= envelope
            
            # Convert to 16-bit PCM
            sample_int = int(sample * 16384)  # Reduce amplitude to prevent clipping
            sample_int = max(-32768, min(32767, sample_int))
            samples.append(sample_int)
        
        # Pack as PCM data
        pcm_data = struct.pack(f'<{len(samples)}h', *samples)
        
        # Encode to μ-law
        return self.codec.encode_pcm_to_mulaw(pcm_data)
    
    def detect_dtmf_digit(self, audio_data: bytes) -> Optional[str]:
        """Detect DTMF digit from audio data (simplified implementation)."""
        # This is a simplified implementation
        # In production, you'd use more sophisticated frequency analysis
        
        try:
            # Decode μ-law to PCM
            pcm_data = self.codec.decode_mulaw_to_pcm(audio_data)
            samples = struct.unpack(f'<{len(pcm_data)//2}h', pcm_data)
            
            if len(samples) < self.sample_rate * 0.05:  # Need at least 50ms
                return None
            
            # Simple energy-based detection (placeholder)
            # In real implementation, use FFT to detect frequency pairs
            energy = sum(abs(s) for s in samples) / len(samples)
            
            if energy > 1000:  # Threshold for DTMF presence
                # This is a placeholder - real implementation would analyze frequencies
                self.logger.debug(f"DTMF-like signal detected with energy: {energy}")
                return None  # Would return detected digit
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error detecting DTMF: {e}")
            return None


class AudioStreamMetrics:
    """Track metrics for audio streams."""
    
    def __init__(self):
        self.chunks_sent = 0
        self.chunks_received = 0
        self.bytes_sent = 0
        self.bytes_received = 0
        self.start_time = datetime.now()
        self.last_activity = datetime.now()
        self.errors = 0
        self.buffer_overflows = 0
        self.dtmf_detected = []
        
    def record_chunk_sent(self, chunk: AudioChunk) -> None:
        """Record sent chunk metrics."""
        self.chunks_sent += 1
        self.bytes_sent += len(chunk.payload)
        self.last_activity = datetime.now()
    
    def record_chunk_received(self, chunk: AudioChunk) -> None:
        """Record received chunk metrics."""
        self.chunks_received += 1
        self.bytes_received += len(chunk.payload)
        self.last_activity = datetime.now()
    
    def record_error(self) -> None:
        """Record error occurrence."""
        self.errors += 1
    
    def record_buffer_overflow(self) -> None:
        """Record buffer overflow."""
        self.buffer_overflows += 1
    
    def record_dtmf_detection(self, digit: str) -> None:
        """Record DTMF digit detection."""
        self.dtmf_detected.append({
            "digit": digit,
            "timestamp": datetime.now()
        })
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive statistics."""
        duration = (datetime.now() - self.start_time).total_seconds()
        
        return {
            "duration_seconds": duration,
            "chunks_sent": self.chunks_sent,
            "chunks_received": self.chunks_received,
            "bytes_sent": self.bytes_sent,
            "bytes_received": self.bytes_received,
            "send_rate_chunks_per_second": self.chunks_sent / max(duration, 1),
            "receive_rate_chunks_per_second": self.chunks_received / max(duration, 1),
            "send_rate_bytes_per_second": self.bytes_sent / max(duration, 1),
            "receive_rate_bytes_per_second": self.bytes_received / max(duration, 1),
            "errors": self.errors,
            "buffer_overflows": self.buffer_overflows,
            "dtmf_detected_count": len(self.dtmf_detected),
            "last_activity": self.last_activity.isoformat(),
            "start_time": self.start_time.isoformat()
        }