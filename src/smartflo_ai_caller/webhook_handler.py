"""Webhook handler for Smartflo call events."""

import json
from datetime import datetime
from typing import Any, Dict, Optional

from fastapi import Request
from pydantic import BaseModel, Field, validator

from .config import settings
from .utils.exceptions import WebhookValidationError
from .utils.logging import get_logger, log_with_context

logger = get_logger(__name__)


class SmartfloWebhookPayload(BaseModel):
    """Pydantic model for Smartflo webhook payload."""
    
    # Call identification
    call_id: str = Field(..., description="Unique call identifier from Smartflo")
    call_type: str = Field(..., description="Type of call (inbound/outbound)")
    
    # Caller information
    caller_number: str = Field(..., description="Caller's phone number")
    called_number: str = Field(..., description="Called/destination phone number")
    
    # Event details
    event_type: str = Field(..., description="Type of webhook event")
    event_timestamp: datetime = Field(..., description="Timestamp of the event")
    
    # Call details
    call_status: Optional[str] = Field(None, description="Current call status")
    call_duration: Optional[int] = Field(None, description="Call duration in seconds")
    agent_id: Optional[str] = Field(None, description="Agent identifier if applicable")
    
    # Additional data
    dtmf_digits: Optional[str] = Field(None, description="DTMF digits pressed")
    recording_url: Optional[str] = Field(None, description="URL to call recording")
    
    # Billing and metadata
    billing_info: Optional[Dict[str, Any]] = Field(None, description="Billing information")
    custom_data: Optional[Dict[str, Any]] = Field(None, description="Custom data from Smartflo")
    
    @validator("event_type")
    def validate_event_type(cls, v: str) -> str:
        """Validate event type."""
        valid_events = [
            "call_received",
            "call_answered_ivr",
            "call_answered_agent",
            "call_hangup",
            "dtmf_received",
            "agent_status_change",
            "call_disposition"
        ]
        if v not in valid_events:
            logger.warning(f"Unknown event type received: {v}")
        return v
    
    @validator("call_type")
    def validate_call_type(cls, v: str) -> str:
        """Validate call type."""
        valid_types = ["inbound", "outbound"]
        if v.lower() not in valid_types:
            raise ValueError(f"Call type must be one of: {valid_types}")
        return v.lower()


class WebhookHandler:
    """Handler for Smartflo webhook events."""
    
    def __init__(self) -> None:
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    async def validate_webhook_request(self, request: Request) -> None:
        """Validate incoming webhook request."""
        # Check Content-Type
        content_type = request.headers.get("content-type", "")
        if not content_type.startswith("application/json"):
            raise WebhookValidationError(
                "Invalid Content-Type. Expected application/json",
                details={"content_type": content_type}
            )
        
        # Validate webhook secret if configured
        if settings.webhook.secret:
            webhook_secret = request.headers.get("x-webhook-secret")
            if webhook_secret != settings.webhook.secret:
                raise WebhookValidationError(
                    "Invalid webhook secret",
                    details={"secret_provided": webhook_secret is not None}
                )
    
    async def parse_webhook_payload(self, request: Request) -> SmartfloWebhookPayload:
        """Parse and validate webhook payload."""
        try:
            body = await request.body()
            payload_data = json.loads(body)
            
            # Log raw payload for debugging
            log_with_context(
                self.logger,
                level=10,  # DEBUG
                message="Received webhook payload",
                **{"raw_payload": payload_data}
            )
            
            # Validate payload structure
            payload = SmartfloWebhookPayload(**payload_data)
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Webhook payload validated successfully",
                session_id=payload.call_id,
                **{
                    "event_type": payload.event_type,
                    "call_type": payload.call_type,
                    "caller_number": payload.caller_number
                }
            )
            
            return payload
            
        except json.JSONDecodeError as e:
            raise WebhookValidationError(
                "Invalid JSON payload",
                details={"json_error": str(e)}
            )
        except ValueError as e:
            raise WebhookValidationError(
                "Payload validation failed",
                details={"validation_error": str(e)}
            )
        except Exception as e:
            self.logger.error(f"Unexpected error parsing webhook payload: {e}")
            raise WebhookValidationError(
                "Failed to parse webhook payload",
                details={"error": str(e)}
            )
    
    async def process_webhook_event(self, payload: SmartfloWebhookPayload) -> Dict[str, Any]:
        """Process webhook event based on event type."""
        try:
            log_with_context(
                self.logger,
                level=20,  # INFO
                message=f"Processing webhook event: {payload.event_type}",
                session_id=payload.call_id,
                **{
                    "event_type": payload.event_type,
                    "call_status": payload.call_status,
                    "caller_number": payload.caller_number
                }
            )
            
            # Route to appropriate handler based on event type
            handler_map = {
                "call_received": self._handle_call_received,
                "call_answered_ivr": self._handle_call_answered_ivr,
                "call_answered_agent": self._handle_call_answered_agent,
                "call_hangup": self._handle_call_hangup,
                "dtmf_received": self._handle_dtmf_received,
                "agent_status_change": self._handle_agent_status_change,
                "call_disposition": self._handle_call_disposition,
            }
            
            handler = handler_map.get(payload.event_type, self._handle_unknown_event)
            result = await handler(payload)
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message=f"Webhook event processed successfully: {payload.event_type}",
                session_id=payload.call_id
            )
            
            return result
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error processing webhook event: {e}",
                session_id=payload.call_id,
                **{
                    "event_type": payload.event_type,
                    "error": str(e)
                }
            )
            raise
    
    async def _handle_call_received(self, payload: SmartfloWebhookPayload) -> Dict[str, Any]:
        """Handle call received event."""
        return {
            "action": "call_received",
            "session_id": payload.call_id,
            "caller_number": payload.caller_number,
            "called_number": payload.called_number,
            "timestamp": payload.event_timestamp.isoformat(),
            "requires_ai_connection": True
        }
    
    async def _handle_call_answered_ivr(self, payload: SmartfloWebhookPayload) -> Dict[str, Any]:
        """Handle call answered by IVR event."""
        return {
            "action": "call_answered_ivr",
            "session_id": payload.call_id,
            "caller_number": payload.caller_number,
            "timestamp": payload.event_timestamp.isoformat(),
            "requires_ai_connection": True
        }
    
    async def _handle_call_answered_agent(self, payload: SmartfloWebhookPayload) -> Dict[str, Any]:
        """Handle call answered by agent event."""
        return {
            "action": "call_answered_agent",
            "session_id": payload.call_id,
            "agent_id": payload.agent_id,
            "caller_number": payload.caller_number,
            "timestamp": payload.event_timestamp.isoformat(),
            "requires_ai_connection": True
        }
    
    async def _handle_call_hangup(self, payload: SmartfloWebhookPayload) -> Dict[str, Any]:
        """Handle call hangup event."""
        return {
            "action": "call_hangup",
            "session_id": payload.call_id,
            "call_duration": payload.call_duration,
            "timestamp": payload.event_timestamp.isoformat(),
            "requires_ai_disconnection": True
        }
    
    async def _handle_dtmf_received(self, payload: SmartfloWebhookPayload) -> Dict[str, Any]:
        """Handle DTMF received event."""
        return {
            "action": "dtmf_received",
            "session_id": payload.call_id,
            "dtmf_digits": payload.dtmf_digits,
            "timestamp": payload.event_timestamp.isoformat(),
            "requires_ai_input": True
        }
    
    async def _handle_agent_status_change(self, payload: SmartfloWebhookPayload) -> Dict[str, Any]:
        """Handle agent status change event."""
        return {
            "action": "agent_status_change",
            "session_id": payload.call_id,
            "agent_id": payload.agent_id,
            "call_status": payload.call_status,
            "timestamp": payload.event_timestamp.isoformat(),
            "requires_ai_update": True
        }
    
    async def _handle_call_disposition(self, payload: SmartfloWebhookPayload) -> Dict[str, Any]:
        """Handle call disposition event."""
        return {
            "action": "call_disposition",
            "session_id": payload.call_id,
            "timestamp": payload.event_timestamp.isoformat(),
            "recording_url": payload.recording_url,
            "requires_ai_cleanup": True
        }
    
    async def _handle_unknown_event(self, payload: SmartfloWebhookPayload) -> Dict[str, Any]:
        """Handle unknown event types."""
        log_with_context(
            self.logger,
            level=30,  # WARNING
            message=f"Unknown event type received: {payload.event_type}",
            session_id=payload.call_id,
            **{"event_type": payload.event_type}
        )
        
        return {
            "action": "unknown_event",
            "session_id": payload.call_id,
            "event_type": payload.event_type,
            "timestamp": payload.event_timestamp.isoformat(),
            "requires_logging_only": True
        }