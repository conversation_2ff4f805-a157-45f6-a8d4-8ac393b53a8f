"""Mark synchronization manager for bi-directional audio streaming."""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Set
from dataclasses import dataclass, field
from enum import Enum

from .utils.logging import get_logger, log_with_context

logger = get_logger(__name__)


class MarkState(Enum):
    """Mark synchronization states."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    EXPIRED = "expired"


@dataclass
class PendingMark:
    """Represents a pending mark waiting for synchronization."""
    
    stream_id: str
    mark_name: str
    sequence_number: int
    created_at: datetime = field(default_factory=datetime.now)
    state: MarkState = MarkState.PENDING
    callback: Optional[Callable] = None
    timeout_seconds: int = 30
    
    def is_expired(self) -> bool:
        """Check if mark has expired."""
        return (datetime.now() - self.created_at).total_seconds() > self.timeout_seconds


class MarkSynchronizer:
    """Manages mark event synchronization for audio streaming."""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self.pending_marks: Dict[str, List[PendingMark]] = {}  # stream_id -> marks
        self.mark_lock = asyncio.Lock()
        
        # Callbacks
        self.mark_completion_callback: Optional[Callable] = None
        self.mark_timeout_callback: Optional[Callable] = None
        
        # Cleanup task
        self.cleanup_task: Optional[asyncio.Task] = None
        self.start_cleanup_task()
    
    def start_cleanup_task(self) -> None:
        """Start background cleanup task."""
        if not self.cleanup_task or self.cleanup_task.done():
            self.cleanup_task = asyncio.create_task(self._cleanup_expired_marks())
    
    def register_mark_completion_callback(self, callback: Callable) -> None:
        """Register callback for mark completion."""
        self.mark_completion_callback = callback
        self.logger.debug("Registered mark completion callback")
    
    def register_mark_timeout_callback(self, callback: Callable) -> None:
        """Register callback for mark timeout."""
        self.mark_timeout_callback = callback
        self.logger.debug("Registered mark timeout callback")
    
    async def add_pending_mark(
        self, 
        stream_id: str, 
        mark_name: str, 
        sequence_number: int,
        callback: Optional[Callable] = None,
        timeout_seconds: int = 30
    ) -> None:
        """Add a mark to pending synchronization."""
        async with self.mark_lock:
            if stream_id not in self.pending_marks:
                self.pending_marks[stream_id] = []
            
            pending_mark = PendingMark(
                stream_id=stream_id,
                mark_name=mark_name,
                sequence_number=sequence_number,
                callback=callback,
                timeout_seconds=timeout_seconds
            )
            
            self.pending_marks[stream_id].append(pending_mark)
            
            log_with_context(
                self.logger,
                level=10,  # DEBUG
                message="Added pending mark for synchronization",
                stream_id=stream_id,
                **{
                    "mark_name": mark_name,
                    "sequence_number": sequence_number,
                    "timeout_seconds": timeout_seconds
                }
            )
    
    async def complete_mark(self, stream_id: str, mark_name: str) -> bool:
        """Mark a pending mark as completed."""
        async with self.mark_lock:
            if stream_id not in self.pending_marks:
                log_with_context(
                    self.logger,
                    level=30,  # WARNING
                    message="No pending marks for stream",
                    stream_id=stream_id,
                    **{"mark_name": mark_name}
                )
                return False
            
            # Find the mark to complete
            completed_mark = None
            for mark in self.pending_marks[stream_id]:
                if mark.mark_name == mark_name and mark.state == MarkState.PENDING:
                    mark.state = MarkState.COMPLETED
                    completed_mark = mark
                    break
            
            if not completed_mark:
                log_with_context(
                    self.logger,
                    level=30,  # WARNING
                    message="Mark not found in pending list",
                    stream_id=stream_id,
                    **{"mark_name": mark_name}
                )
                return False
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Mark completed",
                stream_id=stream_id,
                **{
                    "mark_name": mark_name,
                    "duration_ms": (datetime.now() - completed_mark.created_at).total_seconds() * 1000
                }
            )
            
            # Call completion callback if provided
            if completed_mark.callback:
                try:
                    await completed_mark.callback(stream_id, mark_name, True)
                except Exception as e:
                    log_with_context(
                        self.logger,
                        level=40,  # ERROR
                        message=f"Error in mark completion callback: {e}",
                        stream_id=stream_id,
                        **{"mark_name": mark_name, "error": str(e)}
                    )
            
            # Call global completion callback
            if self.mark_completion_callback:
                try:
                    await self.mark_completion_callback(stream_id, mark_name, completed_mark)
                except Exception as e:
                    log_with_context(
                        self.logger,
                        level=40,  # ERROR
                        message=f"Error in global mark completion callback: {e}",
                        stream_id=stream_id,
                        **{"mark_name": mark_name, "error": str(e)}
                    )
            
            return True
    
    async def clear_stream_marks(self, stream_id: str) -> List[PendingMark]:
        """Clear all pending marks for a stream (called on clear event)."""
        async with self.mark_lock:
            cleared_marks = []
            
            if stream_id in self.pending_marks:
                # Mark all pending marks as completed due to clear
                for mark in self.pending_marks[stream_id]:
                    if mark.state == MarkState.PENDING:
                        mark.state = MarkState.COMPLETED
                        cleared_marks.append(mark)
                        
                        # Call callback for cleared mark
                        if mark.callback:
                            try:
                                await mark.callback(stream_id, mark.mark_name, False)  # False = cleared, not played
                            except Exception as e:
                                log_with_context(
                                    self.logger,
                                    level=40,  # ERROR
                                    message=f"Error in cleared mark callback: {e}",
                                    stream_id=stream_id,
                                    **{"mark_name": mark.mark_name, "error": str(e)}
                                )
                
                log_with_context(
                    self.logger,
                    level=20,  # INFO
                    message="Cleared pending marks for stream",
                    stream_id=stream_id,
                    **{"cleared_count": len(cleared_marks)}
                )
            
            return cleared_marks
    
    async def get_pending_marks(self, stream_id: str) -> List[PendingMark]:
        """Get pending marks for a stream."""
        async with self.mark_lock:
            return [
                mark for mark in self.pending_marks.get(stream_id, [])
                if mark.state == MarkState.PENDING
            ]
    
    async def get_stream_stats(self, stream_id: str) -> Dict[str, int]:
        """Get mark statistics for a stream."""
        async with self.mark_lock:
            marks = self.pending_marks.get(stream_id, [])
            
            stats = {
                "total_marks": len(marks),
                "pending": len([m for m in marks if m.state == MarkState.PENDING]),
                "processing": len([m for m in marks if m.state == MarkState.PROCESSING]),
                "completed": len([m for m in marks if m.state == MarkState.COMPLETED]),
                "expired": len([m for m in marks if m.state == MarkState.EXPIRED])
            }
            
            return stats
    
    async def get_all_stats(self) -> Dict[str, any]:
        """Get overall mark synchronization statistics."""
        async with self.mark_lock:
            total_streams = len(self.pending_marks)
            total_marks = sum(len(marks) for marks in self.pending_marks.values())
            
            state_counts = {
                "pending": 0,
                "processing": 0,
                "completed": 0,
                "expired": 0
            }
            
            for marks in self.pending_marks.values():
                for mark in marks:
                    state_counts[mark.state.value] += 1
            
            return {
                "total_streams": total_streams,
                "total_marks": total_marks,
                "state_counts": state_counts,
                "streams": list(self.pending_marks.keys())
            }
    
    async def _cleanup_expired_marks(self) -> None:
        """Background task to clean up expired marks."""
        while True:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                
                async with self.mark_lock:
                    expired_count = 0
                    
                    for stream_id, marks in self.pending_marks.items():
                        for mark in marks:
                            if mark.state == MarkState.PENDING and mark.is_expired():
                                mark.state = MarkState.EXPIRED
                                expired_count += 1
                                
                                log_with_context(
                                    self.logger,
                                    level=30,  # WARNING
                                    message="Mark expired without completion",
                                    stream_id=stream_id,
                                    **{
                                        "mark_name": mark.mark_name,
                                        "age_seconds": (datetime.now() - mark.created_at).total_seconds()
                                    }
                                )
                                
                                # Call timeout callback
                                if mark.callback:
                                    try:
                                        await mark.callback(stream_id, mark.mark_name, False)
                                    except Exception as e:
                                        log_with_context(
                                            self.logger,
                                            level=40,  # ERROR
                                            message=f"Error in mark timeout callback: {e}",
                                            stream_id=stream_id,
                                            **{"mark_name": mark.mark_name, "error": str(e)}
                                        )
                                
                                # Call global timeout callback
                                if self.mark_timeout_callback:
                                    try:
                                        await self.mark_timeout_callback(stream_id, mark.mark_name, mark)
                                    except Exception as e:
                                        log_with_context(
                                            self.logger,
                                            level=40,  # ERROR
                                            message=f"Error in global mark timeout callback: {e}",
                                            stream_id=stream_id,
                                            **{"mark_name": mark.mark_name, "error": str(e)}
                                        )
                    
                    if expired_count > 0:
                        log_with_context(
                            self.logger,
                            level=20,  # INFO
                            message=f"Found {expired_count} expired marks during cleanup"
                        )
                    
                    # Remove old completed/expired marks (keep last 100 per stream)
                    for stream_id, marks in self.pending_marks.items():
                        # Sort by creation time, keep newest
                        marks.sort(key=lambda m: m.created_at, reverse=True)
                        
                        # Keep only last 100 marks
                        if len(marks) > 100:
                            removed_count = len(marks) - 100
                            self.pending_marks[stream_id] = marks[:100]
                            
                            log_with_context(
                                self.logger,
                                level=10,  # DEBUG
                                message=f"Removed {removed_count} old marks from stream",
                                stream_id=stream_id
                            )
                
            except Exception as e:
                log_with_context(
                    self.logger,
                    level=40,  # ERROR
                    message=f"Error in mark cleanup task: {e}",
                    **{"error": str(e)}
                )
    
    async def shutdown(self) -> None:
        """Shutdown the mark synchronizer."""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Complete any pending marks
        async with self.mark_lock:
            for stream_id, marks in self.pending_marks.items():
                for mark in marks:
                    if mark.state == MarkState.PENDING:
                        mark.state = MarkState.COMPLETED
        
        log_with_context(
            self.logger,
            level=20,  # INFO
            message="Mark synchronizer shutdown completed"
        )