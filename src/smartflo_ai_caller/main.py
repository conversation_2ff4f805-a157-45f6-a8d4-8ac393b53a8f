"""Main FastAPI application for Smartflo AI Caller middleware."""

import async<PERSON>
from contextlib import asynccontextmanager
from typing import Any, Dict

import uvicorn
import websockets
from fastapi import FastAPI, HTTPException, Request, Response, status, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from .ai_voice_client import AIVoiceMateClient
from .audio_streaming import AudioStreamManager
from .bidirectional_ws_handler import BiDirectionalWSHandler
from .config import settings
from .event_transformer import EventTransformer
from .session_manager import SessionManager
from .webhook_handler import WebhookHandler
from .utils.exceptions import (
    SmartfloAICallerException,
    WebhookValidationError,
    AIVoiceMateConnectionError,
    SessionNotFoundError,
    SessionExpiredError,
    MaxSessionsExceededError,
    RetryableError
)
from .utils.logging import setup_logging, get_logger, set_correlation_id, log_with_context


# Global instances
session_manager: SessionManager
ai_client: AIVoiceMateClient
webhook_handler: WebhookHandler
event_transformer: EventTransformer
audio_stream_manager: AudioStreamManager
bidirectional_ws_handler: BiDirectionalWSHandler
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan."""
    # Startup
    setup_logging()
    
    global session_manager, ai_client, webhook_handler, event_transformer, audio_stream_manager, bidirectional_ws_handler
    
    logger.info("Starting Smartflo AI Caller middleware...")
    
    # Initialize components
    session_manager = SessionManager()
    ai_client = AIVoiceMateClient()
    webhook_handler = WebhookHandler()
    event_transformer = EventTransformer()
    audio_stream_manager = AudioStreamManager()
    bidirectional_ws_handler = BiDirectionalWSHandler(audio_stream_manager)
    
    # Setup AI client message handlers
    ai_client.register_message_handler("ai_response", handle_ai_response)
    ai_client.register_message_handler("speech_text", handle_speech_text)
    ai_client.register_message_handler("ai_end_call", handle_ai_end_call)
    ai_client.register_message_handler("error", handle_ai_error)
    ai_client.register_message_handler("ai_audio_response", handle_ai_audio_response)
    
    # Voice Call API specific handlers
    ai_client.register_message_handler("llm_answer", handle_llm_answer)
    ai_client.register_message_handler("transcript_batch", handle_transcript_batch)
    ai_client.register_message_handler("pause_audio_recording", handle_pause_audio_recording)
    ai_client.register_message_handler("active_users", handle_active_users)
    ai_client.register_message_handler("store_user", handle_store_user_response)
    
    # Setup audio streaming handlers
    bidirectional_ws_handler.register_vendor_handler("media", handle_vendor_audio_inbound)
    bidirectional_ws_handler.register_vendor_handler("dtmf", handle_vendor_dtmf)
    bidirectional_ws_handler.register_vendor_handler("mark", handle_vendor_mark)
    bidirectional_ws_handler.register_vendor_handler("clear", handle_vendor_clear)
    
    # Setup audio forwarding callbacks
    async def forward_audio_to_ai(stream_id: str, pcm_data: bytes, audio_chunk) -> None:
        """Forward audio from vendor to AI Voice Mate."""
        if ai_client.is_connected:
            # Prepare audio for AI (resample to 16kHz if needed)
            ai_audio = await audio_stream_manager.audio_processor.prepare_ai_audio(pcm_data, 16000)
            await ai_client.send_audio_data(stream_id, ai_audio)
    
    async def forward_dtmf_to_ai(stream_id: str, digit: str) -> None:
        """Forward DTMF from vendor to AI Voice Mate."""
        if ai_client.is_connected:
            await ai_client.send_dtmf_input(stream_id, digit)
    
    async def handle_mark_sync(stream_id: str, mark_event: Dict[str, Any]) -> None:
        """Handle mark synchronization - send mark back to vendor."""
        # When AI audio completes, send mark back to vendor
        try:
            await bidirectional_ws_handler.broadcast_to_vendors(mark_event, stream_id)
            logger.debug(f"Mark sync completed for stream {stream_id}: {mark_event.get('mark', {}).get('name')}")
        except Exception as e:
            logger.error(f"Error in mark sync: {e}")
    
    # Register callbacks with audio stream manager
    audio_stream_manager.register_audio_forward_callback(forward_audio_to_ai)
    audio_stream_manager.register_dtmf_forward_callback(forward_dtmf_to_ai)
    audio_stream_manager.register_mark_sync_callback(handle_mark_sync)
    
    # Connect to AI Voice Mate
    try:
        await ai_client.connect()
        logger.info("Successfully connected to AI Voice Mate")
    except Exception as e:
        logger.error(f"Failed to connect to AI Voice Mate: {e}")
        # Continue startup - will attempt reconnection later
    
    logger.info("Smartflo AI Caller middleware started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Smartflo AI Caller middleware...")
    
    # Cleanup
    await session_manager.shutdown()
    await ai_client.disconnect()
    await bidirectional_ws_handler.shutdown()
    
    logger.info("Smartflo AI Caller middleware shutdown completed")


def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    app = FastAPI(
        title="Smartflo AI Caller",
        description="Middleware service between Smartflo webhooks and AI Voice Mate",
        version="1.0.0",
        lifespan=lifespan
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Add request middleware for correlation ID
    @app.middleware("http")
    async def add_correlation_id(request: Request, call_next):
        """Add correlation ID to each request."""
        correlation_id = request.headers.get("x-correlation-id")
        cid = set_correlation_id(correlation_id)
        
        # Add correlation ID to response
        response = await call_next(request)
        response.headers["x-correlation-id"] = cid
        return response
    
    # Exception handlers
    @app.exception_handler(WebhookValidationError)
    async def webhook_validation_exception_handler(request: Request, exc: WebhookValidationError):
        logger.error(f"Webhook validation error: {exc.message}")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "error": "webhook_validation_error",
                "message": exc.message,
                "details": exc.details
            }
        )
    
    @app.exception_handler(SessionNotFoundError)
    async def session_not_found_exception_handler(request: Request, exc: SessionNotFoundError):
        logger.error(f"Session not found: {exc.message}")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={
                "error": "session_not_found",
                "message": exc.message,
                "details": exc.details
            }
        )
    
    @app.exception_handler(SessionExpiredError)
    async def session_expired_exception_handler(request: Request, exc: SessionExpiredError):
        logger.error(f"Session expired: {exc.message}")
        return JSONResponse(
            status_code=status.HTTP_410_GONE,
            content={
                "error": "session_expired",
                "message": exc.message,
                "details": exc.details
            }
        )
    
    @app.exception_handler(MaxSessionsExceededError)
    async def max_sessions_exceeded_exception_handler(request: Request, exc: MaxSessionsExceededError):
        logger.error(f"Max sessions exceeded: {exc.message}")
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "error": "max_sessions_exceeded",
                "message": exc.message,
                "details": exc.details
            }
        )
    
    @app.exception_handler(AIVoiceMateConnectionError)
    async def ai_connection_exception_handler(request: Request, exc: AIVoiceMateConnectionError):
        logger.error(f"AI Voice Mate connection error: {exc.message}")
        return JSONResponse(
            status_code=status.HTTP_502_BAD_GATEWAY,
            content={
                "error": "ai_connection_error",
                "message": exc.message,
                "details": exc.details
            }
        )
    
    @app.exception_handler(SmartfloAICallerException)
    async def smartflo_exception_handler(request: Request, exc: SmartfloAICallerException):
        logger.error(f"Smartflo AI Caller error: {exc.message}")
        status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        
        if isinstance(exc, RetryableError):
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE
        
        return JSONResponse(
            status_code=status_code,
            content={
                "error": exc.error_code or "internal_error",
                "message": exc.message,
                "details": exc.details
            }
        )
    
    # Routes
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "service": "smartflo-ai-caller",
            "version": "1.0.0",
            "status": "running"
        }
    
    @app.post("/webhook/smartflo")
    async def handle_webhook(request: Request):
        """Handle Smartflo webhook events."""
        try:
            # Validate webhook request
            await webhook_handler.validate_webhook_request(request)
            
            # Parse webhook payload
            payload = await webhook_handler.parse_webhook_payload(request)
            
            # Process webhook event
            processed_event = await webhook_handler.process_webhook_event(payload)
            
            # Handle the processed event
            await process_smartflo_event(processed_event)
            
            return {"status": "success", "message": "Webhook processed successfully"}
            
        except WebhookValidationError:
            raise
        except Exception as e:
            logger.error(f"Unexpected error processing webhook: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error processing webhook"
            )
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        health_data = {
            "status": "healthy",
            "timestamp": asyncio.get_event_loop().time(),
            "version": "1.0.0"
        }
        
        # Check AI Voice Mate connection
        if ai_client.is_connected:
            health_data["ai_voice_mate"] = "connected"
        else:
            health_data["ai_voice_mate"] = "disconnected"
            health_data["status"] = "degraded"
        
        # Add session statistics
        session_stats = await session_manager.get_session_stats()
        health_data["sessions"] = session_stats
        
        return health_data
    
    @app.get("/metrics")
    async def metrics():
        """Metrics endpoint."""
        session_stats = await session_manager.get_session_stats()
        
        return {
            "sessions": session_stats,
            "ai_connection": {
                "connected": ai_client.is_connected,
                "url": ai_client.ws_url
            },
            "configuration": {
                "max_sessions": settings.session.max_concurrent_sessions,
                "session_timeout": settings.session.timeout,
                "retry_attempts": settings.ai_voice_mate.retry_attempts
            }
        }
    
    @app.get("/sessions")
    async def list_sessions(status: str = None, include_expired: bool = False):
        """List active sessions."""
        return await session_manager.list_sessions(status, include_expired)
    
    @app.get("/sessions/{session_id}")
    async def get_session(session_id: str):
        """Get specific session details."""
        session = await session_manager.get_session(session_id)
        return session.to_dict()
    
    @app.delete("/sessions/{session_id}")
    async def end_session(session_id: str):
        """Manually end a session."""
        session = await session_manager.end_session(session_id)
        
        # Disconnect from AI if connected
        if session.ai_connected:
            try:
                await ai_client.end_ai_call(session_id)
            except Exception as e:
                logger.error(f"Error ending AI call for session {session_id}: {e}")
        
        return {"status": "success", "message": f"Session {session_id} ended"}
    
    # Audio streaming endpoints
    
    @app.websocket("/audio-stream")
    async def audio_stream_endpoint(websocket: WebSocket):
        """WebSocket endpoint for bi-directional audio streaming."""
        await websocket.accept()
        
        try:
            await bidirectional_ws_handler.handle_vendor_connection(websocket, "/audio-stream")
        except WebSocketDisconnect:
            logger.info("Audio stream WebSocket disconnected")
        except Exception as e:
            logger.error(f"Error in audio stream WebSocket: {e}")
            await websocket.close()
    
    @app.post("/audio-stream/start")
    async def start_audio_stream(request: Request):
        """Start audio stream for a session."""
        try:
            data = await request.json()
            
            stream_id = data.get("stream_id")
            account_id = data.get("account_id")
            call_id = data.get("call_id")
            from_number = data.get("from_number")
            to_number = data.get("to_number")
            custom_parameters = data.get("custom_parameters", {})
            
            if not all([stream_id, account_id, call_id, from_number, to_number]):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Missing required parameters"
                )
            
            session = await bidirectional_ws_handler.start_audio_stream(
                stream_id, account_id, call_id, from_number, to_number, custom_parameters
            )
            
            return {
                "status": "success",
                "message": "Audio stream started",
                "stream_session": session.to_dict()
            }
            
        except Exception as e:
            logger.error(f"Error starting audio stream: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to start audio stream: {e}"
            )
    
    @app.post("/audio-stream/stop")
    async def stop_audio_stream(request: Request):
        """Stop audio stream for a session."""
        try:
            data = await request.json()
            stream_id = data.get("stream_id")
            reason = data.get("reason", "Manual stop")
            
            if not stream_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Missing stream_id parameter"
                )
            
            success = await bidirectional_ws_handler.stop_audio_stream(stream_id, reason)
            
            if success:
                return {
                    "status": "success",
                    "message": "Audio stream stopped"
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Audio stream not found"
                )
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error stopping audio stream: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to stop audio stream: {e}"
            )
    
    @app.post("/audio-stream/send-audio")
    async def send_audio_to_stream(request: Request):
        """Send audio data to stream."""
        try:
            data = await request.json()
            stream_id = data.get("stream_id")
            audio_data = data.get("audio_data")  # base64 encoded audio
            
            if not stream_id or not audio_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Missing stream_id or audio_data"
                )
            
            # Decode base64 audio data
            import base64
            audio_bytes = base64.b64decode(audio_data)
            
            chunks_sent = await bidirectional_ws_handler.send_audio_to_vendors(stream_id, audio_bytes)
            
            return {
                "status": "success",
                "message": "Audio sent to stream",
                "chunks_sent": chunks_sent
            }
            
        except Exception as e:
            logger.error(f"Error sending audio to stream: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to send audio: {e}"
            )
    
    @app.post("/audio-stream/send-dtmf")
    async def send_dtmf_to_stream(request: Request):
        """Send DTMF digit to stream."""
        try:
            data = await request.json()
            stream_id = data.get("stream_id")
            digit = data.get("digit")
            
            if not stream_id or not digit:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Missing stream_id or digit"
                )
            
            success = await bidirectional_ws_handler.send_dtmf_to_vendors(stream_id, digit)
            
            if success:
                return {
                    "status": "success",
                    "message": "DTMF digit sent"
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Audio stream not found"
                )
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error sending DTMF: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to send DTMF: {e}"
            )
    
    @app.get("/audio-stream/stats")
    async def get_audio_stream_stats():
        """Get audio streaming statistics."""
        try:
            stats = await bidirectional_ws_handler.get_connection_stats()
            return stats
        except Exception as e:
            logger.error(f"Error getting audio stream stats: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get stats: {e}"
            )
    
    return app


async def process_smartflo_event(event: Dict[str, Any]) -> None:
    """Process Smartflo webhook event and interact with AI Voice Mate."""
    session_id = event.get("session_id")
    action = event.get("action")
    
    if not session_id:
        logger.error("No session ID in processed event")
        return
    
    log_with_context(
        logger,
        level=20,  # INFO
        message=f"Processing Smartflo event: {action}",
        session_id=session_id,
        **{"action": action}
    )
    
    try:
        # Transform event to AI actions
        user_data, ai_action = event_transformer.transform_webhook_to_ai_actions(event)
        
        if action == "call_received":
            await handle_call_received(event, user_data, ai_action)
        elif action == "call_answered_ivr":
            await handle_call_answered_ivr(event, ai_action)
        elif action == "call_hangup":
            await handle_call_hangup(event, ai_action)
        elif action == "dtmf_received":
            await handle_dtmf_received(event, ai_action)
        else:
            # Generic handling for other events
            await handle_generic_event(event, ai_action)
        
    except Exception as e:
        log_with_context(
            logger,
            level=40,  # ERROR
            message=f"Error processing Smartflo event: {e}",
            session_id=session_id,
            **{"action": action, "error": str(e)}
        )


async def handle_call_received(
    event: Dict[str, Any], 
    user_data: Dict[str, Any], 
    ai_action: Dict[str, Any]
) -> None:
    """Handle call received event."""
    session_id = event["session_id"]
    caller_number = event.get("caller_number", "")
    called_number = event.get("called_number", "")
    
    # Create session
    await session_manager.create_session(
        session_id=session_id,
        caller_number=caller_number,
        called_number=called_number
    )
    
    # Register user with AI Voice Mate
    await ai_client.register_user(session_id, user_data)
    await session_manager.mark_user_registered(session_id)
    
    log_with_context(
        logger,
        level=20,  # INFO
        message="Call received and user registered",
        session_id=session_id
    )


async def handle_call_answered_ivr(event: Dict[str, Any], ai_action: Dict[str, Any]) -> None:
    """Handle call answered by IVR event."""
    session_id = event["session_id"]
    
    # Start AI call
    await ai_client.start_ai_call(session_id)
    await session_manager.mark_ai_connected(session_id)
    
    # Send greeting message
    greeting = event_transformer.create_ai_greeting_message()
    await ai_client.send_text_input(session_id, greeting)
    
    log_with_context(
        logger,
        level=20,  # INFO
        message="AI call started with greeting",
        session_id=session_id
    )


async def handle_call_hangup(event: Dict[str, Any], ai_action: Dict[str, Any]) -> None:
    """Handle call hangup event."""
    session_id = event["session_id"]
    call_duration = event.get("call_duration")
    
    # End AI call
    try:
        await ai_client.end_ai_call(session_id)
    except Exception as e:
        logger.warning(f"Error ending AI call for session {session_id}: {e}")
    
    # End session
    await session_manager.end_session(session_id, call_duration)
    
    log_with_context(
        logger,
        level=20,  # INFO
        message="Call ended and session cleaned up",
        session_id=session_id,
        **{"call_duration": call_duration}
    )


async def handle_dtmf_received(event: Dict[str, Any], ai_action: Dict[str, Any]) -> None:
    """Handle DTMF received event."""
    session_id = event["session_id"]
    dtmf_digits = event.get("dtmf_digits", "")
    
    # Send DTMF as input to AI
    await ai_client.send_dtmf_input(session_id, dtmf_digits)
    
    log_with_context(
        logger,
        level=20,  # INFO
        message="DTMF input sent to AI",
        session_id=session_id,
        **{"dtmf_digits": dtmf_digits}
    )


async def handle_generic_event(event: Dict[str, Any], ai_action: Dict[str, Any]) -> None:
    """Handle generic events."""
    session_id = event["session_id"]
    action = event.get("action")
    
    log_with_context(
        logger,
        level=20,  # INFO
        message=f"Generic event handled: {action}",
        session_id=session_id
    )


# AI Voice Mate message handlers
async def handle_ai_response(message: Dict[str, Any]) -> None:
    """Handle AI response message."""
    response_text = message.get("data", "")
    
    log_with_context(
        logger,
        level=20,  # INFO
        message="Received AI response",
        **{"response_length": len(response_text)}
    )


async def handle_speech_text(message: Dict[str, Any]) -> None:
    """Handle speech to text message."""
    transcribed_text = message.get("data", "")
    
    log_with_context(
        logger,
        level=20,  # INFO
        message="Received speech transcription",
        **{"text_length": len(transcribed_text)}
    )


async def handle_ai_end_call(message: Dict[str, Any]) -> None:
    """Handle AI end call message."""
    log_with_context(
        logger,
        level=20,  # INFO
        message="AI initiated call end"
    )


async def handle_ai_error(message: Dict[str, Any]) -> None:
    """Handle AI error message."""
    error_message = message.get("message", "Unknown error")
    
    log_with_context(
        logger,
        level=40,  # ERROR
        message=f"AI error received: {error_message}"
    )


async def handle_ai_audio_response(message: Dict[str, Any]) -> None:
    """Handle AI audio response message and forward to vendors."""
    try:
        session_id = message.get("session_id")
        audio_data = message.get("audio_data")  # Base64 encoded μ-law audio
        stream_id = message.get("stream_id")
        
        if not session_id:
            logger.error("No session_id in AI audio response")
            return
        
        if not audio_data:
            logger.warning(f"No audio data in AI response for session {session_id}")
            return
        
        # Use stream_id or fall back to session_id
        target_stream_id = stream_id or session_id
        
        # Decode base64 audio data
        import base64
        try:
            audio_bytes = base64.b64decode(audio_data)
        except Exception as e:
            log_with_context(
                logger,
                level=40,  # ERROR
                message=f"Failed to decode AI audio data: {e}",
                session_id=session_id
            )
            return
        
        # Forward audio to vendors through bi-directional handler
        chunks_sent = await bidirectional_ws_handler.send_audio_to_vendors(
            target_stream_id, audio_bytes
        )
        
        log_with_context(
            logger,
            level=20,  # INFO
            message="AI audio response forwarded to vendors",
            session_id=session_id,
            **{
                "stream_id": target_stream_id,
                "audio_bytes": len(audio_bytes),
                "chunks_sent": chunks_sent
            }
        )
        
    except Exception as e:
        log_with_context(
            logger,
            level=40,  # ERROR
            message=f"Error handling AI audio response: {e}",
            session_id=message.get("session_id"),
            **{"error": str(e)}
        )


# Voice Call API Message Handlers
async def handle_llm_answer(message: Dict[str, Any]) -> None:
    """Handle AI audio response in llm_answer format (base64 WAV chunks)."""
    try:
        audio_data = message.get("data")  # Base64 encoded WAV audio
        
        if not audio_data:
            logger.warning("No audio data in llm_answer message")
            return
        
        # TODO: Determine which session/stream this belongs to
        # The API doesn't specify session identification in llm_answer messages
        # For now, we'll try to forward to all active streams
        
        import base64
        try:
            # Decode base64 WAV audio
            wav_bytes = base64.b64decode(audio_data)
            
            # Convert WAV to PCM then to μ-law for vendor streaming
            pcm_data = await _convert_wav_to_pcm(wav_bytes)
            mulaw_data = audio_stream_manager.audio_processor.codec.encode_pcm_to_mulaw(pcm_data)
            
            # Forward to all active vendor connections
            # This is a simplification - in production, you'd need proper session tracking
            total_chunks = 0
            active_sessions = await audio_stream_manager.get_session_stats()
            
            for session_info in active_sessions.get("sessions", []):
                stream_id = session_info.get("stream_id")
                if stream_id:
                    chunks_sent = await bidirectional_ws_handler.send_audio_to_vendors(stream_id, mulaw_data)
                    total_chunks += chunks_sent
            
            log_with_context(
                logger,
                level=20,  # INFO
                message="AI llm_answer audio forwarded to vendors",
                **{
                    "audio_bytes": len(wav_bytes),
                    "pcm_bytes": len(pcm_data),
                    "mulaw_bytes": len(mulaw_data),
                    "total_chunks": total_chunks
                }
            )
            
        except Exception as e:
            log_with_context(
                logger,
                level=40,  # ERROR
                message=f"Failed to process llm_answer audio: {e}",
                **{"error": str(e)}
            )
            
    except Exception as e:
        log_with_context(
            logger,
            level=40,  # ERROR
            message=f"Error handling llm_answer: {e}",
            **{"error": str(e)}
        )


async def handle_transcript_batch(message: Dict[str, Any]) -> None:
    """Handle real-time transcription from AI Voice Mate."""
    try:
        transcript_text = message.get("data", "")
        
        log_with_context(
            logger,
            level=20,  # INFO
            message="Real-time transcript received",
            **{
                "transcript": transcript_text,
                "text_length": len(transcript_text)
            }
        )
        
        # TODO: Forward transcript to frontend/UI if needed
        # For now, just log it for debugging
        
    except Exception as e:
        log_with_context(
            logger,
            level=40,  # ERROR
            message=f"Error handling transcript_batch: {e}",
            **{"error": str(e)}
        )


async def handle_pause_audio_recording(message: Dict[str, Any]) -> None:
    """Handle pause audio recording request from AI Voice Mate."""
    try:
        log_with_context(
            logger,
            level=20,  # INFO
            message="AI requested to pause audio recording"
        )
        
        # TODO: Implement recording pause logic for active sessions
        # This would typically pause microphone recording in client applications
        # For vendor integration, we might need to send pause signals
        
        # For now, we'll log this event and potentially forward to vendors
        active_sessions = await audio_stream_manager.get_session_stats()
        
        for session_info in active_sessions.get("sessions", []):
            stream_id = session_info.get("stream_id")
            if stream_id:
                # Create a pause event and forward to vendors
                pause_event = {
                    "event": "pause_recording",
                    "streamSid": stream_id,
                    "reason": "AI requested pause"
                }
                
                # Send to vendors (this is vendor-specific implementation)
                await bidirectional_ws_handler.broadcast_to_vendors(pause_event, stream_id)
        
        log_with_context(
            logger,
            level=20,  # INFO
            message="Pause recording request processed",
            **{"active_sessions": len(active_sessions.get("sessions", []))}
        )
        
    except Exception as e:
        log_with_context(
            logger,
            level=40,  # ERROR
            message=f"Error handling pause_audio_recording: {e}",
            **{"error": str(e)}
        )


async def handle_active_users(message: Dict[str, Any]) -> None:
    """Handle active users response from AI Voice Mate."""
    try:
        users_data = message.get("data", [])
        
        log_with_context(
            logger,
            level=20,  # INFO
            message="Active users received from AI",
            **{"user_count": len(users_data)}
        )
        
        # Store or process active users data
        for user in users_data:
            session_id = user.get("session")
            name = user.get("name")
            user_id = user.get("user_id")
            
            log_with_context(
                logger,
                level=10,  # DEBUG
                message="Active user info",
                session_id=session_id,
                **{"name": name, "user_id": user_id}
            )
        
        # TODO: Update session manager with active users info if needed
        
    except Exception as e:
        log_with_context(
            logger,
            level=40,  # ERROR
            message=f"Error handling active_users: {e}",
            **{"error": str(e)}
        )


async def handle_store_user_response(message: Dict[str, Any]) -> None:
    """Handle store_user response from AI Voice Mate."""
    try:
        response_data = message.get("data", "")
        
        log_with_context(
            logger,
            level=20,  # INFO
            message="Store user response received",
            **{"response": response_data}
        )
        
        # The API shows this should be "user added successfully" on success
        if "successfully" in response_data.lower():
            logger.info("User registration confirmed by AI Voice Mate")
        else:
            logger.warning(f"Unexpected store_user response: {response_data}")
        
    except Exception as e:
        log_with_context(
            logger,
            level=40,  # ERROR
            message=f"Error handling store_user response: {e}",
            **{"error": str(e)}
        )


async def _convert_wav_to_pcm(wav_bytes: bytes) -> bytes:
    """Convert WAV audio data to raw PCM."""
    try:
        import io
        import wave
        
        # Create a BytesIO object from the WAV bytes
        wav_buffer = io.BytesIO(wav_bytes)
        
        # Read WAV file
        with wave.open(wav_buffer, 'rb') as wav_file:
            # Get audio parameters
            sample_rate = wav_file.getframerate()
            channels = wav_file.getnchannels()
            sample_width = wav_file.getsampwidth()
            
            # Read all frames as PCM data
            pcm_data = wav_file.readframes(wav_file.getnframes())
            
            log_with_context(
                logger,
                level=10,  # DEBUG
                message="WAV to PCM conversion",
                **{
                    "sample_rate": sample_rate,
                    "channels": channels,
                    "sample_width": sample_width,
                    "pcm_bytes": len(pcm_data)
                }
            )
            
            return pcm_data
            
    except Exception as e:
        log_with_context(
            logger,
            level=40,  # ERROR
            message=f"Error converting WAV to PCM: {e}",
            **{"wav_bytes": len(wav_bytes)}
        )
        raise


# Vendor event handlers for bi-directional streaming
async def handle_vendor_audio_inbound(message: Dict[str, Any], websocket, connection_id: str) -> None:
    """Handle inbound audio from vendor and forward to AI Voice Mate."""
    try:
        stream_id = message.get("streamSid")
        media_data = message.get("media", {})
        
        if not stream_id:
            logger.error("No streamSid in vendor audio message")
            return
        
        chunk_number = media_data.get("chunk")
        timestamp = media_data.get("timestamp")
        payload_b64 = media_data.get("payload", "")
        
        if not payload_b64:
            return
        
        # Decode base64 μ-law audio
        import base64
        try:
            mulaw_data = base64.b64decode(payload_b64)
            
            # Convert μ-law to PCM for AI Voice Mate
            pcm_data = audio_stream_manager.audio_processor.codec.decode_mulaw_to_pcm(mulaw_data)
            
            # Forward to AI Voice Mate (assuming it accepts PCM)
            if ai_client.is_connected:
                await ai_client.send_audio_data(stream_id, pcm_data)
            
            log_with_context(
                logger,
                level=10,  # DEBUG
                message="Vendor audio forwarded to AI",
                stream_id=stream_id,
                **{
                    "chunk_number": chunk_number,
                    "timestamp": timestamp,
                    "audio_bytes": len(mulaw_data)
                }
            )
            
        except Exception as e:
            log_with_context(
                logger,
                level=40,  # ERROR
                message=f"Error processing vendor audio: {e}",
                stream_id=stream_id,
                connection_id=connection_id
            )
            
    except Exception as e:
        logger.error(f"Error in vendor audio handler: {e}")


async def handle_vendor_dtmf(message: Dict[str, Any], websocket, connection_id: str) -> None:
    """Handle DTMF from vendor and forward to AI Voice Mate."""
    try:
        stream_id = message.get("streamSid")
        dtmf_data = message.get("dtmf", {})
        digit = dtmf_data.get("digit")
        
        if not stream_id or digit is None:
            logger.error("Missing streamSid or digit in vendor DTMF message")
            return
        
        # Forward DTMF to AI Voice Mate
        if ai_client.is_connected:
            await ai_client.send_dtmf_input(stream_id, digit)
        
        log_with_context(
            logger,
            level=20,  # INFO
            message="Vendor DTMF forwarded to AI",
            stream_id=stream_id,
            **{"digit": digit}
        )
        
    except Exception as e:
        logger.error(f"Error in vendor DTMF handler: {e}")


async def handle_vendor_mark(message: Dict[str, Any], websocket, connection_id: str) -> None:
    """Handle mark event from vendor."""
    try:
        stream_id = message.get("streamSid")
        mark_data = message.get("mark", {})
        mark_name = mark_data.get("name")
        
        log_with_context(
            logger,
            level=10,  # DEBUG
            message="Vendor mark event received",
            stream_id=stream_id,
            **{"mark_name": mark_name}
        )
        
        # Mark events indicate audio playback completion
        # Can be used for synchronization with AI Voice Mate
        
    except Exception as e:
        logger.error(f"Error in vendor mark handler: {e}")


async def handle_vendor_clear(message: Dict[str, Any], websocket, connection_id: str) -> None:
    """Handle clear event from vendor."""
    try:
        stream_id = message.get("streamSid")
        
        # Clear event means vendor wants to interrupt current audio
        # We should also clear any pending audio in AI Voice Mate
        if ai_client.is_connected:
            await ai_client.clear_audio_buffer(stream_id)
        
        log_with_context(
            logger,
            level=20,  # INFO
            message="Vendor clear event processed",
            stream_id=stream_id
        )
        
    except Exception as e:
        logger.error(f"Error in vendor clear handler: {e}")


def main() -> None:
    """Main entry point."""
    app = create_app()
    
    uvicorn.run(
        app,
        host=settings.server.host,
        port=settings.server.port,
        log_level=settings.logging.level.lower(),
        access_log=True
    )


if __name__ == "__main__":
    main()