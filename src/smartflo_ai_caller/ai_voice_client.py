"""WebSocket client for communicating with AI Voice Mate."""

import asyncio
import json
import struct
from typing import Any, Dict, Optional, Callable, List
from datetime import datetime
from contextlib import asynccontextmanager

import websockets
from websockets.exceptions import ConnectionClosed, InvalidURI

from .config import settings
from .audio_utils import AudioCodec, AudioBuffer, AudioProcessor
from .utils.exceptions import (
    AIVoiceMateConnectionError,
    AIVoiceMateTimeoutError,
    RetryableError
)
from .utils.logging import get_logger, log_with_context

logger = get_logger(__name__)


class AIVoiceMateClient:
    """WebSocket client for AI Voice Mate communication."""
    
    def __init__(self) -> None:
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.is_connected = False
        self.connection_lock = asyncio.Lock()
        self.message_handlers: Dict[str, Callable] = {}
        self.reconnect_task: Optional[asyncio.Task] = None
        self.heartbeat_task: Optional[asyncio.Task] = None
        
        # Connection settings
        self.ws_url = settings.ai_voice_mate.ws_url
        self.connection_timeout = settings.ai_voice_mate.connection_timeout
        self.retry_attempts = settings.ai_voice_mate.retry_attempts
        self.retry_delay = settings.ai_voice_mate.retry_delay
        
        # Audio streaming components
        self.audio_codec = AudioCodec()
        self.audio_processor = AudioProcessor()
        self.inbound_audio_buffer = AudioBuffer()
        self.outbound_audio_buffer = AudioBuffer()
        
        # Audio streaming state
        self.audio_streaming_enabled = False
        self.audio_stream_handlers: Dict[str, Callable] = {}
        self.audio_chunk_counter = 0
    
    async def connect(self) -> None:
        """Connect to AI Voice Mate WebSocket server."""
        async with self.connection_lock:
            if self.is_connected:
                return
            
            try:
                log_with_context(
                    self.logger,
                    level=20,  # INFO
                    message=f"Connecting to AI Voice Mate at {self.ws_url}",
                    **{"ws_url": self.ws_url}
                )
                
                self.websocket = await asyncio.wait_for(
                    websockets.connect(
                        self.ws_url,
                        ping_interval=20,
                        ping_timeout=10,
                        close_timeout=5
                    ),
                    timeout=self.connection_timeout
                )
                
                self.is_connected = True
                
                log_with_context(
                    self.logger,
                    level=20,  # INFO
                    message="Successfully connected to AI Voice Mate",
                    **{"ws_url": self.ws_url}
                )
                
                # Start message listener and heartbeat
                asyncio.create_task(self._listen_for_messages())
                self.heartbeat_task = asyncio.create_task(self._heartbeat())
                
            except asyncio.TimeoutError:
                raise AIVoiceMateTimeoutError(
                    f"Connection timeout after {self.connection_timeout}s",
                    details={"ws_url": self.ws_url, "timeout": self.connection_timeout}
                )
            except (ConnectionRefusedError, InvalidURI) as e:
                raise AIVoiceMateConnectionError(
                    f"Failed to connect to AI Voice Mate: {e}",
                    details={"ws_url": self.ws_url, "error": str(e)}
                )
            except Exception as e:
                self.logger.error(f"Unexpected connection error: {e}")
                raise AIVoiceMateConnectionError(
                    f"Unexpected connection error: {e}",
                    details={"ws_url": self.ws_url, "error": str(e)}
                )
    
    async def disconnect(self) -> None:
        """Disconnect from AI Voice Mate WebSocket server."""
        async with self.connection_lock:
            if not self.is_connected:
                return
            
            try:
                # Cancel background tasks
                if self.reconnect_task:
                    self.reconnect_task.cancel()
                if self.heartbeat_task:
                    self.heartbeat_task.cancel()
                
                # Close WebSocket connection
                if self.websocket:
                    await self.websocket.close()
                
                self.is_connected = False
                self.websocket = None
                
                log_with_context(
                    self.logger,
                    level=20,  # INFO
                    message="Disconnected from AI Voice Mate"
                )
                
            except Exception as e:
                self.logger.error(f"Error during disconnect: {e}")
    
    async def send_message(self, message: Dict[str, Any]) -> None:
        """Send message to AI Voice Mate."""
        if not self.is_connected or not self.websocket:
            raise AIVoiceMateConnectionError("Not connected to AI Voice Mate")
        
        try:
            message_json = json.dumps(message)
            
            log_with_context(
                self.logger,
                level=10,  # DEBUG
                message="Sending message to AI Voice Mate",
                session_id=message.get("session"),
                **{
                    "message_type": message.get("type"),
                    "message_size": len(message_json)
                }
            )
            
            await self.websocket.send(message_json)
            
        except ConnectionClosed as e:
            self.is_connected = False
            raise AIVoiceMateConnectionError(
                f"Connection closed while sending message: {e}",
                details={"message_type": message.get("type")}
            )
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
            raise AIVoiceMateConnectionError(
                f"Failed to send message: {e}",
                details={"message_type": message.get("type"), "error": str(e)}
            )
    
    async def register_user(self, session_id: str, user_data: Dict[str, Any]) -> None:
        """Register user with AI Voice Mate."""
        message = {
            "type": "store_user",
            "session": session_id,
            "data": {
                "name": user_data.get("name", "Unknown"),
                "mobile": user_data.get("mobile", ""),
                "userId": user_data.get("userId", session_id),
                "sentences": [],
                "sessionType": "call",
                "target": "english_tutor",
                **user_data
            }
        }
        
        await self.send_message(message)
        
        log_with_context(
            self.logger,
            level=20,  # INFO
            message="User registered with AI Voice Mate",
            session_id=session_id,
            user_id=user_data.get("userId", session_id)
        )
    
    async def start_ai_call(self, session_id: str) -> None:
        """Start AI call session."""
        await self.send_message({
            "type": "start_ai_call",
            "session": session_id,
            "data": None
        })
        
        # Enable AI listening
        await self.send_message({
            "type": "ai_start_listening",
            "session": session_id,
            "data": None
        })
        
        log_with_context(
            self.logger,
            level=20,  # INFO
            message="AI call started",
            session_id=session_id
        )
    
    async def end_ai_call(self, session_id: str) -> None:
        """End AI call session."""
        await self.send_message({
            "type": "end_ai_call",
            "session": session_id,
            "data": None
        })
        
        log_with_context(
            self.logger,
            level=20,  # INFO
            message="AI call ended",
            session_id=session_id
        )
    
    async def send_text_input(self, session_id: str, text: str) -> None:
        """Send text input to AI."""
        await self.send_message({
            "type": "text_input",
            "session": session_id,
            "data": text
        })
        
        log_with_context(
            self.logger,
            level=20,  # INFO
            message="Text input sent to AI",
            session_id=session_id,
            **{"text_length": len(text)}
        )
    
    async def send_dtmf_input(self, session_id: str, dtmf_digits: str) -> None:
        """Send DTMF input as text to AI."""
        dtmf_text = f"User pressed DTMF digits: {dtmf_digits}"
        await self.send_text_input(session_id, dtmf_text)
        
        log_with_context(
            self.logger,
            level=20,  # INFO
            message="DTMF input sent to AI",
            session_id=session_id,
            **{"dtmf_digits": dtmf_digits}
        )
    
    async def send_audio_data(self, session_id: str, audio_data: bytes) -> None:
        """Send audio data to AI Voice Mate using audio_chunk format."""
        try:
            # Convert audio bytes to array format as specified in API
            audio_bytes_array = list(audio_data)
            
            await self.send_message({
                "type": "audio_chunk",
                "session": session_id,
                "data": audio_bytes_array
            })
            
            log_with_context(
                self.logger,
                level=10,  # DEBUG
                message="Audio chunk sent to AI",
                session_id=session_id,
                **{"audio_bytes": len(audio_data)}
            )
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error sending audio chunk: {e}",
                session_id=session_id,
                **{"error": str(e)}
            )
            raise
    
    async def clear_audio_buffer(self, session_id: str) -> None:
        """Clear audio buffer for a session."""
        try:
            await self.send_message({
                "type": "clear_audio_buffer",
                "session": session_id,
                "data": None
            })
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Audio buffer cleared",
                session_id=session_id
            )
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error clearing audio buffer: {e}",
                session_id=session_id,
                **{"error": str(e)}
            )
            raise
    
    async def enable_audio_streaming(self, session_id: str) -> None:
        """Enable audio streaming for a session."""
        try:
            await self.send_message({
                "type": "enable_audio_streaming",
                "session": session_id,
                "data": {
                    "format": "pcm",
                    "sample_rate": 8000,
                    "channels": 1,
                    "bit_depth": 16
                }
            })
            
            self.audio_streaming_enabled = True
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Audio streaming enabled",
                session_id=session_id
            )
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error enabling audio streaming: {e}",
                session_id=session_id,
                **{"error": str(e)}
            )
            raise
    
    async def disable_audio_streaming(self, session_id: str) -> None:
        """Disable audio streaming for a session."""
        try:
            await self.send_message({
                "type": "disable_audio_streaming",
                "session": session_id,
                "data": None
            })
            
            self.audio_streaming_enabled = False
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Audio streaming disabled",
                session_id=session_id
            )
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error disabling audio streaming: {e}",
                session_id=session_id,
                **{"error": str(e)}
            )
            raise
    
    async def voice_action_stop(self, session_id: str, action_data: str = "stop_recording") -> None:
        """Send voice action stop message."""
        try:
            await self.send_message({
                "type": "voice_action_stop",
                "session": session_id,
                "data": action_data
            })
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Voice action stop sent",
                session_id=session_id,
                **{"action": action_data}
            )
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error sending voice action stop: {e}",
                session_id=session_id,
                **{"error": str(e)}
            )
            raise
    
    async def request_active_users(self, session_id: str) -> None:
        """Request active users from AI Voice Mate."""
        try:
            await self.send_message({
                "type": "request_active_users",
                "session": session_id,
                "data": None
            })
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message="Active users requested",
                session_id=session_id
            )
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error requesting active users: {e}",
                session_id=session_id,
                **{"error": str(e)}
            )
            raise
    
    def register_message_handler(self, message_type: str, handler: Callable) -> None:
        """Register handler for specific message types."""
        self.message_handlers[message_type] = handler
        
        log_with_context(
            self.logger,
            level=10,  # DEBUG
            message=f"Registered message handler for type: {message_type}"
        )
    
    async def _listen_for_messages(self) -> None:
        """Listen for messages from AI Voice Mate."""
        try:
            while self.is_connected and self.websocket:
                try:
                    message_raw = await self.websocket.recv()
                    message = json.loads(message_raw)
                    
                    log_with_context(
                        self.logger,
                        level=10,  # DEBUG
                        message="Received message from AI Voice Mate",
                        **{
                            "message_type": message.get("type"),
                            "message_size": len(message_raw)
                        }
                    )
                    
                    # Handle message through registered handlers
                    message_type = message.get("type")
                    if message_type in self.message_handlers:
                        try:
                            await self.message_handlers[message_type](message)
                        except Exception as e:
                            log_with_context(
                                self.logger,
                                level=40,  # ERROR
                                message=f"Error in message handler: {e}",
                                **{
                                    "message_type": message_type,
                                    "error": str(e)
                                }
                            )
                    
                except ConnectionClosed:
                    log_with_context(
                        self.logger,
                        level=30,  # WARNING
                        message="Connection closed by AI Voice Mate"
                    )
                    break
                except json.JSONDecodeError as e:
                    log_with_context(
                        self.logger,
                        level=40,  # ERROR
                        message=f"Invalid JSON received: {e}"
                    )
                except Exception as e:
                    log_with_context(
                        self.logger,
                        level=40,  # ERROR
                        message=f"Error processing message: {e}"
                    )
                    
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error in message listener: {e}"
            )
        finally:
            self.is_connected = False
            # Schedule reconnection if needed
            if not self.reconnect_task or self.reconnect_task.done():
                self.reconnect_task = asyncio.create_task(self._auto_reconnect())
    
    async def _heartbeat(self) -> None:
        """Send periodic heartbeat to maintain connection."""
        while self.is_connected:
            try:
                await asyncio.sleep(30)  # Send heartbeat every 30 seconds
                if self.websocket and self.is_connected:
                    await self.websocket.ping()
            except Exception as e:
                log_with_context(
                    self.logger,
                    level=40,  # ERROR
                    message=f"Heartbeat error: {e}"
                )
                break
    
    async def _auto_reconnect(self) -> None:
        """Automatically reconnect on connection loss."""
        if self.is_connected:
            return
        
        for attempt in range(1, self.retry_attempts + 1):
            try:
                log_with_context(
                    self.logger,
                    level=20,  # INFO
                    message=f"Attempting to reconnect (attempt {attempt}/{self.retry_attempts})"
                )
                
                await asyncio.sleep(self.retry_delay * attempt)  # Exponential backoff
                await self.connect()
                return
                
            except Exception as e:
                log_with_context(
                    self.logger,
                    level=40,  # ERROR
                    message=f"Reconnection attempt {attempt} failed: {e}"
                )
                
                if attempt == self.retry_attempts:
                    log_with_context(
                        self.logger,
                        level=50,  # CRITICAL
                        message="All reconnection attempts failed"
                    )
    
    @asynccontextmanager
    async def connection_context(self):
        """Context manager for WebSocket connection."""
        try:
            await self.connect()
            yield self
        finally:
            await self.disconnect()
    
    async def wait_for_connection(self, timeout: int = 30) -> bool:
        """Wait for connection to be established."""
        start_time = asyncio.get_event_loop().time()
        while not self.is_connected:
            if asyncio.get_event_loop().time() - start_time > timeout:
                return False
            await asyncio.sleep(0.1)
        return True
    
    # Audio streaming methods
    
    def register_audio_handler(self, message_type: str, handler: Callable) -> None:
        """Register handler for audio-specific message types."""
        self.audio_stream_handlers[message_type] = handler
        
        log_with_context(
            self.logger,
            level=10,  # DEBUG
            message=f"Registered audio handler for type: {message_type}"
        )
    
    async def enable_audio_streaming(self, session_id: str) -> None:
        """Enable audio streaming for a session."""
        self.audio_streaming_enabled = True
        self.audio_chunk_counter = 0
        
        # Clear buffers
        await self.inbound_audio_buffer.clear()
        await self.outbound_audio_buffer.clear()
        
        log_with_context(
            self.logger,
            level=20,  # INFO
            message="Audio streaming enabled",
            session_id=session_id
        )
    
    async def disable_audio_streaming(self, session_id: str) -> None:
        """Disable audio streaming for a session."""
        self.audio_streaming_enabled = False
        
        # Clear buffers
        await self.inbound_audio_buffer.clear()
        await self.outbound_audio_buffer.clear()
        
        log_with_context(
            self.logger,
            level=20,  # INFO
            message="Audio streaming disabled",
            session_id=session_id
        )
    
    async def send_audio_chunk(self, session_id: str, audio_data: List[int]) -> None:
        """Send audio chunk to AI Voice Mate (existing method enhanced)."""
        if not self.audio_streaming_enabled:
            # Fallback to original behavior if audio streaming not enabled
            message = {
                "type": "audio_chunk",
                "session": session_id,
                "data": audio_data
            }
            await self.send_message(message)
            return
        
        # Convert list of integers to bytes for audio processing
        try:
            # Convert audio data (assuming it's 8-bit unsigned samples)
            audio_bytes = bytes(audio_data)
            
            # Add to outbound buffer
            await self.outbound_audio_buffer.write(audio_bytes)
            
            # Process through audio codec if needed
            # (This is for compatibility with existing AI Voice Mate format)
            message = {
                "type": "audio_chunk",
                "session": session_id,
                "data": audio_data
            }
            
            await self.send_message(message)
            self.audio_chunk_counter += 1
            
            log_with_context(
                self.logger,
                level=10,  # DEBUG
                message="Audio chunk sent to AI Voice Mate",
                session_id=session_id,
                **{
                    "chunk_counter": self.audio_chunk_counter,
                    "audio_size": len(audio_data)
                }
            )
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error sending audio chunk: {e}",
                session_id=session_id,
                **{"error": str(e)}
            )
            raise AIVoiceMateConnectionError(f"Failed to send audio chunk: {e}")
    
    async def send_pcm_audio(self, session_id: str, pcm_data: bytes) -> None:
        """Send PCM audio data to AI Voice Mate."""
        if not self.is_connected or not self.websocket:
            raise AIVoiceMateConnectionError("Not connected to AI Voice Mate")
        
        try:
            # Convert PCM to the format expected by AI Voice Mate
            # This might need to be adjusted based on AI Voice Mate's expected format
            
            # For now, convert PCM samples to the integer list format
            if len(pcm_data) % 2 != 0:
                pcm_data += b'\x00'  # Pad if odd length
            
            # Unpack as signed 16-bit integers
            pcm_samples = struct.unpack(f'<{len(pcm_data)//2}h', pcm_data)
            
            # Convert to unsigned 8-bit samples for AI Voice Mate
            audio_data = []
            for sample in pcm_samples:
                # Convert from signed 16-bit to unsigned 8-bit
                byte_val = ((sample + 32768) >> 8) & 0xFF
                audio_data.append(byte_val)
            
            # Send as audio chunk
            await self.send_audio_chunk(session_id, audio_data)
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error sending PCM audio: {e}",
                session_id=session_id,
                **{"pcm_size": len(pcm_data), "error": str(e)}
            )
            raise AIVoiceMateConnectionError(f"Failed to send PCM audio: {e}")
    
    async def send_mulaw_audio(self, session_id: str, mulaw_data: bytes) -> None:
        """Send μ-law encoded audio to AI Voice Mate."""
        try:
            # Convert μ-law to PCM first
            pcm_data = self.audio_codec.decode_mulaw_to_pcm(mulaw_data)
            
            # Send as PCM
            await self.send_pcm_audio(session_id, pcm_data)
            
            log_with_context(
                self.logger,
                level=10,  # DEBUG
                message="μ-law audio sent to AI Voice Mate",
                session_id=session_id,
                **{"mulaw_size": len(mulaw_data), "pcm_size": len(pcm_data)}
            )
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error sending μ-law audio: {e}",
                session_id=session_id,
                **{"error": str(e)}
            )
            raise AIVoiceMateConnectionError(f"Failed to send μ-law audio: {e}")
    
    async def get_inbound_audio(self, session_id: str, max_size: int = 1024) -> Optional[bytes]:
        """Get inbound audio data from buffer."""
        if not self.audio_streaming_enabled:
            return None
        
        buffer_size = await self.inbound_audio_buffer.size()
        if buffer_size == 0:
            return None
        
        audio_data = await self.inbound_audio_buffer.read(min(max_size, buffer_size))
        
        log_with_context(
            self.logger,
            level=10,  # DEBUG
            message="Retrieved inbound audio data",
            session_id=session_id,
            **{"audio_size": len(audio_data), "buffer_remaining": await self.inbound_audio_buffer.size()}
        )
        
        return audio_data
    
    async def process_ai_audio_response(self, message: Dict[str, Any]) -> Optional[bytes]:
        """Process audio response from AI Voice Mate."""
        try:
            # Check if this is an audio response
            message_type = message.get("type")
            if message_type != "ai_audio_response":
                return None
            
            session_id = message.get("session")
            audio_data = message.get("data")
            
            if not audio_data or not session_id:
                return None
            
            # Process audio data (assuming it comes as a list of integers)
            if isinstance(audio_data, list):
                # Convert to bytes
                audio_bytes = bytes(audio_data)
            elif isinstance(audio_data, str):
                # If base64 encoded
                import base64
                audio_bytes = base64.b64decode(audio_data)
            else:
                audio_bytes = audio_data
            
            # Add to inbound buffer
            await self.inbound_audio_buffer.write(audio_bytes)
            
            log_with_context(
                self.logger,
                level=10,  # DEBUG
                message="Processed AI audio response",
                session_id=session_id,
                **{"audio_size": len(audio_bytes)}
            )
            
            return audio_bytes
            
        except Exception as e:
            log_with_context(
                self.logger,
                level=40,  # ERROR
                message=f"Error processing AI audio response: {e}",
                **{"error": str(e)}
            )
            return None
    
    async def get_audio_streaming_stats(self) -> Dict[str, Any]:
        """Get audio streaming statistics."""
        inbound_size = await self.inbound_audio_buffer.size()
        outbound_size = await self.outbound_audio_buffer.size()
        
        return {
            "audio_streaming_enabled": self.audio_streaming_enabled,
            "audio_chunk_counter": self.audio_chunk_counter,
            "inbound_buffer_size": inbound_size,
            "outbound_buffer_size": outbound_size,
            "audio_handlers_registered": len(self.audio_stream_handlers)
        }