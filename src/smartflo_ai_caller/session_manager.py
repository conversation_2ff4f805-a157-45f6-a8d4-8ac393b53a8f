"""Session management for call sessions between Smartflo and AI Voice Mate."""

import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Set
from dataclasses import dataclass, field

from .config import settings
from .utils.exceptions import (
    SessionNotFoundError,
    SessionExpiredError,
    MaxSessionsExceededError
)
from .utils.logging import get_logger, log_with_context

logger = get_logger(__name__)


@dataclass
class CallSession:
    """Represents an active call session."""
    
    session_id: str
    caller_number: str
    called_number: str
    created_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    status: str = "initialized"  # initialized, active, ended, error
    ai_connected: bool = False
    user_registered: bool = False
    call_duration: Optional[int] = None
    agent_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_expired(self, timeout_seconds: int) -> bool:
        """Check if session has expired."""
        expiry_time = self.last_activity + timedelta(seconds=timeout_seconds)
        return datetime.now() > expiry_time
    
    def update_activity(self) -> None:
        """Update last activity timestamp."""
        self.last_activity = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert session to dictionary."""
        return {
            "session_id": self.session_id,
            "caller_number": self.caller_number,
            "called_number": self.called_number,
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "status": self.status,
            "ai_connected": self.ai_connected,
            "user_registered": self.user_registered,
            "call_duration": self.call_duration,
            "agent_id": self.agent_id,
            "metadata": self.metadata
        }


class SessionManager:
    """Manages call sessions and their lifecycle."""
    
    def __init__(self) -> None:
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self.sessions: Dict[str, CallSession] = {}
        self.session_lock = asyncio.Lock()
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # Configuration
        self.session_timeout = settings.session.timeout
        self.max_concurrent_sessions = settings.session.max_concurrent_sessions
        
        # Start cleanup task
        self.cleanup_task = asyncio.create_task(self._cleanup_expired_sessions())
    
    async def create_session(
        self,
        session_id: str,
        caller_number: str,
        called_number: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> CallSession:
        """Create a new call session."""
        async with self.session_lock:
            # Check if session already exists
            if session_id in self.sessions:
                existing_session = self.sessions[session_id]
                log_with_context(
                    self.logger,
                    level=30,  # WARNING
                    message=f"Session already exists: {session_id}",
                    session_id=session_id,
                    **{"existing_status": existing_session.status}
                )
                return existing_session
            
            # Check concurrent session limit
            active_sessions = len([s for s in self.sessions.values() 
                                 if s.status in ["initialized", "active"]])
            
            if active_sessions >= self.max_concurrent_sessions:
                raise MaxSessionsExceededError(
                    max_sessions=self.max_concurrent_sessions,
                    current_sessions=active_sessions
                )
            
            # Create new session
            session = CallSession(
                session_id=session_id,
                caller_number=caller_number,
                called_number=called_number,
                metadata=metadata or {}
            )
            
            self.sessions[session_id] = session
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message=f"Created new session: {session_id}",
                session_id=session_id,
                **{
                    "caller_number": caller_number,
                    "called_number": called_number,
                    "active_sessions": active_sessions + 1
                }
            )
            
            return session
    
    async def get_session(self, session_id: str) -> CallSession:
        """Get session by ID."""
        async with self.session_lock:
            if session_id not in self.sessions:
                raise SessionNotFoundError(session_id)
            
            session = self.sessions[session_id]
            
            # Check if session has expired
            if session.is_expired(self.session_timeout):
                await self._remove_session(session_id)
                raise SessionExpiredError(session_id)
            
            # Update activity
            session.update_activity()
            return session
    
    async def update_session_status(
        self,
        session_id: str,
        status: str,
        **kwargs: Any
    ) -> CallSession:
        """Update session status and additional fields."""
        session = await self.get_session(session_id)
        
        session.status = status
        session.update_activity()
        
        # Update additional fields
        for key, value in kwargs.items():
            if hasattr(session, key):
                setattr(session, key, value)
            else:
                session.metadata[key] = value
        
        log_with_context(
            self.logger,
            level=20,  # INFO
            message=f"Updated session status: {session_id} -> {status}",
            session_id=session_id,
            **{"new_status": status}
        )
        
        return session
    
    async def mark_ai_connected(self, session_id: str) -> CallSession:
        """Mark session as AI connected."""
        return await self.update_session_status(
            session_id, 
            "active", 
            ai_connected=True
        )
    
    async def mark_user_registered(self, session_id: str) -> CallSession:
        """Mark session as user registered."""
        return await self.update_session_status(
            session_id,
            "active", 
            user_registered=True
        )
    
    async def end_session(
        self,
        session_id: str,
        call_duration: Optional[int] = None
    ) -> CallSession:
        """End a call session."""
        session = await self.update_session_status(
            session_id,
            "ended",
            call_duration=call_duration,
            ai_connected=False
        )
        
        log_with_context(
            self.logger,
            level=20,  # INFO
            message=f"Session ended: {session_id}",
            session_id=session_id,
            **{
                "call_duration": call_duration,
                "session_duration": (datetime.now() - session.created_at).total_seconds()
            }
        )
        
        return session
    
    async def remove_session(self, session_id: str) -> None:
        """Remove session from manager."""
        async with self.session_lock:
            await self._remove_session(session_id)
    
    async def _remove_session(self, session_id: str) -> None:
        """Internal method to remove session (must be called with lock)."""
        if session_id in self.sessions:
            session = self.sessions.pop(session_id)
            
            log_with_context(
                self.logger,
                level=20,  # INFO
                message=f"Removed session: {session_id}",
                session_id=session_id,
                **{
                    "final_status": session.status,
                    "duration": (datetime.now() - session.created_at).total_seconds()
                }
            )
    
    async def get_active_sessions(self) -> Dict[str, CallSession]:
        """Get all active sessions."""
        async with self.session_lock:
            return {
                sid: session for sid, session in self.sessions.items()
                if session.status in ["initialized", "active"] and 
                not session.is_expired(self.session_timeout)
            }
    
    async def get_session_stats(self) -> Dict[str, Any]:
        """Get session statistics."""
        async with self.session_lock:
            total_sessions = len(self.sessions)
            active_sessions = len([s for s in self.sessions.values() 
                                 if s.status in ["initialized", "active"]])
            ended_sessions = len([s for s in self.sessions.values() 
                                if s.status == "ended"])
            error_sessions = len([s for s in self.sessions.values() 
                                if s.status == "error"])
            ai_connected_sessions = len([s for s in self.sessions.values() 
                                       if s.ai_connected])
            
            return {
                "total_sessions": total_sessions,
                "active_sessions": active_sessions,
                "ended_sessions": ended_sessions,
                "error_sessions": error_sessions,
                "ai_connected_sessions": ai_connected_sessions,
                "max_concurrent_sessions": self.max_concurrent_sessions,
                "session_timeout": self.session_timeout
            }
    
    async def list_sessions(
        self,
        status_filter: Optional[str] = None,
        include_expired: bool = False
    ) -> Dict[str, Dict[str, Any]]:
        """List sessions with optional filtering."""
        async with self.session_lock:
            result = {}
            
            for session_id, session in self.sessions.items():
                # Skip expired sessions unless explicitly requested
                if not include_expired and session.is_expired(self.session_timeout):
                    continue
                
                # Apply status filter
                if status_filter and session.status != status_filter:
                    continue
                
                result[session_id] = session.to_dict()
            
            return result
    
    async def cleanup_expired_sessions(self) -> int:
        """Manually cleanup expired sessions."""
        async with self.session_lock:
            expired_sessions = []
            
            for session_id, session in list(self.sessions.items()):
                if session.is_expired(self.session_timeout):
                    expired_sessions.append(session_id)
            
            # Remove expired sessions
            for session_id in expired_sessions:
                await self._remove_session(session_id)
            
            if expired_sessions:
                log_with_context(
                    self.logger,
                    level=20,  # INFO
                    message=f"Cleaned up {len(expired_sessions)} expired sessions",
                    **{"expired_session_count": len(expired_sessions)}
                )
            
            return len(expired_sessions)
    
    async def _cleanup_expired_sessions(self) -> None:
        """Background task to cleanup expired sessions."""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                await self.cleanup_expired_sessions()
            except asyncio.CancelledError:
                log_with_context(
                    self.logger,
                    level=20,  # INFO
                    message="Session cleanup task cancelled"
                )
                break
            except Exception as e:
                log_with_context(
                    self.logger,
                    level=40,  # ERROR
                    message=f"Error in session cleanup task: {e}"
                )
    
    async def shutdown(self) -> None:
        """Shutdown session manager."""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        
        # End all active sessions
        async with self.session_lock:
            active_sessions = [
                sid for sid, session in self.sessions.items()
                if session.status in ["initialized", "active"]
            ]
            
            for session_id in active_sessions:
                await self.update_session_status(session_id, "ended")
        
        log_with_context(
            self.logger,
            level=20,  # INFO
            message="Session manager shutdown completed",
            **{"ended_sessions": len(active_sessions)}
        )