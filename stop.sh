#!/bin/bash
# Smartflo AI Caller - Shutdown Script
# This script stops the Smartflo AI Caller middleware service

set -e # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="/home/<USER>/PycharmProjects/smartflo-ai-caller"
PID_FILE="$PROJECT_DIR/smartflo-ai-caller.pid"

# Functions
print_header() {
    echo -e "${BLUE}=================================================${NC}"
    echo -e "${BLUE}🛑 Smartflo AI Caller - Shutdown Script${NC}"
    echo -e "${BLUE}=================================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Main execution
main() {
    print_header

    local pid

    if [ ! -f "$PID_FILE" ]; then
        print_warning "PID file not found: $PID_FILE"
        print_info "Checking for running process as a fallback..."
        pid=$(pgrep -f "smartflo_ai_caller.main")
        if [ -z "$pid" ]; then
            print_error "No running 'smartflo_ai_caller.main' process found."
            print_info "Service appears to be stopped."
            exit 1
        else
            print_info "Found running process with PID: $pid"
        fi
    else
        pid=$(cat "$PID_FILE")
    fi

    if [ -z "$pid" ]; then
        print_error "PID file is empty. Removing stale file."
        rm -f "$PID_FILE"
        exit 1
    fi

    print_info "Attempting to stop service with PID: $pid"

    if kill -0 $pid 2>/dev/null; then
        # Process exists, try to terminate it gracefully
        kill $pid
        
        # Wait for the process to stop
        local max_attempts=10
        local attempt=1
        while kill -0 $pid 2>/dev/null; do
            if [ $attempt -ge $max_attempts ]; then
                print_error "Service did not stop gracefully after 10 seconds. Forcing shutdown..."
                kill -9 $pid
                sleep 1
                break
            fi
            print_info "Waiting for service to shut down... ($attempt/$max_attempts)"
            sleep 1
            ((attempt++))
        done

        print_success "Service stopped successfully."
    else
        print_warning "Process with PID(s) $pid not found. It might have already stopped."
    fi

    print_info "Cleaning up PID file..."
    if [ -f "$PID_FILE" ]; then
        rm -f "$PID_FILE"
    fi
    print_success "Shutdown complete."
}

# Run main function
main "$@"