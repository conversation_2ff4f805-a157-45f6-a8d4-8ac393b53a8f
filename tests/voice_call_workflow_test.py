#!/usr/bin/env python3
"""Complete voice call workflow test - Integration between Smartflo webhooks and AI Voice Mate with real audio."""

import asyncio
import json
import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

import httpx
import websockets

from audio_processor import AudioProcessor
from voice_call_client import VoiceCallTestClient


class CompleteVoiceCallWorkflowTest:
    """Complete integration test for voice call workflow with real audio streaming."""
    
    def __init__(self, smartflo_middleware_url: str = "http://localhost:8000"):
        self.smartflo_url = smartflo_middleware_url
        self.webhook_url = f"{self.smartflo_url}/webhook/smartflo"
        self.sessions_url = f"{self.smartflo_url}/sessions"
        self.health_url = f"{self.smartflo_url}/health"
        
        self.audio_processor = AudioProcessor()
        self.test_results: List[Dict[str, Any]] = []
        
    def log_result(self, test_name: str, status: str, details: str = "", metrics: Optional[Dict] = None):
        """Log test result."""
        result = {
            "test": test_name,
            "status": status,
            "details": details,
            "timestamp": datetime.now().isoformat(),
            "metrics": metrics or {}
        }
        self.test_results.append(result)
        
        status_emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_emoji} {test_name}: {status} {details}")
    
    async def send_smartflo_webhook(self, call_id: str, event_type: str, additional_data: Optional[Dict] = None) -> bool:
        """Send webhook event to Smartflo middleware."""
        base_payload = {
            "call_id": call_id,
            "call_type": "inbound",
            "caller_number": "******-VOICE-TEST",
            "called_number": "+1-800-AI-HELP",
            "event_type": event_type,
            "event_timestamp": datetime.now().isoformat(),
            "call_status": "active"
        }
        
        if additional_data:
            base_payload.update(additional_data)
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.webhook_url,
                    json=base_payload,
                    headers={"Content-Type": "application/json"}
                )
                return response.status_code == 200
        except Exception as e:
            print(f"❌ Webhook error: {e}")
            return False
    
    async def wait_for_session_state(self, call_id: str, expected_status: str, timeout: float = 10.0) -> bool:
        """Wait for session to reach expected state."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.get(f"{self.sessions_url}/{call_id}")
                    if response.status_code == 200:
                        session_data = response.json()
                        if session_data.get("status") == expected_status:
                            return True
                    elif response.status_code == 404 and expected_status == "not_found":
                        return True
            except Exception:
                pass
            
            await asyncio.sleep(0.5)
        
        return False
    
    async def test_complete_call_lifecycle_with_audio(self) -> bool:
        """Test complete call lifecycle with real audio streaming."""
        test_name = "Complete Call Lifecycle with Audio"
        call_id = f"complete_test_{uuid.uuid4().hex[:8]}"
        
        print(f"\n🎯 TESTING: {test_name}")
        print("="*60)
        
        try:
            start_time = time.time()
            audio_client = VoiceCallTestClient()
            
            # Phase 1: Call Received
            print("\n📞 PHASE 1: Call Received")
            webhook_success = await self.send_smartflo_webhook(call_id, "call_received")
            if not webhook_success:
                self.log_result(test_name, "FAIL", "call_received webhook failed")
                return False
            
            # Wait for session creation
            session_created = await self.wait_for_session_state(call_id, "active", 5.0)
            if not session_created:
                self.log_result(test_name, "FAIL", "Session not created")
                return False
            
            print("   ✅ Session created successfully")
            
            # Phase 2: Call Answered - Start AI interaction
            print("\n🤖 PHASE 2: Call Answered - AI Startup")
            webhook_success = await self.send_smartflo_webhook(call_id, "call_answered_ivr")
            if not webhook_success:
                self.log_result(test_name, "FAIL", "call_answered_ivr webhook failed")
                return False
            
            # Connect audio client with same session ID
            audio_client.session_id = call_id
            if not await audio_client.connect():
                self.log_result(test_name, "FAIL", "AI Voice Mate connection failed")
                return False
            
            print("   ✅ Connected to AI Voice Mate")
            
            # Register user and start AI call
            await audio_client.register_user({
                "name": "Voice Test User",
                "mobile": "******-VOICE-TEST",
                "userId": call_id
            })
            
            await asyncio.sleep(1)
            await audio_client.start_ai_call()
            await asyncio.sleep(2)  # Allow AI to initialize
            
            print("   ✅ AI call session started")
            
            # Phase 3: Audio Streaming - Customer speaks
            print("\n🎙️  PHASE 3: Audio Streaming - Customer Input")
            
            # Stream different types of audio
            test_phrases = [
                "Hello, I need help with my account",
                "Yes, I can hear you",
                "My account number is twelve thirty four"
            ]
            
            audio_metrics = {
                "phrases_sent": 0,
                "chunks_sent": 0,
                "responses_received": 0
            }
            
            for i, phrase in enumerate(test_phrases, 1):
                print(f"   🗣️  Speaking phrase {i}: '{phrase}'")
                
                # Record initial stats
                initial_chunks = audio_client.audio_chunks_sent
                initial_responses = audio_client.ai_responses_received
                
                # Stream the phrase
                await audio_client.stream_test_phrase(phrase, chunk_duration=0.1)
                
                # Wait for AI processing
                await asyncio.sleep(3)
                
                # Update metrics
                chunks_for_phrase = audio_client.audio_chunks_sent - initial_chunks
                responses_for_phrase = audio_client.ai_responses_received - initial_responses
                
                audio_metrics["phrases_sent"] += 1
                audio_metrics["chunks_sent"] += chunks_for_phrase
                audio_metrics["responses_received"] += responses_for_phrase
                
                print(f"      📊 Chunks sent: {chunks_for_phrase}, AI responses: {responses_for_phrase}")
            
            # Phase 4: DTMF Integration
            print("\n🔢 PHASE 4: DTMF Integration")
            
            # Send DTMF webhook
            webhook_success = await self.send_smartflo_webhook(
                call_id, 
                "dtmf_received", 
                {"dtmf_digits": "1234*"}
            )
            
            if webhook_success:
                print("   ✅ DTMF webhook sent")
                await asyncio.sleep(1)
                
                # Follow up with audio confirmation
                await audio_client.stream_test_phrase("I pressed one two three four star")
                await asyncio.sleep(2)
                print("   ✅ DTMF audio confirmation sent")
            
            # Phase 5: Agent Status Update (optional)
            print("\n👨‍💼 PHASE 5: Agent Status Update")
            webhook_success = await self.send_smartflo_webhook(
                call_id,
                "agent_status_change",
                {"agent_id": "agent_001", "call_status": "answered"}
            )
            if webhook_success:
                print("   ✅ Agent status updated")
            
            # Phase 6: Call Termination
            print("\n📴 PHASE 6: Call Termination")
            
            # Customer says goodbye
            await audio_client.stream_test_phrase("Thank you for your help, goodbye!")
            await asyncio.sleep(2)
            
            # End AI call
            await audio_client.end_ai_call()
            await asyncio.sleep(1)
            
            # Send hangup webhook
            call_duration = int(time.time() - start_time)
            webhook_success = await self.send_smartflo_webhook(
                call_id,
                "call_hangup",
                {
                    "call_status": "ended",
                    "call_duration": call_duration,
                    "recording_url": f"https://recordings.example.com/{call_id}.wav"
                }
            )
            
            if not webhook_success:
                self.log_result(test_name, "FAIL", "call_hangup webhook failed")
                return False
            
            # Wait for session cleanup
            session_ended = await self.wait_for_session_state(call_id, "ended", 5.0)
            
            await audio_client.disconnect()
            
            # Phase 7: Results Analysis
            print("\n📊 PHASE 7: Results Analysis")
            
            total_duration = time.time() - start_time
            final_stats = audio_client.get_call_statistics()
            
            # Combine metrics
            complete_metrics = {
                **audio_metrics,
                **final_stats,
                "total_call_duration": total_duration,
                "session_ended_properly": session_ended
            }
            
            # Determine success
            success_criteria = [
                audio_metrics["phrases_sent"] >= 3,
                audio_metrics["chunks_sent"] > 50,  # Should have sent many audio chunks
                final_stats["messages_received"] > 0,  # Should have received some AI messages
                session_ended  # Session should be properly ended
            ]
            
            success = all(success_criteria)
            
            print(f"   📈 Total duration: {total_duration:.2f}s")
            print(f"   📦 Audio chunks sent: {audio_metrics['chunks_sent']}")
            print(f"   🤖 AI responses received: {audio_metrics['responses_received']}")
            print(f"   💬 Total messages: {final_stats['messages_received']}")
            print(f"   ✅ Session ended: {session_ended}")
            
            if final_stats.get('recognized_speech'):
                print(f"   🎯 Speech recognized:")
                for speech in final_stats['recognized_speech']:
                    print(f"      - '{speech}'")
            
            status = "PASS" if success else "FAIL"
            details = f"Duration: {total_duration:.1f}s, Chunks: {audio_metrics['chunks_sent']}, Responses: {audio_metrics['responses_received']}"
            
            self.log_result(test_name, status, details, complete_metrics)
            return success
            
        except Exception as e:
            self.log_result(test_name, "FAIL", f"Exception: {e}")
            return False
        finally:
            if 'audio_client' in locals() and audio_client.is_connected:
                await audio_client.disconnect()
    
    async def test_concurrent_voice_calls(self, num_calls: int = 3) -> bool:
        """Test multiple concurrent voice calls."""
        test_name = f"Concurrent Voice Calls (x{num_calls})"
        print(f"\n🎯 TESTING: {test_name}")
        print("="*60)
        
        try:
            # Create multiple call tasks
            call_tasks = []
            call_ids = []
            
            for i in range(num_calls):
                call_id = f"concurrent_test_{i:02d}_{uuid.uuid4().hex[:6]}"
                call_ids.append(call_id)
                
                task = asyncio.create_task(self._single_concurrent_call(call_id, i))
                call_tasks.append(task)
            
            # Wait for all calls to complete
            results = await asyncio.gather(*call_tasks, return_exceptions=True)
            
            # Analyze results
            successful_calls = sum(1 for r in results if r is True)
            failed_calls = len(results) - successful_calls
            
            success = successful_calls >= (num_calls * 0.8)  # 80% success rate
            
            status = "PASS" if success else "FAIL"
            details = f"Successful: {successful_calls}/{num_calls}"
            
            metrics = {
                "total_calls": num_calls,
                "successful_calls": successful_calls,
                "failed_calls": failed_calls,
                "success_rate": successful_calls / num_calls
            }
            
            self.log_result(test_name, status, details, metrics)
            return success
            
        except Exception as e:
            self.log_result(test_name, "FAIL", f"Exception: {e}")
            return False
    
    async def _single_concurrent_call(self, call_id: str, call_index: int) -> bool:
        """Handle a single concurrent call."""
        try:
            print(f"   📞 Starting concurrent call {call_index + 1}: {call_id}")
            
            # Send call received webhook
            webhook_success = await self.send_smartflo_webhook(call_id, "call_received")
            if not webhook_success:
                return False
            
            # Connect audio client
            audio_client = VoiceCallTestClient()
            audio_client.session_id = call_id
            
            if not await audio_client.connect():
                return False
            
            # Quick call flow
            await audio_client.register_user()
            await audio_client.start_ai_call()
            await asyncio.sleep(1)
            
            # Send a short phrase
            await audio_client.stream_test_phrase(f"Hello from call {call_index + 1}")
            await asyncio.sleep(2)
            
            # End call
            await audio_client.end_ai_call()
            await self.send_smartflo_webhook(call_id, "call_hangup", {"call_duration": 10})
            
            await audio_client.disconnect()
            
            print(f"   ✅ Completed concurrent call {call_index + 1}")
            return True
            
        except Exception as e:
            print(f"   ❌ Failed concurrent call {call_index + 1}: {e}")
            return False
    
    async def test_audio_quality_scenarios(self) -> bool:
        """Test different audio quality scenarios."""
        test_name = "Audio Quality Scenarios"
        print(f"\n🎯 TESTING: {test_name}")
        print("="*60)
        
        try:
            call_id = f"quality_test_{uuid.uuid4().hex[:8]}"
            
            # Setup call
            await self.send_smartflo_webhook(call_id, "call_received")
            await self.send_smartflo_webhook(call_id, "call_answered_ivr")
            
            audio_client = VoiceCallTestClient()
            audio_client.session_id = call_id
            
            if not await audio_client.connect():
                self.log_result(test_name, "FAIL", "Connection failed")
                return False
            
            await audio_client.register_user()
            await audio_client.start_ai_call()
            await asyncio.sleep(2)
            
            # Test different audio scenarios
            scenarios = [
                ("Normal Speech", "This is a normal speech pattern"),
                ("Fast Speech", "This is fast speech"),
                ("Slow Speech", "This... is... slow... speech"),
                ("Numbers", "One two three four five six seven eight nine zero"),
                ("Technical Terms", "API authentication SSL certificate configuration"),
                ("Mixed Content", "My phone number is five five five, one two three four")
            ]
            
            successful_scenarios = 0
            
            for scenario_name, phrase in scenarios:
                print(f"   🎙️  Testing: {scenario_name}")
                
                try:
                    await audio_client.stream_test_phrase(phrase)
                    await asyncio.sleep(2)  # Wait for AI processing
                    successful_scenarios += 1
                    print(f"      ✅ {scenario_name} completed")
                except Exception as e:
                    print(f"      ❌ {scenario_name} failed: {e}")
            
            # End call
            await audio_client.end_ai_call()
            await self.send_smartflo_webhook(call_id, "call_hangup", {"call_duration": 30})
            await audio_client.disconnect()
            
            success = successful_scenarios >= len(scenarios) * 0.8
            
            status = "PASS" if success else "FAIL"
            details = f"Scenarios passed: {successful_scenarios}/{len(scenarios)}"
            
            metrics = {
                "total_scenarios": len(scenarios),
                "successful_scenarios": successful_scenarios,
                "success_rate": successful_scenarios / len(scenarios)
            }
            
            self.log_result(test_name, status, details, metrics)
            return success
            
        except Exception as e:
            self.log_result(test_name, "FAIL", f"Exception: {e}")
            return False
    
    async def run_all_tests(self) -> None:
        """Run all voice call workflow tests."""
        print("🎙️  COMPLETE VOICE CALL WORKFLOW TESTING")
        print("="*80)
        
        # Check if services are running
        print("🔍 Checking service availability...")
        
        try:
            async with httpx.AsyncClient() as client:
                health_response = await client.get(self.health_url)
                if health_response.status_code != 200:
                    print("❌ Smartflo AI Caller middleware not available")
                    return
                
                health_data = health_response.json()
                print(f"✅ Middleware: {health_data.get('status')}")
                print(f"✅ AI Connection: {health_data.get('ai_voice_mate')}")
        except Exception as e:
            print(f"❌ Service check failed: {e}")
            return
        
        # Run test scenarios
        test_scenarios = [
            ("Complete Call Lifecycle", self.test_complete_call_lifecycle_with_audio),
            ("Audio Quality Scenarios", self.test_audio_quality_scenarios),
            ("Concurrent Voice Calls", lambda: self.test_concurrent_voice_calls(3))
        ]
        
        results = []
        
        for scenario_name, test_func in test_scenarios:
            print(f"\n🚀 Starting: {scenario_name}")
            try:
                result = await test_func()
                results.append(result)
            except Exception as e:
                print(f"❌ {scenario_name} failed with exception: {e}")
                results.append(False)
            
            # Short pause between tests
            await asyncio.sleep(2)
        
        # Final summary
        self.print_test_summary()
        
        passed = sum(results)
        total = len(results)
        print(f"\n🎯 FINAL RESULTS: {passed}/{total} test scenarios passed")
        print("="*80)
    
    def print_test_summary(self) -> None:
        """Print detailed test summary."""
        print("\n📊 DETAILED TEST RESULTS")
        print("="*60)
        
        passed = sum(1 for r in self.test_results if r["status"] == "PASS")
        failed = sum(1 for r in self.test_results if r["status"] == "FAIL")
        total = len(self.test_results)
        
        print(f"📈 Overall Summary:")
        print(f"   Total Tests: {total}")
        print(f"   ✅ Passed: {passed}")
        print(f"   ❌ Failed: {failed}")
        print(f"   Success Rate: {(passed/total)*100:.1f}%")
        
        print(f"\n📋 Individual Results:")
        for result in self.test_results:
            status_emoji = "✅" if result["status"] == "PASS" else "❌"
            print(f"   {status_emoji} {result['test']}: {result['details']}")
        
        # Save detailed results
        with open("voice_call_test_results.json", "w") as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"\n💾 Detailed results saved to voice_call_test_results.json")


if __name__ == "__main__":
    async def main():
        """Main test execution."""
        tester = CompleteVoiceCallWorkflowTest()
        await tester.run_all_tests()
    
    asyncio.run(main())