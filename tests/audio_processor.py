#!/usr/bin/env python3
"""Audio processing utilities for voice call workflow testing."""

import asyncio
import array
import math
import wave
import struct
import time
from typing import List, Generator, Optional, Tuple


class AudioProcessor:
    """Handles audio processing for voice call testing."""
    
    def __init__(self, sample_rate: int = 16000, channels: int = 1, chunk_size: int = 1024):
        """Initialize audio processor."""
        self.sample_rate = sample_rate
        self.channels = channels
        self.chunk_size = chunk_size
        self.bytes_per_sample = 2  # 16-bit PCM
        
    def generate_sine_wave(self, frequency: float, duration: float, amplitude: float = 0.5) -> bytes:
        """Generate a sine wave audio signal."""
        num_samples = int(self.sample_rate * duration)
        samples = []
        
        for i in range(num_samples):
            # Generate sine wave sample
            sample = amplitude * math.sin(2 * math.pi * frequency * i / self.sample_rate)
            # Convert to 16-bit PCM
            sample_int = int(sample * 32767)
            # Clamp to valid range
            sample_int = max(-32768, min(32767, sample_int))
            samples.append(sample_int)
        
        # Convert to bytes
        return struct.pack(f'<{len(samples)}h', *samples)
    
    def generate_speech_like_audio(self, duration: float) -> bytes:
        """Generate speech-like audio with varying frequencies."""
        num_samples = int(self.sample_rate * duration)
        samples = []
        
        # Speech-like frequencies (formants)
        base_freq = 150  # Fundamental frequency
        formant1 = 800   # First formant
        formant2 = 1200  # Second formant
        
        for i in range(num_samples):
            t = i / self.sample_rate
            
            # Create speech-like waveform with multiple harmonics
            sample = 0.0
            sample += 0.4 * math.sin(2 * math.pi * base_freq * t)
            sample += 0.3 * math.sin(2 * math.pi * formant1 * t) * math.exp(-t * 2)
            sample += 0.2 * math.sin(2 * math.pi * formant2 * t) * math.exp(-t * 3)
            
            # Add some noise for realism
            noise = (hash((i * 12345) % 1000000) % 1000 - 500) / 50000.0
            sample += noise * 0.1
            
            # Apply envelope (fade in/out)
            envelope = 1.0
            if t < 0.05:  # Fade in
                envelope = t / 0.05
            elif t > duration - 0.05:  # Fade out
                envelope = (duration - t) / 0.05
            
            sample *= envelope
            
            # Convert to 16-bit PCM
            sample_int = int(sample * 16384)  # Slightly quieter than max
            sample_int = max(-32768, min(32767, sample_int))
            samples.append(sample_int)
        
        return struct.pack(f'<{len(samples)}h', *samples)
    
    def generate_dtmf_tone(self, digit: str, duration: float = 0.2) -> bytes:
        """Generate DTMF tone for a digit."""
        # DTMF frequency pairs
        dtmf_freqs = {
            '1': (697, 1209), '2': (697, 1336), '3': (697, 1477),
            '4': (770, 1209), '5': (770, 1336), '6': (770, 1477),
            '7': (852, 1209), '8': (852, 1336), '9': (852, 1477),
            '*': (941, 1209), '0': (941, 1336), '#': (941, 1477)
        }
        
        if digit not in dtmf_freqs:
            raise ValueError(f"Invalid DTMF digit: {digit}")
        
        freq1, freq2 = dtmf_freqs[digit]
        num_samples = int(self.sample_rate * duration)
        samples = []
        
        for i in range(num_samples):
            t = i / self.sample_rate
            
            # Combine two sine waves for DTMF
            sample = 0.5 * (
                math.sin(2 * math.pi * freq1 * t) +
                math.sin(2 * math.pi * freq2 * t)
            )
            
            # Apply envelope
            envelope = 1.0
            fade_time = 0.01  # 10ms fade
            if t < fade_time:
                envelope = t / fade_time
            elif t > duration - fade_time:
                envelope = (duration - t) / fade_time
            
            sample *= envelope
            
            # Convert to 16-bit PCM
            sample_int = int(sample * 16384)
            sample_int = max(-32768, min(32767, sample_int))
            samples.append(sample_int)
        
        return struct.pack(f'<{len(samples)}h', *samples)
    
    def generate_silence(self, duration: float) -> bytes:
        """Generate silence (zeros) for specified duration."""
        num_samples = int(self.sample_rate * duration)
        return struct.pack(f'<{num_samples}h', *([0] * num_samples))
    
    def audio_to_chunks(self, audio_data: bytes, chunk_duration: float = 0.1) -> Generator[List[int], None, None]:
        """Convert audio data to chunks suitable for WebSocket transmission."""
        samples_per_chunk = int(self.sample_rate * chunk_duration)
        bytes_per_chunk = samples_per_chunk * self.bytes_per_sample
        
        for i in range(0, len(audio_data), bytes_per_chunk):
            chunk = audio_data[i:i + bytes_per_chunk]
            
            # Convert bytes to list of integers
            if len(chunk) % 2 != 0:
                chunk += b'\x00'  # Pad if needed
            
            # Unpack as signed 16-bit integers and convert to unsigned bytes
            samples = struct.unpack(f'<{len(chunk)//2}h', chunk)
            
            # Convert to unsigned 8-bit bytes for transmission
            audio_bytes = []
            for sample in samples:
                # Convert from signed 16-bit to unsigned 8-bit
                byte_val = ((sample + 32768) >> 8) & 0xFF
                audio_bytes.append(byte_val)
            
            yield audio_bytes
    
    def create_test_phrase_audio(self, phrase: str) -> bytes:
        """Create audio that represents a spoken phrase."""
        # For testing, we'll create speech-like audio patterns
        # In a real implementation, this would use TTS or recorded audio
        
        word_count = len(phrase.split())
        base_duration = 0.3 + (word_count * 0.2)  # Estimate duration
        
        audio_segments = []
        
        # Add initial silence
        audio_segments.append(self.generate_silence(0.1))
        
        # Generate speech-like audio
        audio_segments.append(self.generate_speech_like_audio(base_duration))
        
        # Add final silence
        audio_segments.append(self.generate_silence(0.1))
        
        return b''.join(audio_segments)
    
    def save_wav_file(self, audio_data: bytes, filename: str) -> None:
        """Save audio data as WAV file."""
        with wave.open(filename, 'wb') as wav_file:
            wav_file.setnchannels(self.channels)
            wav_file.setsampwidth(self.bytes_per_sample)
            wav_file.setframerate(self.sample_rate)
            wav_file.writeframes(audio_data)
    
    def load_wav_file(self, filename: str) -> bytes:
        """Load audio data from WAV file."""
        try:
            with wave.open(filename, 'rb') as wav_file:
                # Verify format
                if wav_file.getnchannels() != self.channels:
                    raise ValueError(f"Expected {self.channels} channels, got {wav_file.getnchannels()}")
                if wav_file.getsampwidth() != self.bytes_per_sample:
                    raise ValueError(f"Expected {self.bytes_per_sample} bytes per sample, got {wav_file.getsampwidth()}")
                if wav_file.getframerate() != self.sample_rate:
                    raise ValueError(f"Expected {self.sample_rate} Hz, got {wav_file.getframerate()}")
                
                return wav_file.readframes(wav_file.getnframes())
        except FileNotFoundError:
            # If file doesn't exist, generate synthetic audio
            return self.generate_speech_like_audio(1.0)
    
    def analyze_audio_quality(self, audio_data: bytes) -> dict:
        """Analyze audio quality metrics."""
        if len(audio_data) % 2 != 0:
            audio_data += b'\x00'
        
        samples = struct.unpack(f'<{len(audio_data)//2}h', audio_data)
        
        # Calculate RMS (Root Mean Square)
        rms = math.sqrt(sum(s*s for s in samples) / len(samples))
        
        # Calculate peak amplitude
        peak = max(abs(s) for s in samples)
        
        # Calculate zero crossing rate (indicates speech content)
        zero_crossings = sum(1 for i in range(1, len(samples)) 
                           if samples[i-1] * samples[i] < 0)
        zcr = zero_crossings / len(samples)
        
        # Estimate SNR (simplified)
        signal_power = rms ** 2
        noise_floor = min(abs(s) for s in samples if s != 0) if any(s != 0 for s in samples) else 0
        snr = 20 * math.log10(rms / max(noise_floor, 1)) if noise_floor > 0 else float('inf')
        
        return {
            'duration': len(samples) / self.sample_rate,
            'rms': rms,
            'peak': peak,
            'zero_crossing_rate': zcr,
            'snr_db': snr,
            'sample_count': len(samples)
        }
    
    async def stream_audio_realtime(self, audio_data: bytes, chunk_duration: float = 0.1) -> Generator[List[int], None, None]:
        """Stream audio data in real-time chunks with proper timing."""
        chunk_time = chunk_duration
        start_time = time.time()
        chunk_count = 0
        
        for chunk in self.audio_to_chunks(audio_data, chunk_duration):
            # Wait for proper timing
            expected_time = start_time + (chunk_count * chunk_time)
            current_time = time.time()
            
            if current_time < expected_time:
                await asyncio.sleep(expected_time - current_time)
            
            yield chunk
            chunk_count += 1


class AudioTestSuite:
    """Test suite for audio processing functionality."""
    
    def __init__(self):
        self.processor = AudioProcessor()
    
    def create_test_audio_files(self) -> None:
        """Create various test audio files."""
        test_files = {
            'greeting': 'Hello, how can I help you today?',
            'numbers': 'One two three four five',
            'dtmf_sequence': '123*',
            'long_speech': 'This is a longer speech sample to test continuous audio processing and streaming capabilities.',
            'question': 'What is your name?',
            'affirmative': 'Yes, that is correct',
            'negative': 'No, that is not right',
            'help_request': 'Can you help me with my account?'
        }
        
        print("🎵 Creating test audio files...")
        
        # Create speech-like audio files
        for name, text in test_files.items():
            audio_data = self.processor.create_test_phrase_audio(text)
            filename = f"audio/test_samples/{name}.wav"
            self.processor.save_wav_file(audio_data, filename)
            
            # Analyze quality
            quality = self.processor.analyze_audio_quality(audio_data)
            print(f"   ✅ {name}.wav: {quality['duration']:.2f}s, RMS: {quality['rms']:.0f}")
        
        # Create DTMF tones
        dtmf_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '*', '#']
        for digit in dtmf_digits:
            audio_data = self.processor.generate_dtmf_tone(digit)
            filename = f"audio/test_samples/dtmf_{digit}.wav"
            self.processor.save_wav_file(audio_data, filename)
        
        print(f"   ✅ Created {len(dtmf_digits)} DTMF tone files")
        
        # Create synthetic test signals
        synthetic_files = {
            'sine_440hz': self.processor.generate_sine_wave(440, 1.0),
            'sine_1000hz': self.processor.generate_sine_wave(1000, 1.0),
            'silence_1s': self.processor.generate_silence(1.0),
            'speech_like_short': self.processor.generate_speech_like_audio(0.5),
            'speech_like_long': self.processor.generate_speech_like_audio(3.0)
        }
        
        for name, audio_data in synthetic_files.items():
            filename = f"audio/synthetic/{name}.wav"
            self.processor.save_wav_file(audio_data, filename)
        
        print(f"   ✅ Created {len(synthetic_files)} synthetic audio files")
    
    def test_audio_chunking(self) -> None:
        """Test audio chunking functionality."""
        print("\n🔧 Testing audio chunking...")
        
        # Create test audio
        audio_data = self.processor.generate_speech_like_audio(1.0)
        
        # Test different chunk sizes
        chunk_durations = [0.05, 0.1, 0.2]
        
        for duration in chunk_durations:
            chunks = list(self.processor.audio_to_chunks(audio_data, duration))
            expected_chunks = int(1.0 / duration)
            
            print(f"   📦 {duration*1000:.0f}ms chunks: {len(chunks)} chunks (expected ~{expected_chunks})")
            
            # Verify chunk size
            if chunks:
                expected_size = int(self.processor.sample_rate * duration * 2)  # *2 for byte conversion
                actual_size = len(chunks[0])
                print(f"      Chunk size: {actual_size} bytes (expected ~{expected_size})")
    
    def test_audio_quality_analysis(self) -> None:
        """Test audio quality analysis."""
        print("\n📊 Testing audio quality analysis...")
        
        test_cases = [
            ("Sine wave 440Hz", self.processor.generate_sine_wave(440, 1.0)),
            ("Speech-like audio", self.processor.generate_speech_like_audio(1.0)),
            ("Silence", self.processor.generate_silence(1.0)),
            ("DTMF tone", self.processor.generate_dtmf_tone('5'))
        ]
        
        for name, audio_data in test_cases:
            quality = self.processor.analyze_audio_quality(audio_data)
            print(f"   🎵 {name}:")
            print(f"      Duration: {quality['duration']:.2f}s")
            print(f"      RMS: {quality['rms']:.0f}")
            print(f"      Peak: {quality['peak']}")
            print(f"      ZCR: {quality['zero_crossing_rate']:.4f}")
            print(f"      SNR: {quality['snr_db']:.1f} dB")


if __name__ == "__main__":
    # Run audio test suite
    suite = AudioTestSuite()
    suite.create_test_audio_files()
    suite.test_audio_chunking()
    suite.test_audio_quality_analysis()
    
    print("\n🎉 Audio processing test suite completed!")