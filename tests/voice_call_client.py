#!/usr/bin/env python3
"""Enhanced WebSocket client for voice call testing with real audio streaming."""

import asyncio
import json
import time
import uuid
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

import websockets
from websockets.exceptions import ConnectionClosed

from audio_processor import AudioProcessor


class VoiceCallTestClient:
    """Enhanced WebSocket client for testing voice calls with real audio streaming."""
    
    def __init__(self, server_url: str = "ws://localhost:5010"):
        self.server_url = server_url
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.session_id = f"test_session_{uuid.uuid4().hex[:8]}"
        self.is_connected = False
        self.is_streaming_audio = False
        
        # Audio processor
        self.audio_processor = AudioProcessor()
        
        # Message handlers
        self.message_handlers: Dict[str, Callable] = {}
        self.received_messages: List[Dict[str, Any]] = []
        
        # Audio tracking
        self.audio_chunks_sent = 0
        self.ai_responses_received = 0
        self.speech_text_received = []
        
        # Timing metrics
        self.connection_start_time = None
        self.first_audio_sent_time = None
        self.first_response_time = None
        
        print(f"🎯 VoiceCallTestClient initialized with session: {self.session_id}")
    
    async def connect(self) -> bool:
        """Connect to AI Voice Mate WebSocket server."""
        try:
            print(f"🔌 Connecting to {self.server_url}...")
            self.connection_start_time = time.time()
            
            self.websocket = await websockets.connect(
                self.server_url,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=5
            )
            
            self.is_connected = True
            print(f"✅ Connected to AI Voice Mate successfully")
            
            # Start message listener
            asyncio.create_task(self._listen_for_messages())
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to connect: {e}")
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from WebSocket server."""
        self.is_connected = False
        self.is_streaming_audio = False
        
        if self.websocket:
            await self.websocket.close()
            print("🔌 Disconnected from AI Voice Mate")
    
    def register_message_handler(self, message_type: str, handler: Callable) -> None:
        """Register handler for specific message types."""
        self.message_handlers[message_type] = handler
        print(f"📝 Registered handler for message type: {message_type}")
    
    async def _listen_for_messages(self) -> None:
        """Listen for messages from AI Voice Mate."""
        try:
            while self.is_connected and self.websocket:
                try:
                    message_raw = await self.websocket.recv()
                    message = json.loads(message_raw)
                    
                    # Track message
                    message['received_at'] = time.time()
                    self.received_messages.append(message)
                    
                    message_type = message.get('type')
                    print(f"📥 Received: {message_type}")
                    
                    # Handle specific message types
                    if message_type == 'ai_response':
                        self.ai_responses_received += 1
                        if self.first_response_time is None and self.first_audio_sent_time:
                            self.first_response_time = time.time()
                            latency = self.first_response_time - self.first_audio_sent_time
                            print(f"⚡ First response latency: {latency:.2f}s")
                    
                    elif message_type == 'speech_text':
                        speech_text = message.get('data', '')
                        self.speech_text_received.append(speech_text)
                        print(f"🎙️  Speech recognized: '{speech_text}'")
                    
                    elif message_type == 'ai_end_call':
                        print("📞 AI ended call")
                    
                    elif message_type == 'error':
                        print(f"⚠️  AI Error: {message.get('message', 'Unknown error')}")
                    
                    # Call registered handlers
                    if message_type in self.message_handlers:
                        try:
                            await self.message_handlers[message_type](message)
                        except Exception as e:
                            print(f"❌ Handler error for {message_type}: {e}")
                
                except ConnectionClosed:
                    print("🔌 Connection closed by server")
                    break
                except json.JSONDecodeError as e:
                    print(f"❌ Invalid JSON received: {e}")
                except Exception as e:
                    print(f"❌ Message processing error: {e}")
                    
        except Exception as e:
            print(f"❌ Message listener error: {e}")
        finally:
            self.is_connected = False
    
    async def send_message(self, message: Dict[str, Any]) -> None:
        """Send message to AI Voice Mate."""
        if not self.is_connected or not self.websocket:
            raise RuntimeError("Not connected to AI Voice Mate")
        
        try:
            message_json = json.dumps(message)
            await self.websocket.send(message_json)
            
        except Exception as e:
            print(f"❌ Failed to send message: {e}")
            raise
    
    async def register_user(self, user_data: Optional[Dict[str, Any]] = None) -> None:
        """Register user with AI Voice Mate."""
        if user_data is None:
            user_data = {}
        
        default_data = {
            "name": f"Test User {self.session_id[-4:]}",
            "mobile": "+1234567890",
            "userId": self.session_id,
            "sentences": [],
            "sessionType": "call",
            "target": "english_tutor"
        }
        
        # Merge with provided data
        user_data = {**default_data, **user_data}
        user_data["target"] = "+919999999991"
        message = {
            "type": "store_user",
            "session": self.session_id,
            "data": user_data
        }
        
        await self.send_message(message)
        print(f"👤 User registered: {user_data['name']}")
    
    async def start_ai_call(self) -> None:
        """Start AI call session."""
        # Start AI call
        await self.send_message({
            "type": "start_ai_call",
            "session": self.session_id,
            "data": None
        })
        
        # Enable AI listening
        await self.send_message({
            "type": "ai_start_listening",
            "session": self.session_id,
            "data": None
        })
        
        print("🤖 AI call started and listening enabled")
    
    async def end_ai_call(self) -> None:
        """End AI call session."""
        self.is_streaming_audio = False
        
        await self.send_message({
            "type": "end_ai_call",
            "session": self.session_id,
            "data": None
        })
        
        print("📞 AI call ended")
    
    async def send_text_input(self, text: str) -> None:
        """Send text input to AI."""
        await self.send_message({
            "type": "text_input",
            "session": self.session_id,
            "data": text
        })
        
        print(f"💬 Text sent: '{text}'")
    
    async def send_audio_chunk(self, audio_bytes: List[int]) -> None:
        """Send audio chunk to AI Voice Mate."""
        message = {
            "type": "audio_chunk",
            "session": self.session_id,
            "data": audio_bytes
        }
        
        await self.send_message(message)
        self.audio_chunks_sent += 1
        
        # Track first audio sent time
        if self.first_audio_sent_time is None:
            self.first_audio_sent_time = time.time()
    
    async def stop_voice_recording(self) -> None:
        """Stop voice recording."""
        self.is_streaming_audio = False
        
        await self.send_message({
            "type": "voice_action_stop",
            "session": self.session_id,
            "data": "stop_recording"
        })
        
        print("🛑 Voice recording stopped")
    
    async def stream_audio_file(self, audio_data: bytes, chunk_duration: float = 0.1) -> None:
        """Stream audio file in real-time chunks."""
        print(f"🎵 Starting audio stream ({len(audio_data)} bytes, {chunk_duration*1000:.0f}ms chunks)")
        
        self.is_streaming_audio = True
        chunk_count = 0
        
        try:
            async for chunk in self.audio_processor.stream_audio_realtime(audio_data, chunk_duration):
                if not self.is_streaming_audio or not self.is_connected:
                    break
                
                await self.send_audio_chunk(chunk)
                chunk_count += 1
                
                if chunk_count % 10 == 0:  # Progress update every second
                    print(f"   📦 Sent {chunk_count} chunks...")
            
            print(f"✅ Audio streaming completed: {chunk_count} chunks sent")
            
        except Exception as e:
            print(f"❌ Audio streaming error: {e}")
        finally:
            # Send stop signal
            await self.stop_voice_recording()
    
    async def stream_test_phrase(self, phrase: str, chunk_duration: float = 0.1) -> None:
        """Stream a test phrase as audio."""
        print(f"🗣️  Streaming phrase: '{phrase}'")
        
        # Generate audio for the phrase
        audio_data = self.audio_processor.create_test_phrase_audio(phrase)
        
        # Analyze audio quality
        quality = self.audio_processor.analyze_audio_quality(audio_data)
        print(f"   🎵 Generated audio: {quality['duration']:.2f}s, RMS: {quality['rms']:.0f}")
        
        # Stream the audio
        await self.stream_audio_file(audio_data, chunk_duration)
    
    async def simulate_dtmf_sequence(self, digits: str) -> None:
        """Simulate DTMF digit sequence."""
        print(f"🔢 Simulating DTMF sequence: {digits}")
        
        for digit in digits:
            if digit.isdigit() or digit in ['*', '#']:
                # Generate DTMF tone
                dtmf_audio = self.audio_processor.generate_dtmf_tone(digit, 0.2)
                
                print(f"   🔢 Sending DTMF: {digit}")
                await self.stream_audio_file(dtmf_audio, 0.05)
                
                # Short pause between digits
                silence = self.audio_processor.generate_silence(0.1)
                await self.stream_audio_file(silence, 0.05)
    
    def get_call_statistics(self) -> Dict[str, Any]:
        """Get call statistics and metrics."""
        current_time = time.time()
        
        stats = {
            "session_id": self.session_id,
            "connection_duration": current_time - self.connection_start_time if self.connection_start_time else 0,
            "audio_chunks_sent": self.audio_chunks_sent,
            "ai_responses_received": self.ai_responses_received,
            "speech_text_count": len(self.speech_text_received),
            "messages_received": len(self.received_messages),
            "is_connected": self.is_connected,
            "is_streaming": self.is_streaming_audio
        }
        
        if self.first_audio_sent_time and self.first_response_time:
            stats["first_response_latency"] = self.first_response_time - self.first_audio_sent_time
        
        if self.speech_text_received:
            stats["recognized_speech"] = self.speech_text_received
        
        return stats
    
    def print_call_summary(self) -> None:
        """Print a summary of the call session."""
        stats = self.get_call_statistics()
        
        print("\n📊 CALL SESSION SUMMARY")
        print("=" * 40)
        print(f"Session ID: {stats['session_id']}")
        print(f"Duration: {stats['connection_duration']:.2f}s")
        print(f"Audio chunks sent: {stats['audio_chunks_sent']}")
        print(f"AI responses received: {stats['ai_responses_received']}")
        print(f"Speech texts received: {stats['speech_text_count']}")
        print(f"Total messages: {stats['messages_received']}")
        
        if 'first_response_latency' in stats:
            print(f"First response latency: {stats['first_response_latency']:.2f}s")
        
        if stats['recognized_speech']:
            print("Recognized speech:")
            for i, text in enumerate(stats['recognized_speech'], 1):
                print(f"  {i}. '{text}'")
        
        print("=" * 40)


class VoiceCallTestScenarios:
    """Predefined test scenarios for voice calls."""
    
    @staticmethod
    async def basic_greeting_test(client: VoiceCallTestClient) -> bool:
        """Test basic greeting scenario."""
        print("\n🧪 SCENARIO: Basic Greeting Test")
        print("-" * 30)
        
        try:
            # Connect and setup
            if not await client.connect():
                return False
            
            await client.register_user()
            await asyncio.sleep(0.5)
            
            await client.start_ai_call()
            await asyncio.sleep(1)
            
            # Send greeting
            await client.stream_test_phrase("Hello, how are you today?")
            
            # Wait for response
            await asyncio.sleep(3)
            
            await client.end_ai_call()
            await asyncio.sleep(0.5)
            
            # Check results
            stats = client.get_call_statistics()
            success = stats['audio_chunks_sent'] > 0 and stats['ai_responses_received'] > 0
            
            print(f"✅ Basic greeting test: {'PASSED' if success else 'FAILED'}")
            return success
            
        except Exception as e:
            print(f"❌ Basic greeting test failed: {e}")
            return False
        finally:
            await client.disconnect()
    
    @staticmethod
    async def dtmf_integration_test(client: VoiceCallTestClient) -> bool:
        """Test DTMF + voice integration."""
        print("\n🧪 SCENARIO: DTMF Integration Test")
        print("-" * 30)
        
        try:
            if not await client.connect():
                return False
            
            await client.register_user()
            await client.start_ai_call()
            await asyncio.sleep(1)
            
            # Send DTMF sequence
            await client.simulate_dtmf_sequence("123*")
            await asyncio.sleep(1)
            
            # Follow with voice
            await client.stream_test_phrase("I pressed one two three star")
            await asyncio.sleep(3)
            
            await client.end_ai_call()
            
            stats = client.get_call_statistics()
            success = stats['audio_chunks_sent'] > 0
            
            print(f"✅ DTMF integration test: {'PASSED' if success else 'FAILED'}")
            return success
            
        except Exception as e:
            print(f"❌ DTMF integration test failed: {e}")
            return False
        finally:
            await client.disconnect()
    
    @staticmethod
    async def conversation_test(client: VoiceCallTestClient) -> bool:
        """Test multi-turn conversation."""
        print("\n🧪 SCENARIO: Multi-turn Conversation Test")
        print("-" * 30)
        
        try:
            if not await client.connect():
                return False
            
            await client.register_user()
            await client.start_ai_call()
            await asyncio.sleep(1)
            
            # Multi-turn conversation
            phrases = [
                "Hello, I need help with my account",
                "Yes, that's correct",
                "Can you check my balance please?",
                "Thank you for your help"
            ]
            
            for i, phrase in enumerate(phrases, 1):
                print(f"   Turn {i}: '{phrase}'")
                await client.stream_test_phrase(phrase)
                await asyncio.sleep(2)  # Wait for AI response
            
            await client.end_ai_call()
            
            stats = client.get_call_statistics()
            success = stats['audio_chunks_sent'] > 50  # Multiple turns should send many chunks
            
            print(f"✅ Conversation test: {'PASSED' if success else 'FAILED'}")
            return success
            
        except Exception as e:
            print(f"❌ Conversation test failed: {e}")
            return False
        finally:
            await client.disconnect()


if __name__ == "__main__":
    async def main():
        """Run voice call client tests."""
        print("🎙️  VOICE CALL CLIENT TESTING")
        print("=" * 50)
        
        # Test basic functionality
        client = VoiceCallTestClient()
        
        scenarios = [
            VoiceCallTestScenarios.basic_greeting_test,
            VoiceCallTestScenarios.dtmf_integration_test,
            VoiceCallTestScenarios.conversation_test
        ]
        
        results = []
        for scenario in scenarios:
            client = VoiceCallTestClient()  # Fresh client for each test
            result = await scenario(client)
            results.append(result)
            
            if client.is_connected:
                client.print_call_summary()
        
        # Final summary
        passed = sum(results)
        total = len(results)
        print(f"\n🎯 FINAL RESULTS: {passed}/{total} scenarios passed")
        print("=" * 50)
    
    asyncio.run(main())