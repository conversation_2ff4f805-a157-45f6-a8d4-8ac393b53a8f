#!/usr/bin/env python3
"""
Simple test to verify AI Voice Mate audio handling fix.
This test connects directly to AI Voice Mate and tests audio response processing.
"""

import asyncio
import json
import time
from voice_call_client import VoiceCallTestClient


async def test_ai_voice_mate_audio():
    """Test AI Voice Mate audio response handling."""
    print("🎯 TESTING: AI Voice Mate Audio Response Handling")
    print("="*60)
    
    client = VoiceCallTestClient()
    
    try:
        # Connect to AI Voice Mate
        print("\n🔌 PHASE 1: Connecting to AI Voice Mate")
        if not await client.connect():
            print("❌ Failed to connect to AI Voice Mate")
            return False
        
        print("✅ Connected to AI Voice Mate successfully")
        
        # Register user
        print("\n👤 PHASE 2: User Registration")
        await client.register_user({
            "name": "Audio Test User",
            "mobile": "******-AUDIO-TEST",
            "userId": "audio_test_001"
        })
        
        await asyncio.sleep(1)
        print("✅ User registered")
        
        # Start AI call
        print("\n🤖 PHASE 3: Starting AI Call")
        await client.start_ai_call()
        await asyncio.sleep(2)  # Allow AI to initialize
        print("✅ AI call started")
        
        # Send dummy audio to trigger AI response
        print("\n🎤 PHASE 4: Sending Audio to Trigger AI Response")
        dummy_audio = [0] * 100  # Simple dummy audio data
        await client.send_audio_chunk(dummy_audio)
        print("✅ Dummy audio sent")
        
        # Wait for AI response
        print("\n⏳ PHASE 5: Waiting for AI Audio Response")
        print("   Waiting 30 seconds for AI to process and respond...")
        
        initial_responses = client.ai_responses_received
        wait_time = 30
        start_wait = time.time()
        
        while time.time() - start_wait < wait_time:
            await asyncio.sleep(1)
            current_responses = client.ai_responses_received
            if current_responses > initial_responses:
                print(f"🔊 AI audio response received! ({current_responses - initial_responses} responses)")
                break
            print(f"   ⏳ Still waiting... ({int(time.time() - start_wait)}s elapsed)")
        
        # Check results
        final_responses = client.ai_responses_received
        if final_responses > initial_responses:
            print(f"✅ SUCCESS: Received {final_responses - initial_responses} AI audio response(s)")
            print("🎉 Audio handling fix is working!")
            return True
        else:
            print("❌ FAILURE: No AI audio responses received")
            print("   This could mean:")
            print("   1. AI Voice Mate is not sending audio responses")
            print("   2. Audio responses are being sent but not handled properly")
            print("   3. Network/connection issues")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False
    
    finally:
        if client.is_connected:
            await client.disconnect()
        print("\n🔌 Disconnected from AI Voice Mate")


async def main():
    """Main test function."""
    print("🎵 AI Voice Mate Audio Response Test")
    print("This test verifies that the audio handling fix works correctly.")
    print("It will connect to AI Voice Mate and test audio response processing.")
    print()
    
    success = await test_ai_voice_mate_audio()
    
    print("\n" + "="*60)
    if success:
        print("🎉 TEST PASSED: Audio handling is working correctly!")
    else:
        print("❌ TEST FAILED: Audio handling needs further investigation")
    print("="*60)
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
