#!/usr/bin/env python3
"""
Audio Format Compliance Tester for Bi-Directional Audio Streaming.
This module tests compliance with the "Bi-Directional Audio Streaming Integration Document.pdf" specifications.
"""

import base64
import json
import struct
import wave
import io
import audioop
import time
import uuid
from datetime import datetime
from typing import Dict, Any, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class AudioFormatSpec:
    """Audio format specifications from the PDF document."""
    
    # Required format specifications
    ENCODING = "audio/x-mulaw"
    SAMPLE_RATE = 8000
    BIT_RATE = 64  # kbps
    BIT_DEPTH = 8
    CHUNK_DURATION_MS = 20
    CHUNK_SIZE_BYTES = 160  # 20ms at 8kHz = 160 samples = 160 bytes (μ-law)
    
    # Event types
    EVENT_TYPES = [
        "connected", "start", "media", "stop", "dtmf", "mark", "clear"
    ]
    
    # DTMF digits
    DTMF_DIGITS = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '*', '#']


class AudioComplianceTester:
    """Comprehensive audio format compliance tester."""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.test_results: List[Dict[str, Any]] = []
        self.spec = AudioFormatSpec()
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all compliance tests."""
        self.logger.info("Starting comprehensive audio compliance tests")
        
        # Test categories
        test_categories = [
            ("Audio Format Tests", self._test_audio_format_compliance),
            ("Message Format Tests", self._test_message_format_compliance),
            ("Sequence Number Tests", self._test_sequence_number_compliance),
            ("Timing Tests", self._test_timing_compliance),
            ("Audio Conversion Tests", self._test_audio_conversion_compliance),
            ("Base64 Encoding Tests", self._test_base64_encoding_compliance),
            ("DTMF Tests", self._test_dtmf_compliance),
            ("Mark Synchronization Tests", self._test_mark_sync_compliance)
        ]
        
        results = {
            "test_run_timestamp": datetime.now().isoformat(),
            "total_categories": len(test_categories),
            "categories": {},
            "overall_compliance": True,
            "summary": {}
        }
        
        for category_name, test_function in test_categories:
            self.logger.info(f"Running {category_name}...")
            category_results = test_function()
            results["categories"][category_name] = category_results
            
            if not category_results["passed"]:
                results["overall_compliance"] = False
        
        # Generate summary
        results["summary"] = self._generate_summary(results)
        
        self.logger.info(f"Compliance testing completed. Overall: {'PASSED' if results['overall_compliance'] else 'FAILED'}")
        
        return results
    
    def _test_audio_format_compliance(self) -> Dict[str, Any]:
        """Test audio format compliance (8kHz, μ-law, 8-bit)."""
        tests = []
        
        # Test 1: Sample rate compliance
        test_audio_8khz = self._generate_test_audio(sample_rate=8000, duration_ms=100)
        tests.append({
            "name": "Sample Rate 8kHz",
            "passed": len(test_audio_8khz) == int(8000 * 0.1),  # 100ms worth
            "details": f"Generated {len(test_audio_8khz)} samples for 100ms at 8kHz"
        })
        
        # Test 2: μ-law encoding compliance
        pcm_data = struct.pack('<' + 'h' * 160, *range(-32768, -32768 + 160, 1))
        mulaw_data = audioop.lin2ulaw(pcm_data, 2)
        tests.append({
            "name": "μ-law Encoding",
            "passed": len(mulaw_data) == 160 and all(0 <= b <= 255 for b in mulaw_data),
            "details": f"Converted 160 PCM samples to {len(mulaw_data)} μ-law bytes"
        })
        
        # Test 3: Chunk size compliance (20ms = 160 bytes at 8kHz μ-law)
        chunk_size = int(self.spec.SAMPLE_RATE * self.spec.CHUNK_DURATION_MS / 1000)
        tests.append({
            "name": "Chunk Size (20ms)",
            "passed": chunk_size == self.spec.CHUNK_SIZE_BYTES,
            "details": f"20ms at 8kHz = {chunk_size} bytes"
        })
        
        # Test 4: Bit depth compliance (8-bit μ-law)
        tests.append({
            "name": "Bit Depth (8-bit)",
            "passed": self.spec.BIT_DEPTH == 8,
            "details": f"μ-law uses {self.spec.BIT_DEPTH}-bit encoding"
        })
        
        passed_tests = sum(1 for test in tests if test["passed"])
        
        return {
            "passed": passed_tests == len(tests),
            "total_tests": len(tests),
            "passed_tests": passed_tests,
            "tests": tests
        }
    
    def _test_message_format_compliance(self) -> Dict[str, Any]:
        """Test message format compliance per PDF specification."""
        tests = []
        
        # Test 1: Connected event format
        connected_event = {"event": "connected"}
        tests.append({
            "name": "Connected Event Format",
            "passed": connected_event.get("event") == "connected",
            "details": f"Connected event: {json.dumps(connected_event)}"
        })
        
        # Test 2: Start event format
        start_event = self._create_test_start_event()
        required_start_fields = ["event", "sequenceNumber", "start", "streamSid"]
        start_test = all(field in start_event for field in required_start_fields)
        start_test = start_test and start_event["event"] == "start"
        start_test = start_test and "mediaFormat" in start_event["start"]
        tests.append({
            "name": "Start Event Format",
            "passed": start_test,
            "details": f"Start event has all required fields: {required_start_fields}"
        })
        
        # Test 3: Media event format
        media_event = self._create_test_media_event()
        required_media_fields = ["event", "sequenceNumber", "media", "streamSid"]
        media_test = all(field in media_event for field in required_media_fields)
        media_test = media_test and media_event["event"] == "media"
        media_test = media_test and all(field in media_event["media"] for field in ["chunk", "timestamp", "payload"])
        tests.append({
            "name": "Media Event Format",
            "passed": media_test,
            "details": f"Media event has all required fields"
        })
        
        # Test 4: Stop event format
        stop_event = self._create_test_stop_event()
        required_stop_fields = ["event", "sequenceNumber", "stop", "streamSid"]
        stop_test = all(field in stop_event for field in required_stop_fields)
        stop_test = stop_test and stop_event["event"] == "stop"
        tests.append({
            "name": "Stop Event Format",
            "passed": stop_test,
            "details": f"Stop event has all required fields"
        })
        
        passed_tests = sum(1 for test in tests if test["passed"])
        
        return {
            "passed": passed_tests == len(tests),
            "total_tests": len(tests),
            "passed_tests": passed_tests,
            "tests": tests
        }
    
    def _test_sequence_number_compliance(self) -> Dict[str, Any]:
        """Test sequence number management compliance."""
        tests = []
        
        # Test 1: Sequence number incrementation
        sequence_numbers = []
        for i in range(10):
            event = self._create_test_media_event(sequence_number=i + 1)
            sequence_numbers.append(int(event["sequenceNumber"]))
        
        sequential_test = all(
            sequence_numbers[i] == i + 1 
            for i in range(len(sequence_numbers))
        )
        tests.append({
            "name": "Sequential Numbering",
            "passed": sequential_test,
            "details": f"Sequence numbers: {sequence_numbers}"
        })
        
        # Test 2: Sequence number string format
        event = self._create_test_media_event(sequence_number=42)
        string_format_test = isinstance(event["sequenceNumber"], str)
        tests.append({
            "name": "String Format",
            "passed": string_format_test,
            "details": f"Sequence number type: {type(event['sequenceNumber'])}"
        })
        
        passed_tests = sum(1 for test in tests if test["passed"])
        
        return {
            "passed": passed_tests == len(tests),
            "total_tests": len(tests),
            "passed_tests": passed_tests,
            "tests": tests
        }
    
    def _test_timing_compliance(self) -> Dict[str, Any]:
        """Test timing compliance (20ms chunks)."""
        tests = []
        
        # Test 1: Chunk timing calculation
        chunk_duration_ms = self.spec.CHUNK_DURATION_MS
        sample_rate = self.spec.SAMPLE_RATE
        expected_samples_per_chunk = int(sample_rate * chunk_duration_ms / 1000)
        
        tests.append({
            "name": "Chunk Duration Calculation",
            "passed": expected_samples_per_chunk == 160,
            "details": f"{chunk_duration_ms}ms at {sample_rate}Hz = {expected_samples_per_chunk} samples"
        })
        
        # Test 2: Timestamp incrementation (20ms steps)
        timestamps = []
        for chunk_num in range(1, 6):
            timestamp = chunk_num * chunk_duration_ms
            timestamps.append(timestamp)
        
        timing_test = all(
            timestamps[i] - timestamps[i-1] == chunk_duration_ms 
            for i in range(1, len(timestamps))
        )
        tests.append({
            "name": "Timestamp Incrementation",
            "passed": timing_test,
            "details": f"Timestamps: {timestamps} (20ms steps)"
        })
        
        passed_tests = sum(1 for test in tests if test["passed"])
        
        return {
            "passed": passed_tests == len(tests),
            "total_tests": len(tests),
            "passed_tests": passed_tests,
            "tests": tests
        }
    
    def _test_audio_conversion_compliance(self) -> Dict[str, Any]:
        """Test audio conversion compliance (PCM ↔ μ-law)."""
        tests = []
        
        # Test 1: PCM to μ-law conversion
        pcm_samples = [0, 1000, -1000, 32767, -32768]
        pcm_data = struct.pack('<' + 'h' * len(pcm_samples), *pcm_samples)
        mulaw_data = audioop.lin2ulaw(pcm_data, 2)
        
        tests.append({
            "name": "PCM to μ-law Conversion",
            "passed": len(mulaw_data) == len(pcm_samples),
            "details": f"Converted {len(pcm_samples)} PCM samples to {len(mulaw_data)} μ-law bytes"
        })
        
        # Test 2: μ-law to PCM conversion (round trip)
        pcm_restored = audioop.ulaw2lin(mulaw_data, 2)
        restored_samples = struct.unpack('<' + 'h' * len(pcm_samples), pcm_restored)
        
        # μ-law is lossy, so we check if values are reasonably close
        conversion_quality = all(
            abs(original - restored) < 1000  # Allow some loss due to μ-law quantization
            for original, restored in zip(pcm_samples, restored_samples)
        )
        tests.append({
            "name": "μ-law to PCM Conversion",
            "passed": conversion_quality,
            "details": f"Original: {pcm_samples}, Restored: {list(restored_samples)}"
        })
        
        # Test 3: Conversion preserves audio characteristics
        # Generate a simple tone and test conversion
        tone_samples = [int(100 * (i % 100 - 50)) for i in range(160)]  # Reduced amplitude to fit 16-bit range
        tone_pcm = struct.pack('<' + 'h' * len(tone_samples), *tone_samples)
        tone_mulaw = audioop.lin2ulaw(tone_pcm, 2)
        tone_restored = audioop.ulaw2lin(tone_mulaw, 2)
        
        tests.append({
            "name": "Audio Characteristic Preservation",
            "passed": len(tone_restored) == len(tone_pcm),
            "details": f"Tone conversion: {len(tone_pcm)} → {len(tone_mulaw)} → {len(tone_restored)}"
        })
        
        passed_tests = sum(1 for test in tests if test["passed"])
        
        return {
            "passed": passed_tests == len(tests),
            "total_tests": len(tests),
            "passed_tests": passed_tests,
            "tests": tests
        }
    
    def _test_base64_encoding_compliance(self) -> Dict[str, Any]:
        """Test base64 encoding compliance."""
        tests = []
        
        # Test 1: Base64 encoding of μ-law data
        mulaw_data = bytes(range(160))  # Test data
        b64_encoded = base64.b64encode(mulaw_data).decode('utf-8')
        
        tests.append({
            "name": "Base64 Encoding",
            "passed": isinstance(b64_encoded, str) and len(b64_encoded) > 0,
            "details": f"Encoded {len(mulaw_data)} bytes to {len(b64_encoded)} base64 chars"
        })
        
        # Test 2: Base64 decoding (round trip)
        decoded_data = base64.b64decode(b64_encoded)
        
        tests.append({
            "name": "Base64 Decoding",
            "passed": decoded_data == mulaw_data,
            "details": f"Round trip successful: {len(mulaw_data)} bytes"
        })
        
        # Test 3: Media event payload format
        media_event = self._create_test_media_event()
        payload = media_event["media"]["payload"]
        
        try:
            decoded_payload = base64.b64decode(payload)
            payload_test = True
        except Exception:
            payload_test = False
        
        tests.append({
            "name": "Media Event Payload",
            "passed": payload_test,
            "details": f"Payload can be base64 decoded: {len(payload)} chars"
        })
        
        passed_tests = sum(1 for test in tests if test["passed"])
        
        return {
            "passed": passed_tests == len(tests),
            "total_tests": len(tests),
            "passed_tests": passed_tests,
            "tests": tests
        }
    
    def _test_dtmf_compliance(self) -> Dict[str, Any]:
        """Test DTMF event compliance."""
        tests = []
        
        # Test 1: DTMF digit validation
        valid_digits = self.spec.DTMF_DIGITS
        for digit in valid_digits:
            dtmf_event = self._create_test_dtmf_event(digit)
            digit_test = dtmf_event["dtmf"]["digit"] == digit
            tests.append({
                "name": f"DTMF Digit '{digit}'",
                "passed": digit_test,
                "details": f"DTMF event for digit '{digit}'"
            })
        
        # Test 2: DTMF event format
        dtmf_event = self._create_test_dtmf_event("5")
        required_fields = ["event", "streamSid", "sequenceNumber", "dtmf"]
        format_test = all(field in dtmf_event for field in required_fields)
        format_test = format_test and dtmf_event["event"] == "dtmf"
        format_test = format_test and "digit" in dtmf_event["dtmf"]
        
        tests.append({
            "name": "DTMF Event Format",
            "passed": format_test,
            "details": f"DTMF event has all required fields"
        })
        
        passed_tests = sum(1 for test in tests if test["passed"])
        
        return {
            "passed": passed_tests == len(tests),
            "total_tests": len(tests),
            "passed_tests": passed_tests,
            "tests": tests
        }
    
    def _test_mark_sync_compliance(self) -> Dict[str, Any]:
        """Test mark synchronization compliance."""
        tests = []
        
        # Test 1: Mark event format
        mark_event = self._create_test_mark_event("test_mark")
        required_fields = ["event", "sequenceNumber", "streamSid", "mark"]
        format_test = all(field in mark_event for field in required_fields)
        format_test = format_test and mark_event["event"] == "mark"
        format_test = format_test and "name" in mark_event["mark"]
        
        tests.append({
            "name": "Mark Event Format",
            "passed": format_test,
            "details": f"Mark event has all required fields"
        })
        
        # Test 2: Mark name preservation
        test_mark_names = ["audio_end", "playback_complete", "sync_point_1", "mark_123"]
        for mark_name in test_mark_names:
            mark_event = self._create_test_mark_event(mark_name)
            name_test = mark_event["mark"]["name"] == mark_name
            tests.append({
                "name": f"Mark Name '{mark_name}'",
                "passed": name_test,
                "details": f"Mark name preserved: '{mark_name}'"
            })
        
        # Test 3: Clear event format
        clear_event = self._create_test_clear_event()
        clear_fields = ["event", "streamSid"]
        clear_test = all(field in clear_event for field in clear_fields)
        clear_test = clear_test and clear_event["event"] == "clear"
        
        tests.append({
            "name": "Clear Event Format",
            "passed": clear_test,
            "details": f"Clear event has required fields"
        })
        
        passed_tests = sum(1 for test in tests if test["passed"])
        
        return {
            "passed": passed_tests == len(tests),
            "total_tests": len(tests),
            "passed_tests": passed_tests,
            "tests": tests
        }
    
    # Helper methods for creating test events
    def _create_test_start_event(self) -> Dict[str, Any]:
        """Create a test start event."""
        return {
            "event": "start",
            "sequenceNumber": "1",
            "start": {
                "streamSid": f"MZ{uuid.uuid4().hex[:30]}",
                "accountSid": f"AC{uuid.uuid4().hex[:30]}",
                "callSid": f"CA{uuid.uuid4().hex[:30]}",
                "from": "+**********",
                "to": "+**********",
                "mediaFormat": {
                    "encoding": self.spec.ENCODING,
                    "sampleRate": self.spec.SAMPLE_RATE,
                    "bitRate": self.spec.BIT_RATE,
                    "bitDepth": self.spec.BIT_DEPTH
                },
                "customParameters": {
                    "TestCall": "true"
                }
            },
            "streamSid": f"MZ{uuid.uuid4().hex[:30]}"
        }
    
    def _create_test_media_event(self, sequence_number: int = 2) -> Dict[str, Any]:
        """Create a test media event."""
        # Create test μ-law audio data
        test_audio = bytes(range(160))  # 160 bytes of test data
        payload = base64.b64encode(test_audio).decode('utf-8')
        
        return {
            "event": "media",
            "sequenceNumber": str(sequence_number),
            "media": {
                "chunk": "1",
                "timestamp": "20",
                "payload": payload
            },
            "streamSid": f"MZ{uuid.uuid4().hex[:30]}"
        }
    
    def _create_test_stop_event(self) -> Dict[str, Any]:
        """Create a test stop event."""
        return {
            "event": "stop",
            "sequenceNumber": "3",
            "stop": {
                "accountSid": f"AC{uuid.uuid4().hex[:30]}",
                "callSid": f"CA{uuid.uuid4().hex[:30]}",
                "reason": "Test completed"
            },
            "streamSid": f"MZ{uuid.uuid4().hex[:30]}"
        }
    
    def _create_test_dtmf_event(self, digit: str) -> Dict[str, Any]:
        """Create a test DTMF event."""
        return {
            "event": "dtmf",
            "streamSid": f"MZ{uuid.uuid4().hex[:30]}",
            "sequenceNumber": "4",
            "dtmf": {
                "digit": digit
            }
        }
    
    def _create_test_mark_event(self, mark_name: str) -> Dict[str, Any]:
        """Create a test mark event."""
        return {
            "event": "mark",
            "sequenceNumber": "5",
            "streamSid": f"MZ{uuid.uuid4().hex[:30]}",
            "mark": {
                "name": mark_name
            }
        }
    
    def _create_test_clear_event(self) -> Dict[str, Any]:
        """Create a test clear event."""
        return {
            "event": "clear",
            "streamSid": f"MZ{uuid.uuid4().hex[:30]}"
        }
    
    def _generate_test_audio(self, sample_rate: int, duration_ms: int) -> List[int]:
        """Generate test audio samples."""
        num_samples = int(sample_rate * duration_ms / 1000)
        # Generate a simple sine wave
        samples = []
        for i in range(num_samples):
            # Simple sine approximation
            sample = int(1000 * ((i % 100) - 50))
            samples.append(sample)
        return samples
    
    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate test summary."""
        total_tests = 0
        passed_tests = 0
        
        for category_name, category_results in results["categories"].items():
            total_tests += category_results["total_tests"]
            passed_tests += category_results["passed_tests"]
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "pass_rate": passed_tests / total_tests if total_tests > 0 else 0,
            "compliance_status": "FULLY COMPLIANT" if passed_tests == total_tests else "NON-COMPLIANT"
        }
    
    def save_results(self, results: Dict[str, Any], filename: str = None) -> str:
        """Save test results to file."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"audio_compliance_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        self.logger.info(f"Compliance test results saved to: {filename}")
        return filename
    
    def print_results(self, results: Dict[str, Any]):
        """Print formatted test results."""
        print("\n" + "="*80)
        print("AUDIO FORMAT COMPLIANCE TEST RESULTS")
        print("="*80)
        
        print(f"Test Run: {results['test_run_timestamp']}")
        print(f"Overall Compliance: {'✅ PASSED' if results['overall_compliance'] else '❌ FAILED'}")
        
        summary = results["summary"]
        print(f"Tests: {summary['passed_tests']}/{summary['total_tests']} passed ({summary['pass_rate']:.1%})")
        print(f"Status: {summary['compliance_status']}")
        
        print("\nDETAILED RESULTS:")
        print("-"*80)
        
        for category_name, category_results in results["categories"].items():
            status = "✅" if category_results["passed"] else "❌"
            print(f"{status} {category_name}: {category_results['passed_tests']}/{category_results['total_tests']}")
            
            if not category_results["passed"]:
                for test in category_results["tests"]:
                    if not test["passed"]:
                        print(f"   ❌ {test['name']}: {test['details']}")
        
        print("\n" + "="*80)


def main():
    """Main function to run compliance tests."""
    logging.basicConfig(level=logging.INFO)
    
    print("🎙️ Audio Format Compliance Tester")
    print("📋 Testing compliance with Bi-Directional Audio Streaming Integration Document")
    print("="*80)
    
    # Create tester
    tester = AudioComplianceTester()
    
    # Run all tests
    results = tester.run_all_tests()
    
    # Print results
    tester.print_results(results)
    
    # Save results
    filename = tester.save_results(results)
    print(f"\n📄 Detailed results saved to: {filename}")
    
    # Return status for scripting
    return 0 if results["overall_compliance"] else 1


if __name__ == "__main__":
    exit(main())