#!/bin/bash

# Voice Call Test Suite Startup Script
# This script provides an easy way to start and manage the voice call testing environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SMARTFLO_PORT=8000
AI_VOICE_MATE_PORT=5010
FLASK_TEST_PORT=5002

# Functions
print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}🎙️  Voice Call Test Suite${NC}"
    echo -e "${BLUE}📞 Bi-Directional Audio Streaming Test Environment${NC}"
    echo -e "${BLUE}================================================${NC}"
    echo
}

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_port() {
    local port=$1
    local service=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_status "$service is running on port $port ✅"
        return 0
    else
        print_warning "$service is not running on port $port ❌"
        return 1
    fi
}

check_dependencies() {
    print_status "Checking dependencies..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed"
        exit 1
    fi
    
    # Check pip packages
    if ! python3 -c "import flask, flask_socketio, websockets" 2>/dev/null; then
        print_warning "Some Python dependencies are missing"
        print_status "Installing dependencies..."
        pip3 install -r requirements_enhanced_test_app.txt
    fi
    
    print_status "Dependencies check completed ✅"
}

check_services() {
    print_status "Checking service availability..."
    
    local services_ready=true
    
    if ! check_port $SMARTFLO_PORT "smartflo-ai-caller"; then
        services_ready=false
    fi
    
    if ! check_port $AI_VOICE_MATE_PORT "AI Voice Mate"; then
        services_ready=false
    fi
    
    if [ "$services_ready" = false ]; then
        print_warning "Some services are not running. The test suite will still start but may have limited functionality."
        echo
        print_status "To start missing services:"
        print_status "  - smartflo-ai-caller: python -m smartflo_ai_caller.main"
        print_status "  - AI Voice Mate: start your AI Voice Mate service on port $AI_VOICE_MATE_PORT"
        echo
    else
        print_status "All services are ready ✅"
    fi
}

run_compliance_tests() {
    print_status "Running audio compliance tests..."
    echo
    
    if python3 audio_compliance_tester.py; then
        print_status "Compliance tests passed ✅"
    else
        print_error "Compliance tests failed ❌"
        echo "Check the generated compliance report for details."
    fi
    echo
}

start_flask_app() {
    print_status "Starting Enhanced Flask Test Application..."
    print_status "Access the test interface at: http://localhost:$FLASK_TEST_PORT"
    echo
    
    # Check if port is already in use
    if check_port $FLASK_TEST_PORT "Flask Test App"; then
        print_error "Port $FLASK_TEST_PORT is already in use"
        print_status "Please stop the existing service or use a different port"
        exit 1
    fi
    
    # Start the Flask app
    python3 enhanced_voice_call_test_app.py
}

run_integration_tests() {
    print_status "Running integration tests..."
    echo
    
    python3 integration_client.py
    
    print_status "Integration tests completed"
    echo
}

show_help() {
    echo "Voice Call Test Suite - Usage Options:"
    echo
    echo "  $0 [command]"
    echo
    echo "Commands:"
    echo "  start           Start the enhanced Flask test application (default)"
    echo "  check           Check system status and dependencies"
    echo "  compliance      Run audio format compliance tests"
    echo "  integration     Run direct integration tests"
    echo "  test-all        Run all tests (compliance + integration)"
    echo "  help            Show this help message"
    echo
    echo "Examples:"
    echo "  $0                    # Start Flask test application"
    echo "  $0 start              # Start Flask test application"
    echo "  $0 check              # Check system status"
    echo "  $0 compliance         # Run compliance tests only"
    echo "  $0 integration        # Run integration tests only"
    echo "  $0 test-all           # Run all tests"
    echo
    echo "For detailed usage instructions, see VOICE_CALL_TESTING_GUIDE.md"
}

# Main execution
main() {
    print_header
    
    local command=${1:-start}
    
    case $command in
        "start")
            check_dependencies
            check_services
            start_flask_app
            ;;
        
        "check")
            check_dependencies
            check_services
            print_status "System check completed"
            ;;
        
        "compliance")
            check_dependencies
            run_compliance_tests
            ;;
        
        "integration")
            check_dependencies
            check_services
            run_integration_tests
            ;;
        
        "test-all")
            check_dependencies
            check_services
            run_compliance_tests
            run_integration_tests
            ;;
        
        "help"|"-h"|"--help")
            show_help
            ;;
        
        *)
            print_error "Unknown command: $command"
            echo
            show_help
            exit 1
            ;;
    esac
}

# Trap Ctrl+C and cleanup
cleanup() {
    echo
    print_status "Shutting down test suite..."
    # Kill any background processes if needed
    exit 0
}

trap cleanup SIGINT SIGTERM

# Run main function
main "$@"