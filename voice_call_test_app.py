#!/usr/bin/env python3
"""
Flask web application for testing bi-directional audio streaming voice calls.
This application provides a simple web interface to test the voice call functionality
described in the "Bi-Directional Audio Streaming Integration Document.pdf".
"""

import asyncio
import base64
import json
import uuid
import wave
import io
import struct
from datetime import datetime
from typing import Dict, Any, List

from flask import Flask, render_template_string, request, jsonify, session
from flask_socketio import SocketIO, emit, join_room, leave_room
import threading
import time

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'voice_call_test_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# Global state for managing calls
active_calls: Dict[str, Dict[str, Any]] = {}
call_stats: Dict[str, Dict[str, Any]] = {}


class MockBiDirectionalStreaming:
    """Mock implementation of bi-directional streaming for testing."""
    
    def __init__(self):
        self.sessions = {}
        self.sequence_numbers = {}
        
    def create_session(self, call_id: str, caller: str, callee: str) -> Dict[str, Any]:
        """Create a new streaming session."""
        session_data = {
            "call_id": call_id,
            "stream_id": f"MZ{uuid.uuid4().hex[:30]}",
            "account_id": f"AC{uuid.uuid4().hex[:30]}",
            "caller": caller,
            "callee": callee,
            "created_at": datetime.now().isoformat(),
            "state": "initialized",
            "audio_chunks_sent": 0,
            "audio_chunks_received": 0,
            "marks_sent": 0,
            "marks_received": 0
        }
        
        self.sessions[call_id] = session_data
        self.sequence_numbers[call_id] = 1
        
        return session_data
    
    def create_connected_event(self) -> Dict[str, Any]:
        """Create connected event as per PDF specification."""
        return {"event": "connected"}
    
    def create_start_event(self, call_id: str) -> Dict[str, Any]:
        """Create start event as per PDF specification."""
        session = self.sessions[call_id]
        seq_num = self.sequence_numbers[call_id]
        self.sequence_numbers[call_id] += 1
        
        return {
            "event": "start",
            "sequenceNumber": str(seq_num),
            "start": {
                "streamSid": session["stream_id"],
                "accountSid": session["account_id"],
                "callSid": call_id,
                "from": session["caller"],
                "to": session["callee"],
                "mediaFormat": {
                    "encoding": "audio/x-mulaw",
                    "sampleRate": 8000,
                    "bitRate": 64,
                    "bitDepth": 8
                },
                "customParameters": {
                    "TestCall": "true",
                    "Timestamp": session["created_at"]
                }
            },
            "streamSid": session["stream_id"]
        }
    
    def create_media_event(self, call_id: str, audio_data: bytes, chunk_number: int) -> Dict[str, Any]:
        """Create media event as per PDF specification."""
        session = self.sessions[call_id]
        seq_num = self.sequence_numbers[call_id]
        self.sequence_numbers[call_id] += 1
        
        # Convert audio to μ-law and base64 encode
        mulaw_data = self.pcm_to_mulaw(audio_data)
        payload = base64.b64encode(mulaw_data).decode('utf-8')
        
        session["audio_chunks_sent"] += 1
        
        return {
            "event": "media",
            "sequenceNumber": str(seq_num),
            "media": {
                "chunk": str(chunk_number),
                "timestamp": str(chunk_number * 20),  # 20ms per chunk
                "payload": payload
            },
            "streamSid": session["stream_id"]
        }
    
    def create_mark_event(self, call_id: str, mark_name: str) -> Dict[str, Any]:
        """Create mark event as per PDF specification."""
        session = self.sessions[call_id]
        seq_num = self.sequence_numbers[call_id]
        self.sequence_numbers[call_id] += 1
        
        session["marks_sent"] += 1
        
        return {
            "event": "mark",
            "sequenceNumber": str(seq_num),
            "streamSid": session["stream_id"],
            "mark": {
                "name": mark_name
            }
        }
    
    def create_stop_event(self, call_id: str, reason: str = "Call ended") -> Dict[str, Any]:
        """Create stop event as per PDF specification."""
        session = self.sessions[call_id]
        seq_num = self.sequence_numbers[call_id]
        self.sequence_numbers[call_id] += 1
        
        session["state"] = "stopped"
        
        return {
            "event": "stop",
            "sequenceNumber": str(seq_num),
            "stop": {
                "accountSid": session["account_id"],
                "callSid": call_id,
                "reason": reason
            },
            "streamSid": session["stream_id"]
        }
    
    def process_inbound_audio(self, call_id: str, audio_payload: str) -> bytes:
        """Process inbound audio from base64 μ-law to PCM."""
        session = self.sessions[call_id]
        session["audio_chunks_received"] += 1
        
        # Decode base64 to μ-law
        mulaw_data = base64.b64decode(audio_payload)
        
        # Convert μ-law to PCM
        pcm_data = self.mulaw_to_pcm(mulaw_data)
        
        return pcm_data
    
    @staticmethod
    def pcm_to_mulaw(pcm_data: bytes) -> bytes:
        """Convert PCM to μ-law (simplified implementation)."""
        if len(pcm_data) % 2 != 0:
            pcm_data += b'\\x00'
        
        # Unpack as 16-bit signed integers
        samples = struct.unpack(f'<{len(pcm_data)//2}h', pcm_data)
        
        # Simple μ-law conversion (not exact but functional for testing)
        mulaw_samples = []
        for sample in samples:
            # Clamp to valid range
            sample = max(-32768, min(32767, sample))
            # Simple quantization to 8-bit μ-law
            mulaw_byte = abs(sample) >> 8
            mulaw_byte = min(255, mulaw_byte)
            if sample < 0:
                mulaw_byte |= 0x80
            mulaw_samples.append(mulaw_byte)
        
        return bytes(mulaw_samples)
    
    @staticmethod
    def mulaw_to_pcm(mulaw_data: bytes) -> bytes:
        """Convert μ-law to PCM (simplified implementation)."""
        pcm_samples = []
        for byte in mulaw_data:
            # Simple μ-law to linear conversion
            sign = -1 if byte & 0x80 else 1
            magnitude = (byte & 0x7F) << 8
            sample = sign * magnitude
            pcm_samples.append(sample)
        
        return struct.pack(f'<{len(pcm_samples)}h', *pcm_samples)
    
    def get_session_stats(self, call_id: str) -> Dict[str, Any]:
        """Get session statistics."""
        return self.sessions.get(call_id, {})


# Global streaming instance
streaming = MockBiDirectionalStreaming()


# HTML Template for the voice call test interface
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Call Test - Bi-Directional Audio Streaming</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.6.2/socket.io.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .call-controls {
            grid-column: 1 / -1;
            text-align: center;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .btn.danger:hover {
            background: #c82333;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.success:hover {
            background: #218838;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.calling {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .audio-controls {
            margin: 20px 0;
        }
        
        .audio-visualizer {
            width: 100%;
            height: 100px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            margin: 10px 0;
            position: relative;
        }
        
        #inputCanvas, #outputCanvas {
            width: 100%;
            height: 100%;
        }
        
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        
        .stats-table th, .stats-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .stats-table th {
            background: #f2f2f2;
        }
        
        .log-container {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #f9f9f9;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px;
        }
        
        .log-outbound {
            color: #0066cc;
        }
        
        .log-inbound {
            color: #cc6600;
        }
        
        .log-error {
            color: #cc0000;
            font-weight: bold;
        }
        
        .form-group {
            margin: 10px 0;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
        }
        
        .metric-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎙️ Voice Call Test Application</h1>
        <p>Testing Bi-Directional Audio Streaming Integration</p>
    </div>

    <div class="container">
        <div class="panel call-controls">
            <h3>Call Controls</h3>
            <div class="form-group">
                <label for="callerNumber">From Number:</label>
                <input type="text" id="callerNumber" value="+1234567890" placeholder="Caller number">
            </div>
            <div class="form-group">
                <label for="calleeNumber">To Number:</label>
                <input type="text" id="calleeNumber" value="+0987654321" placeholder="Callee number">
            </div>
            
            <div class="audio-controls">
                <button id="startCallBtn" class="btn success">📞 Start Call</button>
                <button id="endCallBtn" class="btn danger" disabled>📞 End Call</button>
                <button id="startRecordingBtn" class="btn" disabled>🎤 Start Recording</button>
                <button id="stopRecordingBtn" class="btn" disabled>⏹️ Stop Recording</button>
                <button id="sendMarkBtn" class="btn" disabled>🏷️ Send Mark</button>
                <button id="clearBufferBtn" class="btn" disabled>🗑️ Clear Buffer</button>
            </div>
            
            <div id="callStatus" class="status disconnected">Disconnected</div>
        </div>

        <div class="panel">
            <h3>📊 Audio Input (Microphone)</h3>
            <div class="audio-visualizer">
                <canvas id="inputCanvas"></canvas>
            </div>
            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-value" id="inputLevel">0</div>
                    <div class="metric-label">Input Level</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="chunksSent">0</div>
                    <div class="metric-label">Chunks Sent</div>
                </div>
            </div>
        </div>

        <div class="panel">
            <h3>🔊 Audio Output (Speaker)</h3>
            <div class="audio-visualizer">
                <canvas id="outputCanvas"></canvas>
            </div>
            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-value" id="outputLevel">0</div>
                    <div class="metric-label">Output Level</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="chunksReceived">0</div>
                    <div class="metric-label">Chunks Received</div>
                </div>
            </div>
        </div>

        <div class="panel">
            <h3>📈 Call Statistics</h3>
            <table class="stats-table">
                <tr><th>Metric</th><th>Value</th></tr>
                <tr><td>Call Duration</td><td id="callDuration">0s</td></tr>
                <tr><td>Audio Latency</td><td id="audioLatency">0ms</td></tr>
                <tr><td>Marks Sent</td><td id="marksSent">0</td></tr>
                <tr><td>Marks Received</td><td id="marksReceived">0</td></tr>
                <tr><td>Stream ID</td><td id="streamId">-</td></tr>
                <tr><td>Sequence Number</td><td id="sequenceNumber">0</td></tr>
            </table>
        </div>

        <div class="panel">
            <h3>📋 Event Log</h3>
            <div class="log-container" id="eventLog">
                <div class="log-entry">Ready to start voice call test...</div>
            </div>
            <button id="clearLogBtn" class="btn">Clear Log</button>
        </div>
    </div>

    <script>
        // WebSocket connection
        const socket = io();
        
        // Global state
        let callActive = false;
        let recording = false;
        let callStartTime = null;
        let audioContext = null;
        let mediaStream = null;
        let audioProcessor = null;
        let currentCallId = null;
        let chunkCounter = 0;
        let markCounter = 0;
        
        // Audio visualization
        let inputAnalyser = null;
        let outputAnalyser = null;
        let inputCanvas = null;
        let outputCanvas = null;
        let inputCtx = null;
        let outputCtx = null;
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeAudio();
            setupEventListeners();
            setupVisualization();
        });
        
        function initializeAudio() {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                logEvent('Audio context initialized', 'system');
            } catch (e) {
                logEvent('Failed to initialize audio context: ' + e.message, 'error');
            }
        }
        
        function setupVisualization() {
            inputCanvas = document.getElementById('inputCanvas');
            outputCanvas = document.getElementById('outputCanvas');
            inputCtx = inputCanvas.getContext('2d');
            outputCtx = outputCanvas.getContext('2d');
            
            // Set canvas dimensions
            inputCanvas.width = inputCanvas.offsetWidth;
            inputCanvas.height = inputCanvas.offsetHeight;
            outputCanvas.width = outputCanvas.offsetWidth;
            outputCanvas.height = outputCanvas.offsetHeight;
            
            // Start visualization loop
            visualizeAudio();
        }
        
        function setupEventListeners() {
            document.getElementById('startCallBtn').onclick = startCall;
            document.getElementById('endCallBtn').onclick = endCall;
            document.getElementById('startRecordingBtn').onclick = startRecording;
            document.getElementById('stopRecordingBtn').onclick = stopRecording;
            document.getElementById('sendMarkBtn').onclick = sendMark;
            document.getElementById('clearBufferBtn').onclick = clearBuffer;
            document.getElementById('clearLogBtn').onclick = clearLog;
            
            // Socket event listeners
            socket.on('call_started', handleCallStarted);
            socket.on('call_ended', handleCallEnded);
            socket.on('audio_received', handleAudioReceived);
            socket.on('mark_received', handleMarkReceived);
            socket.on('event_log', handleEventLog);
            socket.on('call_stats', handleCallStats);
        }
        
        async function startCall() {
            const caller = document.getElementById('callerNumber').value;
            const callee = document.getElementById('calleeNumber').value;
            
            if (!caller || !callee) {
                alert('Please enter both caller and callee numbers');
                return;
            }
            
            try {
                // Request microphone access
                mediaStream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: 8000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    } 
                });
                
                const response = await fetch('/api/start_call', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ caller, callee })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    currentCallId = result.call_id;
                    callActive = true;
                    callStartTime = Date.now();
                    
                    updateCallStatus('Connected - Call ID: ' + currentCallId, 'connected');
                    updateButtons();
                    
                    // Setup audio processing
                    setupAudioProcessing();
                    
                    logEvent('Call started successfully', 'outbound');
                } else {
                    throw new Error(result.error);
                }
                
            } catch (error) {
                logEvent('Failed to start call: ' + error.message, 'error');
                alert('Failed to start call: ' + error.message);
            }
        }
        
        async function endCall() {
            if (!currentCallId) return;
            
            try {
                const response = await fetch('/api/end_call', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ call_id: currentCallId })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    callActive = false;
                    recording = false;
                    currentCallId = null;
                    chunkCounter = 0;
                    markCounter = 0;
                    
                    // Stop media stream
                    if (mediaStream) {
                        mediaStream.getTracks().forEach(track => track.stop());
                        mediaStream = null;
                    }
                    
                    updateCallStatus('Disconnected', 'disconnected');
                    updateButtons();
                    
                    logEvent('Call ended successfully', 'outbound');
                } else {
                    throw new Error(result.error);
                }
                
            } catch (error) {
                logEvent('Failed to end call: ' + error.message, 'error');
            }
        }
        
        function setupAudioProcessing() {
            if (!audioContext || !mediaStream) return;
            
            try {
                const source = audioContext.createMediaStreamSource(mediaStream);
                inputAnalyser = audioContext.createAnalyser();
                inputAnalyser.fftSize = 256;
                
                source.connect(inputAnalyser);
                
                // Create script processor for audio chunks
                audioProcessor = audioContext.createScriptProcessor(1024, 1, 1);
                source.connect(audioProcessor);
                audioProcessor.connect(audioContext.destination);
                
                audioProcessor.onaudioprocess = function(event) {
                    if (recording && callActive) {
                        const inputBuffer = event.inputBuffer;
                        const inputData = inputBuffer.getChannelData(0);
                        
                        // Convert to 16-bit PCM
                        const pcmData = float32ToPCM16(inputData);
                        
                        // Send audio chunk
                        sendAudioChunk(pcmData);
                    }
                };
                
                logEvent('Audio processing setup complete', 'system');
                
            } catch (error) {
                logEvent('Audio processing setup failed: ' + error.message, 'error');
            }
        }
        
        function startRecording() {
            if (!callActive) {
                alert('Please start a call first');
                return;
            }
            
            recording = true;
            updateButtons();
            logEvent('Recording started', 'outbound');
        }
        
        function stopRecording() {
            recording = false;
            updateButtons();
            logEvent('Recording stopped', 'outbound');
        }
        
        function sendAudioChunk(pcmData) {
            if (!currentCallId || !recording) return;
            
            chunkCounter++;
            
            socket.emit('send_audio', {
                call_id: currentCallId,
                audio_data: Array.from(new Uint8Array(pcmData.buffer)),
                chunk_number: chunkCounter
            });
            
            // Update UI
            document.getElementById('chunksSent').textContent = chunkCounter;
            
            // Update input level visualization
            const level = calculateAudioLevel(pcmData);
            document.getElementById('inputLevel').textContent = level.toFixed(1);
        }
        
        function sendMark() {
            if (!currentCallId) return;
            
            markCounter++;
            const markName = `test_mark_${markCounter}`;
            
            socket.emit('send_mark', {
                call_id: currentCallId,
                mark_name: markName
            });
            
            logEvent(`Mark sent: ${markName}`, 'outbound');
            document.getElementById('marksSent').textContent = markCounter;
        }
        
        function clearBuffer() {
            if (!currentCallId) return;
            
            socket.emit('clear_buffer', {
                call_id: currentCallId
            });
            
            logEvent('Buffer clear requested', 'outbound');
        }
        
        function handleCallStarted(data) {
            logEvent(`Call session started: ${data.stream_id}`, 'inbound');
            document.getElementById('streamId').textContent = data.stream_id;
        }
        
        function handleCallEnded(data) {
            logEvent('Call session ended: ' + data.reason, 'inbound');
        }
        
        function handleAudioReceived(data) {
            // Simulate audio output
            const receivedCount = parseInt(document.getElementById('chunksReceived').textContent) + 1;
            document.getElementById('chunksReceived').textContent = receivedCount;
            
            // Update output level (simulate)
            const outputLevel = Math.random() * 100;
            document.getElementById('outputLevel').textContent = outputLevel.toFixed(1);
            
            logEvent(`Audio chunk received: ${data.chunk_number}`, 'inbound');
        }
        
        function handleMarkReceived(data) {
            const receivedCount = parseInt(document.getElementById('marksReceived').textContent) + 1;
            document.getElementById('marksReceived').textContent = receivedCount;
            
            logEvent(`Mark received: ${data.mark_name}`, 'inbound');
        }
        
        function handleEventLog(data) {
            logEvent(data.message, data.type);
        }
        
        function handleCallStats(data) {
            if (data.sequence_number) {
                document.getElementById('sequenceNumber').textContent = data.sequence_number;
            }
            if (data.latency) {
                document.getElementById('audioLatency').textContent = data.latency + 'ms';
            }
        }
        
        function updateCallStatus(message, className) {
            const statusEl = document.getElementById('callStatus');
            statusEl.textContent = message;
            statusEl.className = 'status ' + className;
        }
        
        function updateButtons() {
            document.getElementById('startCallBtn').disabled = callActive;
            document.getElementById('endCallBtn').disabled = !callActive;
            document.getElementById('startRecordingBtn').disabled = !callActive || recording;
            document.getElementById('stopRecordingBtn').disabled = !callActive || !recording;
            document.getElementById('sendMarkBtn').disabled = !callActive;
            document.getElementById('clearBufferBtn').disabled = !callActive;
        }
        
        function logEvent(message, type = 'system') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            const logContainer = document.getElementById('eventLog');
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('eventLog').innerHTML = 
                '<div class="log-entry">Event log cleared...</div>';
        }
        
        function float32ToPCM16(float32Array) {
            const buffer = new ArrayBuffer(float32Array.length * 2);
            const view = new DataView(buffer);
            
            for (let i = 0; i < float32Array.length; i++) {
                const sample = Math.max(-1, Math.min(1, float32Array[i]));
                view.setInt16(i * 2, sample * 0x7FFF, true);
            }
            
            return buffer;
        }
        
        function calculateAudioLevel(pcmData) {
            const view = new DataView(pcmData);
            let sum = 0;
            
            for (let i = 0; i < view.byteLength; i += 2) {
                const sample = view.getInt16(i, true);
                sum += Math.abs(sample);
            }
            
            return (sum / (view.byteLength / 2)) / 327.67; // Convert to percentage
        }
        
        function visualizeAudio() {
            requestAnimationFrame(visualizeAudio);
            
            // Update call duration
            if (callActive && callStartTime) {
                const duration = Math.floor((Date.now() - callStartTime) / 1000);
                document.getElementById('callDuration').textContent = duration + 's';
            }
            
            // Visualize input audio
            if (inputAnalyser && inputCtx) {
                const bufferLength = inputAnalyser.frequencyBinCount;
                const dataArray = new Uint8Array(bufferLength);
                inputAnalyser.getByteFrequencyData(dataArray);
                
                drawAudioVisualization(inputCtx, inputCanvas, dataArray, '#007bff');
            }
            
            // Simulate output visualization
            if (outputCtx && callActive) {
                const fakeData = new Uint8Array(128);
                for (let i = 0; i < fakeData.length; i++) {
                    fakeData[i] = Math.random() * 255 * 0.3; // Simulate some output
                }
                drawAudioVisualization(outputCtx, outputCanvas, fakeData, '#28a745');
            }
        }
        
        function drawAudioVisualization(ctx, canvas, dataArray, color) {
            const width = canvas.width;
            const height = canvas.height;
            
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(0, 0, width, height);
            
            ctx.fillStyle = color;
            
            const barWidth = width / dataArray.length;
            let x = 0;
            
            for (let i = 0; i < dataArray.length; i++) {
                const barHeight = (dataArray[i] / 255) * height;
                ctx.fillRect(x, height - barHeight, barWidth, barHeight);
                x += barWidth;
            }
        }
    </script>
</body>
</html>
"""


@app.route('/')
def index():
    """Serve the main test interface."""
    return render_template_string(HTML_TEMPLATE)


@app.route('/api/start_call', methods=['POST'])
def start_call():
    """Start a new voice call with bi-directional streaming."""
    try:
        data = request.get_json()
        caller = data.get('caller')
        callee = data.get('callee')
        
        if not caller or not callee:
            return jsonify({"success": False, "error": "Missing caller or callee"})
        
        # Generate unique call ID
        call_id = f"CA{uuid.uuid4().hex[:30]}"
        
        # Create streaming session
        session = streaming.create_session(call_id, caller, callee)
        
        # Store call information
        active_calls[call_id] = {
            "caller": caller,
            "callee": callee,
            "session": session,
            "start_time": datetime.now(),
            "events": []
        }
        
        call_stats[call_id] = {
            "audio_chunks_sent": 0,
            "audio_chunks_received": 0,
            "marks_sent": 0,
            "marks_received": 0,
            "last_activity": datetime.now()
        }
        
        # Send connected and start events
        connected_event = streaming.create_connected_event()
        start_event = streaming.create_start_event(call_id)
        
        active_calls[call_id]["events"].extend([connected_event, start_event])
        
        return jsonify({
            "success": True,
            "call_id": call_id,
            "stream_id": session["stream_id"],
            "connected_event": connected_event,
            "start_event": start_event
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})


@app.route('/api/end_call', methods=['POST'])
def end_call():
    """End an active voice call."""
    try:
        data = request.get_json()
        call_id = data.get('call_id')
        
        if not call_id or call_id not in active_calls:
            return jsonify({"success": False, "error": "Call not found"})
        
        # Create stop event
        stop_event = streaming.create_stop_event(call_id, "User ended call")
        active_calls[call_id]["events"].append(stop_event)
        
        # Emit call ended event to client
        socketio.emit('call_ended', {
            "call_id": call_id,
            "reason": "User ended call",
            "stop_event": stop_event
        })
        
        # Clean up
        del active_calls[call_id]
        if call_id in call_stats:
            del call_stats[call_id]
        
        return jsonify({"success": True, "stop_event": stop_event})
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})


@app.route('/api/call_stats/<call_id>')
def get_call_stats(call_id):
    """Get statistics for a specific call."""
    if call_id not in active_calls:
        return jsonify({"error": "Call not found"}), 404
    
    call = active_calls[call_id]
    stats = call_stats.get(call_id, {})
    session_stats = streaming.get_session_stats(call_id)
    
    return jsonify({
        "call_id": call_id,
        "session": session_stats,
        "stats": stats,
        "duration": (datetime.now() - call["start_time"]).total_seconds(),
        "events_count": len(call["events"])
    })


@socketio.on('send_audio')
def handle_send_audio(data):
    """Handle audio data from client."""
    try:
        call_id = data.get('call_id')
        audio_data = data.get('audio_data')  # List of PCM samples
        chunk_number = data.get('chunk_number', 1)
        
        if call_id not in active_calls:
            emit('error', {"message": "Call not found"})
            return
        
        # Convert audio data to bytes
        audio_bytes = bytes(audio_data)
        
        # Create media event
        media_event = streaming.create_media_event(call_id, audio_bytes, chunk_number)
        active_calls[call_id]["events"].append(media_event)
        
        # Update statistics
        call_stats[call_id]["audio_chunks_sent"] += 1
        call_stats[call_id]["last_activity"] = datetime.now()
        
        # Log the event
        emit('event_log', {
            "message": f"Audio chunk processed: {chunk_number} ({len(audio_bytes)} bytes)",
            "type": "inbound"
        })
        
        # Simulate processing and response (echo back with modified audio)
        def simulate_audio_response():
            time.sleep(0.05)  # Simulate processing delay
            
            # Create response audio (simple echo simulation)
            response_audio = audio_bytes[::-1]  # Reverse the audio as a simple transformation
            
            # Process inbound audio
            streaming.process_inbound_audio(call_id, media_event["media"]["payload"])
            call_stats[call_id]["audio_chunks_received"] += 1
            
            # Send response back to client
            socketio.emit('audio_received', {
                "call_id": call_id,
                "chunk_number": chunk_number,
                "audio_data": list(response_audio[:100]),  # Send first 100 bytes
                "timestamp": chunk_number * 20
            })
        
        # Run response in background
        threading.Thread(target=simulate_audio_response).start()
        
        # Send call stats update
        emit('call_stats', {
            "sequence_number": streaming.sequence_numbers.get(call_id, 0),
            "latency": 50  # Simulated latency
        })
        
    except Exception as e:
        emit('error', {"message": f"Audio processing error: {str(e)}"})


@socketio.on('send_mark')
def handle_send_mark(data):
    """Handle mark event from client."""
    try:
        call_id = data.get('call_id')
        mark_name = data.get('mark_name')
        
        if call_id not in active_calls:
            emit('error', {"message": "Call not found"})
            return
        
        # Create mark event
        mark_event = streaming.create_mark_event(call_id, mark_name)
        active_calls[call_id]["events"].append(mark_event)
        
        # Update statistics
        call_stats[call_id]["marks_sent"] += 1
        
        # Log the event
        emit('event_log', {
            "message": f"Mark event processed: {mark_name}",
            "type": "inbound"
        })
        
        # Simulate mark acknowledgment
        def simulate_mark_response():
            time.sleep(0.1)  # Simulate processing delay
            
            call_stats[call_id]["marks_received"] += 1
            
            # Send mark completion back to client
            socketio.emit('mark_received', {
                "call_id": call_id,
                "mark_name": f"ack_{mark_name}",
                "original_mark": mark_name
            })
        
        # Run response in background
        threading.Thread(target=simulate_mark_response).start()
        
    except Exception as e:
        emit('error', {"message": f"Mark processing error: {str(e)}"})


@socketio.on('clear_buffer')
def handle_clear_buffer(data):
    """Handle buffer clear event from client."""
    try:
        call_id = data.get('call_id')
        
        if call_id not in active_calls:
            emit('error', {"message": "Call not found"})
            return
        
        # Log the event
        emit('event_log', {
            "message": "Buffer clear processed - all pending marks completed",
            "type": "inbound"
        })
        
        # Simulate clearing all pending marks
        def simulate_clear_response():
            time.sleep(0.02)  # Simulate immediate response
            
            # Send confirmation
            socketio.emit('event_log', {
                "message": "Audio buffers cleared, marks synchronized",
                "type": "system"
            })
        
        # Run response in background
        threading.Thread(target=simulate_clear_response).start()
        
    except Exception as e:
        emit('error', {"message": f"Clear buffer error: {str(e)}"})


@socketio.on('connect')
def handle_connect():
    """Handle client connection."""
    print(f"Client connected: {request.sid}")
    emit('event_log', {
        "message": "Connected to voice call test server",
        "type": "system"
    })


@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection."""
    print(f"Client disconnected: {request.sid}")


if __name__ == '__main__':
    print("🎙️ Starting Voice Call Test Application")
    print("📖 Testing Bi-Directional Audio Streaming Integration")
    print("🌐 Access the application at: http://localhost:5001")
    print("=" * 60)
    
    socketio.run(app, host='0.0.0.0', port=5001, debug=True)