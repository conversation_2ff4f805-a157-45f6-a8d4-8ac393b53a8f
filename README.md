# Smartflo AI Caller

A production-ready Python middleware service that bridges Tata Telebusiness Smartflo webhook events with the AI Voice Mate application. This service receives webhook notifications from Smartflo for call events and transforms them into WebSocket messages for the AI Voice Mate system.

## Architecture

```
┌─────────────┐    HTTP/Webhook    ┌──────────────────┐    WebSocket    ┌─────────────────┐
│   Smartflo  │ ──────────────────► │ smartflo-ai-    │ ──────────────► │  ai-voice-mate  │
│   Platform  │                    │ caller           │                 │  Application    │
└─────────────┘                    │ (This Service)   │                 └─────────────────┘
                                   └──────────────────┘
```

## Features

- **Webhook Processing**: Receives and validates Smartflo webhook events
- **Event Transformation**: Converts Smartflo events to AI Voice Mate compatible messages
- **Session Management**: Tracks call sessions with automatic cleanup
- **WebSocket Client**: Maintains persistent connection to AI Voice Mate
- **Error Handling**: Comprehensive error handling with retry logic
- **Health Monitoring**: Health checks and metrics endpoints
- **Production Ready**: Docker support, logging, configuration management
- **Security**: Request validation, rate limiting, secure headers

## Quick Start

### Prerequisites

- Python 3.8+
- Docker (optional, for containerized deployment)
- AI Voice Mate application running on `ws://localhost:5010`

### Installation

#### Option 1: Direct Installation

```bash
# Clone the repository
git clone <repository-url>
cd smartflo-ai-caller

# Install dependencies
pip install -r requirements.txt

# Install the package
pip install -e .

# Copy environment configuration
cp .env.example .env
# Edit .env with your configuration

# Run the service
python -m smartflo_ai_caller.main
```

#### Option 2: Docker Deployment

```bash
# Clone the repository
git clone <repository-url>
cd smartflo-ai-caller

# Copy environment configuration
cp .env.example .env
# Edit .env with your configuration

# Build and run with Docker Compose
docker-compose -f docker/docker-compose.yml up -d

# Check status
docker-compose -f docker/docker-compose.yml ps
```

### Configuration

Create a `.env` file in the project root:

```env
# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false
ENVIRONMENT=production

# AI Voice Mate Configuration
AI_VOICE_MATE_WS_URL=ws://localhost:5010
AI_VOICE_MATE_CONNECTION_TIMEOUT=30
AI_VOICE_MATE_RETRY_ATTEMPTS=3
AI_VOICE_MATE_RETRY_DELAY=5

# Webhook Configuration
WEBHOOK_PATH=/webhook/smartflo
WEBHOOK_SECRET=your_webhook_secret_here

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=logs/smartflo-ai-caller.log

# Session Management
SESSION_TIMEOUT=300
MAX_CONCURRENT_SESSIONS=100
```

## API Endpoints

### Webhook Endpoint

**POST** `/webhook/smartflo`

Receives webhook events from Smartflo platform.

**Headers:**
- `Content-Type: application/json`
- `x-webhook-secret: <your-secret>` (optional, if configured)

**Request Body:**
```json
{
  "call_id": "unique_call_id",
  "call_type": "inbound",
  "caller_number": "+**********",
  "called_number": "+**********",
  "event_type": "call_received",
  "event_timestamp": "2024-01-15T10:30:00Z",
  "call_status": "ringing",
  "call_duration": null,
  "agent_id": null,
  "dtmf_digits": null,
  "recording_url": null,
  "billing_info": {},
  "custom_data": {}
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Webhook processed successfully"
}
```

### Health Check

**GET** `/health`

Returns service health status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": **********,
  "version": "1.0.0",
  "ai_voice_mate": "connected",
  "sessions": {
    "total_sessions": 10,
    "active_sessions": 3,
    "ended_sessions": 7,
    "error_sessions": 0
  }
}
```

### Metrics

**GET** `/metrics`

Returns service metrics (internal access only).

### Session Management

**GET** `/sessions`

List all sessions with optional filtering.

**Query Parameters:**
- `status`: Filter by session status (optional)
- `include_expired`: Include expired sessions (default: false)

**GET** `/sessions/{session_id}`

Get details for a specific session.

**DELETE** `/sessions/{session_id}`

Manually end a session.

## Supported Webhook Events

| Event Type | Description | AI Action |
|------------|-------------|-----------|
| `call_received` | Incoming call received | Register user, prepare AI |
| `call_answered_ivr` | Call answered by IVR | Start AI interaction |
| `call_answered_agent` | Call answered by agent | Update AI context |
| `call_hangup` | Call ended | End AI session |
| `dtmf_received` | DTMF digits pressed | Send input to AI |
| `agent_status_change` | Agent status updated | Update AI accordingly |
| `call_disposition` | Call completed | Cleanup session |

## Event Flow

1. **Call Received**: Smartflo sends webhook → Service creates session → Registers user with AI Voice Mate
2. **Call Answered**: Smartflo sends webhook → Service starts AI interaction → AI begins conversation
3. **DTMF Input**: Smartflo sends webhook → Service forwards DTMF as text to AI
4. **Call Ended**: Smartflo sends webhook → Service ends AI session → Cleans up resources

## Session Lifecycle

```
┌─────────────┐    call_received    ┌─────────────┐    call_answered    ┌─────────────┐
│ initialized │ ──────────────────► │   active    │ ──────────────────► │   active    │
└─────────────┘                    └─────────────┘                    └─────────────┘
                                          │                                   │
                                          │ call_hangup / timeout             │ call_hangup
                                          ▼                                   ▼
                                   ┌─────────────┐                    ┌─────────────┐
                                   │    ended    │                    │    ended    │
                                   └─────────────┘                    └─────────────┘
```

## Error Handling

The service implements comprehensive error handling:

- **Webhook Validation Errors**: Returns 400 Bad Request
- **Session Not Found**: Returns 404 Not Found  
- **Session Expired**: Returns 410 Gone
- **Max Sessions Exceeded**: Returns 429 Too Many Requests
- **AI Connection Errors**: Returns 502 Bad Gateway
- **Internal Errors**: Returns 500 Internal Server Error

All errors include detailed error codes and messages for debugging.

## Logging

The service uses structured JSON logging with correlation IDs for request tracing.

**Log Levels:**
- `DEBUG`: Detailed debugging information
- `INFO`: General information about service operation
- `WARNING`: Warning conditions
- `ERROR`: Error conditions
- `CRITICAL`: Critical errors

**Log Format:**
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "INFO",
  "logger": "smartflo_ai_caller.main",
  "message": "Webhook processed successfully",
  "correlation_id": "uuid-here",
  "session_id": "call_123",
  "service": "smartflo-ai-caller",
  "version": "1.0.0"
}
```

## Monitoring

### Health Checks

The service provides health check endpoints for monitoring:

- **HTTP Health Check**: `GET /health`
- **Container Health Check**: Built into Docker image
- **Kubernetes Readiness**: Use `/health` endpoint

### Metrics

Key metrics available at `/metrics`:

- Active sessions count
- AI connection status
- Request processing times
- Error rates
- Session creation/cleanup rates

## Deployment

### Docker

```bash
# Build image
docker build -f docker/Dockerfile -t smartflo-ai-caller:latest .

# Run container
docker run -d \
  --name smartflo-ai-caller \
  -p 8000:8000 \
  --env-file .env \
  smartflo-ai-caller:latest
```

### Docker Compose

```bash
# Start all services
docker-compose -f docker/docker-compose.yml up -d

# Start with nginx proxy
docker-compose -f docker/docker-compose.yml --profile nginx up -d

# View logs
docker-compose -f docker/docker-compose.yml logs -f smartflo-ai-caller
```

### Kubernetes

Example Kubernetes deployment:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: smartflo-ai-caller
spec:
  replicas: 3
  selector:
    matchLabels:
      app: smartflo-ai-caller
  template:
    metadata:
      labels:
        app: smartflo-ai-caller
    spec:
      containers:
      - name: smartflo-ai-caller
        image: smartflo-ai-caller:latest
        ports:
        - containerPort: 8000
        env:
        - name: AI_VOICE_MATE_WS_URL
          value: "ws://ai-voice-mate:5010"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## Security

### Webhook Security

- **Secret Validation**: Optional webhook secret validation
- **Request Validation**: Comprehensive payload validation
- **Rate Limiting**: Configurable rate limits via nginx
- **HTTPS**: TLS encryption for webhook endpoints

### Network Security

- **Container Isolation**: Runs as non-root user in container
- **Network Policies**: Kubernetes network policies supported
- **Firewall Rules**: Restrict access to internal endpoints

## Configuration Reference

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `HOST` | `0.0.0.0` | Server bind address |
| `PORT` | `8000` | Server port |
| `DEBUG` | `false` | Enable debug mode |
| `ENVIRONMENT` | `production` | Environment name |
| `AI_VOICE_MATE_WS_URL` | `ws://localhost:5010` | AI Voice Mate WebSocket URL |
| `AI_VOICE_MATE_CONNECTION_TIMEOUT` | `30` | Connection timeout in seconds |
| `AI_VOICE_MATE_RETRY_ATTEMPTS` | `3` | Number of retry attempts |
| `AI_VOICE_MATE_RETRY_DELAY` | `5` | Delay between retries in seconds |
| `WEBHOOK_PATH` | `/webhook/smartflo` | Webhook endpoint path |
| `WEBHOOK_SECRET` | `null` | Webhook secret for validation |
| `LOG_LEVEL` | `INFO` | Logging level |
| `LOG_FORMAT` | `json` | Log format (json/text) |
| `LOG_FILE` | `null` | Log file path |
| `SESSION_TIMEOUT` | `300` | Session timeout in seconds |
| `MAX_CONCURRENT_SESSIONS` | `100` | Maximum concurrent sessions |

### Configuration Files

- `config/development.yaml`: Development configuration
- `config/production.yaml`: Production configuration  
- `config/logging.yaml`: Logging configuration

## Troubleshooting

### Common Issues

**1. AI Voice Mate Connection Failed**
```
Error: Failed to connect to AI Voice Mate: Connection refused
```
- Verify AI Voice Mate is running on the configured URL
- Check network connectivity between services
- Verify WebSocket URL format (ws:// or wss://)

**2. Webhook Validation Failed**
```
Error: Webhook payload validation failed
```
- Check webhook payload format matches expected schema
- Verify Content-Type header is application/json
- Check webhook secret if configured

**3. Session Not Found**
```
Error: Session not found: call_123
```
- Session may have expired (check SESSION_TIMEOUT)
- Verify session was created during call_received event
- Check for session cleanup in logs

**4. Max Sessions Exceeded**
```
Error: Maximum sessions exceeded: 100/100
```
- Increase MAX_CONCURRENT_SESSIONS configuration
- Check for session leaks (sessions not being cleaned up)
- Monitor session creation/cleanup rates

### Debug Mode

Enable debug mode for detailed logging:

```env
DEBUG=true
LOG_LEVEL=DEBUG
```

### Health Check

Check service health:

```bash
curl http://localhost:8000/health
```

### View Logs

```bash
# Docker
docker logs smartflo-ai-caller

# Docker Compose
docker-compose logs -f smartflo-ai-caller

# Local file
tail -f logs/smartflo-ai-caller.log
```

## Development

### Setup Development Environment

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # or `venv\Scripts\activate` on Windows

# Install development dependencies
pip install -r requirements.txt
pip install -e ".[dev]"

# Run tests
pytest

# Code formatting
black src/
isort src/

# Type checking
mypy src/
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=smartflo_ai_caller

# Run specific test file
pytest tests/test_webhook_handler.py
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:

- Create an issue in the repository
- Check the troubleshooting section
- Review the logs for error details

## Changelog

### v1.0.0 (2024-01-15)
- Initial release
- Webhook processing for Smartflo events
- WebSocket client for AI Voice Mate
- Session management with automatic cleanup
- Comprehensive error handling and logging
- Docker deployment support
- Health checks and metrics
- Production-ready configuration