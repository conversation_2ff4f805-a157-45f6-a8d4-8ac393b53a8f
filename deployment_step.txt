## 1. System & Dependencies Setup
sudo apt-get update
sudo apt upgrade -y
sudo apt install python3-pip python3-dev
sudo apt install python3-virtualenv
sudo apt-get install -y portaudio19-dev
sudo apt-get install -y unixodbc-dev
# sudo pip3 install virtualenv

## 2. Install ngrok
# ngrok is used to expose your local server to the internet for webhook testing.
#
# Option A: Using Snap (Recommended for Ubuntu)
# sudo snap install ngrok
#
# Option B: Using APT
curl -s https://ngrok-agent.s3.amazonaws.com/ngrok.asc | sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null && \
echo "deb https://ngrok-agent.s3.amazonaws.com buster main" | sudo tee /etc/apt/sources.list.d/ngrok.list && \
sudo apt update && \
sudo apt install -y ngrok

## 3. Application Setup
mkdir ~/smartflo-ai-caller
cd ~/smartflo-ai-caller
# virtualenv .venv
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
               
## 4. Run the Application
python3 -m smartflo_ai_caller.main

## 5. Expose with ngrok
# First, configure ngrok with your authtoken from https://dashboard.ngrok.com/get-started/your-authtoken
# ngrok config add-authtoken <YOUR_TOKEN_HERE>
ngrok config add-authtoken 2zSORtoU0oWLbCtNZVcV7jTZ29z_46g2X8ATAzmBRp1me42gR
ngrok http 8000