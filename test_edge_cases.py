#!/usr/bin/env python3
"""Edge case tests for Smartflo AI Caller."""

import asyncio
import json
import httpx


async def test_edge_cases():
    """Test edge cases and error scenarios."""
    
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Edge Cases and <PERSON><PERSON><PERSON>\n")
    
    async with httpx.AsyncClient() as client:
        
        # Test 1: Large payload
        print("1. Testing large payload...")
        large_payload = {
            "call_id": "large_test_001",
            "call_type": "inbound",
            "caller_number": "+1234567890",
            "called_number": "+0987654321",
            "event_type": "call_received",
            "event_timestamp": "2024-01-15T10:30:00Z",
            "call_status": "ringing",
            "custom_data": {"large_field": "x" * 10000}  # 10KB string
        }
        
        try:
            response = await client.post(
                f"{base_url}/webhook/smartflo",
                json=large_payload,
                headers={"Content-Type": "application/json"}
            )
            print(f"   Large payload: {response.status_code} - {'✅ PASS' if response.status_code == 200 else '❌ FAIL'}")
        except Exception as e:
            print(f"   Large payload: ❌ FAIL - {e}")
        
        # Test 2: Invalid event type
        print("2. Testing unknown event type...")
        unknown_event = {
            "call_id": "unknown_test_001",
            "call_type": "inbound",
            "caller_number": "+1234567890",
            "called_number": "+0987654321",
            "event_type": "unknown_event_type",
            "event_timestamp": "2024-01-15T10:30:00Z",
            "call_status": "ringing"
        }
        
        try:
            response = await client.post(
                f"{base_url}/webhook/smartflo",
                json=unknown_event,
                headers={"Content-Type": "application/json"}
            )
            print(f"   Unknown event: {response.status_code} - {'✅ PASS' if response.status_code == 200 else '❌ FAIL'}")
        except Exception as e:
            print(f"   Unknown event: ❌ FAIL - {e}")
        
        # Test 3: Special characters in phone numbers
        print("3. Testing special characters...")
        special_chars = {
            "call_id": "special_test_001",
            "call_type": "inbound",
            "caller_number": "******-567-890 ext.123",
            "called_number": "+************",
            "event_type": "call_received",
            "event_timestamp": "2024-01-15T10:30:00Z",
            "call_status": "ringing"
        }
        
        try:
            response = await client.post(
                f"{base_url}/webhook/smartflo",
                json=special_chars,
                headers={"Content-Type": "application/json"}
            )
            print(f"   Special chars: {response.status_code} - {'✅ PASS' if response.status_code == 200 else '❌ FAIL'}")
        except Exception as e:
            print(f"   Special chars: ❌ FAIL - {e}")
        
        # Test 4: Test session retrieval after creation
        print("4. Testing session retrieval...")
        try:
            response = await client.get(f"{base_url}/sessions/large_test_001")
            print(f"   Session retrieval: {response.status_code} - {'✅ PASS' if response.status_code == 200 else '❌ FAIL'}")
            if response.status_code == 200:
                session_data = response.json()
                print(f"      Session status: {session_data.get('status')}")
                print(f"      User registered: {session_data.get('user_registered')}")
        except Exception as e:
            print(f"   Session retrieval: ❌ FAIL - {e}")
        
        # Test 5: Test complete call flow
        print("5. Testing complete call flow...")
        call_id = "complete_flow_001"
        
        # Step 1: Call received
        step1 = {
            "call_id": call_id,
            "call_type": "inbound",
            "caller_number": "+5555555555",
            "called_number": "+4444444444",
            "event_type": "call_received",
            "event_timestamp": "2024-01-15T10:30:00Z",
            "call_status": "ringing"
        }
        
        response1 = await client.post(f"{base_url}/webhook/smartflo", json=step1, headers={"Content-Type": "application/json"})
        print(f"   Call received: {response1.status_code} - {'✅' if response1.status_code == 200 else '❌'}")
        
        # Step 2: Call answered
        step2 = {
            "call_id": call_id,
            "call_type": "inbound",
            "caller_number": "+5555555555",
            "called_number": "+4444444444",
            "event_type": "call_answered_ivr",
            "event_timestamp": "2024-01-15T10:30:05Z",
            "call_status": "answered"
        }
        
        response2 = await client.post(f"{base_url}/webhook/smartflo", json=step2, headers={"Content-Type": "application/json"})
        print(f"   Call answered: {response2.status_code} - {'✅' if response2.status_code == 200 else '❌'}")
        
        # Step 3: DTMF received
        step3 = {
            "call_id": call_id,
            "call_type": "inbound",
            "caller_number": "+5555555555",
            "called_number": "+4444444444",
            "event_type": "dtmf_received",
            "event_timestamp": "2024-01-15T10:30:10Z",
            "call_status": "answered",
            "dtmf_digits": "1234*#"
        }
        
        response3 = await client.post(f"{base_url}/webhook/smartflo", json=step3, headers={"Content-Type": "application/json"})
        print(f"   DTMF received: {response3.status_code} - {'✅' if response3.status_code == 200 else '❌'}")
        
        # Step 4: Call hangup
        step4 = {
            "call_id": call_id,
            "call_type": "inbound",
            "caller_number": "+5555555555",
            "called_number": "+4444444444",
            "event_type": "call_hangup",
            "event_timestamp": "2024-01-15T10:32:00Z",
            "call_status": "ended",
            "call_duration": 120
        }
        
        response4 = await client.post(f"{base_url}/webhook/smartflo", json=step4, headers={"Content-Type": "application/json"})
        print(f"   Call hangup: {response4.status_code} - {'✅' if response4.status_code == 200 else '❌'}")
        
        # Check final session state
        session_response = await client.get(f"{base_url}/sessions/{call_id}")
        if session_response.status_code == 200:
            session_data = session_response.json()
            print(f"   Final session status: {session_data.get('status')} - {'✅' if session_data.get('status') == 'ended' else '❌'}")
        
        # Test 6: Rate limiting simulation
        print("6. Testing rapid requests...")
        rapid_tasks = []
        for i in range(10):
            payload = {
                "call_id": f"rapid_test_{i:03d}",
                "call_type": "inbound",
                "caller_number": f"+123456789{i}",
                "called_number": "+0987654321",
                "event_type": "call_received",
                "event_timestamp": "2024-01-15T10:30:00Z",
                "call_status": "ringing"
            }
            
            task = client.post(f"{base_url}/webhook/smartflo", json=payload, headers={"Content-Type": "application/json"})
            rapid_tasks.append(task)
        
        rapid_responses = await asyncio.gather(*rapid_tasks, return_exceptions=True)
        successful_rapid = sum(1 for r in rapid_responses if not isinstance(r, Exception) and r.status_code == 200)
        print(f"   Rapid requests: {successful_rapid}/10 successful - {'✅ PASS' if successful_rapid >= 8 else '❌ FAIL'}")
        
        # Test 7: Service health during load
        print("7. Testing service health during load...")
        health_response = await client.get(f"{base_url}/health")
        print(f"   Health check: {health_response.status_code} - {'✅ PASS' if health_response.status_code == 200 else '❌ FAIL'}")
        
        if health_response.status_code == 200:
            health_data = health_response.json()
            print(f"      Status: {health_data.get('status')}")
            print(f"      AI Connection: {health_data.get('ai_voice_mate')}")
            print(f"      Active Sessions: {health_data.get('sessions', {}).get('active_sessions', 0)}")
        
        print("\n🎯 Edge case testing completed!")


if __name__ == "__main__":
    asyncio.run(test_edge_cases())