# Voice Call Testing Guide

## Overview

This guide provides comprehensive instructions for testing bi-directional voice call functionality using the enhanced Flask web application and integration tools. The system tests the complete integration between Tata Telebusiness Smartflo, the smartflo-ai-caller middleware, and the AI Voice Mate agent.

## System Architecture

```
Web Browser (Test UI)
    ↕ (WebSocket/HTTP)
Enhanced Flask App (Port 5002)
    ↕ (WebSocket Client)
smartflo-ai-caller (Port 8000)
    ↕ (Audio forwarding)
AI Voice Mate Agent (Port 5010)
```

## Prerequisites

### System Requirements

1. **Python 3.8+** with asyncio support
2. **Required Services Running:**
   - smartflo-ai-caller server on `localhost:8000`
   - AI Voice Mate agent on `localhost:5010`
3. **Audio Hardware:**
   - Microphone for input testing
   - Speakers/headphones for output testing
4. **Network:**
   - Local network access for WebSocket connections

### Installation

1. **Install Dependencies:**
   ```bash
   # Core application dependencies
   pip install -r requirements_enhanced_test_app.txt
   
   # Optional: For advanced audio processing
   pip install numpy scipy
   ```

2. **Verify System Status:**
   ```bash
   # Check if smartflo-ai-caller is running
   curl http://localhost:8000/health
   
   # Check if AI Voice Mate is accessible
   # (Note: AI Voice Mate may not have a health endpoint)
   ```

## Testing Applications

### 1. Enhanced Flask Web Application

**Purpose:** Interactive web-based testing with real system integration

**Usage:**
```bash
python enhanced_voice_call_test_app.py
```

**Access:** http://localhost:5002

**Features:**
- Real-time audio capture and playback
- WebSocket integration with smartflo-ai-caller
- AI Voice Mate connection and interaction
- Audio visualization and metrics
- Event logging and debugging
- PDF specification compliance verification

### 2. Direct Integration Client

**Purpose:** Programmatic testing using actual system components

**Usage:**
```bash
python integration_client.py
```

**Features:**
- Direct integration with AudioStreamManager
- BiDirectionalWSHandler testing
- AI Voice Mate forwarding
- Automated test scenarios
- Comprehensive logging

### 3. Audio Compliance Tester

**Purpose:** Validate compliance with PDF specifications

**Usage:**
```bash
python audio_compliance_tester.py
```

**Features:**
- Audio format validation (8kHz, μ-law, 8-bit)
- Message format compliance testing
- Sequence number validation
- Timing compliance verification
- Base64 encoding/decoding tests
- DTMF and mark synchronization tests

## Testing Scenarios

### Scenario 1: Basic Call Flow Test

**Objective:** Verify basic bi-directional audio streaming

**Steps:**
1. Start Enhanced Flask App
2. Open web interface at http://localhost:5002
3. Check system status indicators (should show connected)
4. Enter caller and callee numbers
5. Click "Start Call"
6. Wait for connection establishment
7. Click "Start Recording" to begin audio capture
8. Speak into microphone and observe:
   - Audio level indicators
   - Chunk counter increments
   - Event log entries
9. Send test marks using "Send Mark" button
10. Test buffer clearing with "Clear Buffer"
11. End call with "End Call" button

**Expected Results:**
- All system status indicators show "Connected"
- Audio chunks are sent and received
- Marks are synchronized properly
- Call statistics are accurate
- No errors in event log

### Scenario 2: PDF Specification Compliance

**Objective:** Verify full compliance with PDF document requirements

**Steps:**
1. Run audio compliance tester:
   ```bash
   python audio_compliance_tester.py
   ```
2. Review results for:
   - Audio format compliance (8kHz, μ-law, 8-bit)
   - Message format validation
   - Sequence number management
   - Timing compliance (20ms chunks)
   - Base64 encoding/decoding
   - DTMF event handling
   - Mark synchronization

**Expected Results:**
- All compliance tests pass
- Audio format matches specifications exactly
- Message formats comply with PDF requirements
- Timing is accurate (20ms chunks = 160 bytes)

### Scenario 3: AI Integration Test

**Objective:** Verify AI Voice Mate integration and response handling

**Steps:**
1. Ensure AI Voice Mate is running on port 5010
2. Start Enhanced Flask App
3. Begin call session
4. Send test message to AI using "Test AI Response"
5. Record audio and observe:
   - Speech transcription in UI
   - AI responses in response panel
   - Audio response playback (if implemented)
6. Monitor event log for AI-related events

**Expected Results:**
- AI connection status shows "Connected"
- Test messages generate AI responses
- Speech is transcribed correctly
- AI responses appear in the interface
- No AI-related errors in logs

### Scenario 4: Load and Stress Testing

**Objective:** Test system performance under load

**Steps:**
1. Use Direct Integration Client for automated testing:
   ```bash
   python integration_client.py
   ```
2. Run multiple concurrent sessions
3. Send high-frequency audio chunks
4. Monitor system resources and performance
5. Check for memory leaks or connection issues

**Expected Results:**
- System maintains stable performance
- No memory leaks or resource exhaustion
- All sessions complete successfully
- Response times remain acceptable

## Troubleshooting

### Common Issues

#### 1. Connection Failures

**Symptoms:**
- System status shows "Disconnected"
- WebSocket connection errors
- "Failed to start call" messages

**Solutions:**
- Verify smartflo-ai-caller is running: `curl http://localhost:8000/health`
- Check AI Voice Mate accessibility
- Ensure ports 8000 and 5010 are not blocked
- Review firewall settings
- Check application logs for detailed error messages

#### 2. Audio Issues

**Symptoms:**
- No audio input detected
- Audio chunks not being sent
- Poor audio quality

**Solutions:**
- Verify microphone permissions in browser
- Check audio device settings
- Test microphone with other applications
- Ensure proper audio format (8kHz, mono)
- Review audio compliance test results

#### 3. AI Integration Problems

**Symptoms:**
- AI responses not received
- Transcription not working
- AI connection timeouts

**Solutions:**
- Verify AI Voice Mate is running and accessible
- Check AI service logs for errors
- Ensure proper session registration
- Test AI connection independently
- Review AI client authentication

#### 4. Performance Issues

**Symptoms:**
- Slow response times
- High CPU/memory usage
- Connection timeouts

**Solutions:**
- Monitor system resources
- Check for memory leaks
- Optimize audio chunk size and timing
- Review concurrent connection limits
- Scale system components if needed

### Debug Mode

Enable debug logging for detailed troubleshooting:

```bash
# Set environment variable
export FLASK_DEBUG=1

# Or modify logging level in code
logging.basicConfig(level=logging.DEBUG)
```

### Log Analysis

Important log locations:
- Enhanced Flask App: Console output and browser developer tools
- smartflo-ai-caller: `logs/smartflo-ai-caller.log`
- Integration Client: Console output with detailed async operations

## Performance Benchmarks

### Expected Performance Metrics

- **Audio Latency:** < 100ms end-to-end
- **Chunk Processing:** 20ms intervals (50 chunks/second)
- **Memory Usage:** < 100MB per active session
- **CPU Usage:** < 10% per active session
- **Connection Setup:** < 2 seconds
- **Mark Synchronization:** < 50ms

### Monitoring

Monitor the following metrics during testing:
- Audio chunk send/receive rates
- WebSocket connection stability
- Memory and CPU utilization
- Network bandwidth usage
- Error rates and types

## Production Readiness Checklist

Before deploying to production with Tata Telebusiness Smartflo:

- [ ] All compliance tests pass (100% success rate)
- [ ] AI integration works correctly
- [ ] Audio quality is acceptable
- [ ] Performance meets requirements
- [ ] Error handling is robust
- [ ] Logging is comprehensive
- [ ] Security measures are in place
- [ ] Documentation is complete
- [ ] Monitoring is implemented
- [ ] Backup and recovery procedures are defined

## Advanced Configuration

### Custom Audio Settings

Modify audio parameters in the Flask application:

```javascript
// In the HTML template, modify getUserMedia constraints
mediaStream = await navigator.mediaDevices.getUserMedia({ 
    audio: {
        sampleRate: 8000,        // Keep at 8kHz for compliance
        channelCount: 1,         // Mono audio required
        echoCancellation: true,  // Adjust as needed
        noiseSuppression: true,  // Adjust as needed
        autoGainControl: false   // May improve quality
    } 
});
```

### WebSocket Configuration

Adjust WebSocket settings for different environments:

```python
# In enhanced_voice_call_test_app.py
SMARTFLO_AI_CALLER_WS_URL = "ws://your-server:8000/vendor-audio-stream"
AI_VOICE_MATE_WS_URL = "ws://your-ai-server:5010"
```

### Testing Configuration

Customize test parameters:

```python
# In integration_client.py
custom_parameters = {
    "test_scenario": "production_simulation",
    "audio_quality": "high",
    "chunk_size": 160,
    "sample_rate": 8000
}
```

## API Reference

### Enhanced Flask App Endpoints

- `GET /` - Main testing interface
- `GET /api/system_status` - Check system component status
- `POST /api/start_enhanced_call` - Start voice call session
- `POST /api/end_enhanced_call` - End voice call session
- `GET /api/call_stats/<call_id>` - Get call statistics

### WebSocket Events

**Client to Server:**
- `send_enhanced_audio` - Send audio chunk
- `send_enhanced_mark` - Send mark event
- `clear_enhanced_buffer` - Clear audio buffer
- `test_ai_response` - Test AI interaction

**Server to Client:**
- `call_started` - Call session started
- `call_ended` - Call session ended
- `audio_received` - Audio chunk received
- `mark_received` - Mark event received
- `ai_response` - AI text response
- `speech_transcribed` - Speech to text result

## Security Considerations

- Use secure WebSocket connections (WSS) in production
- Implement proper authentication and authorization
- Validate all input data and audio streams
- Protect against audio injection attacks
- Monitor for unusual usage patterns
- Secure sensitive call metadata
- Implement rate limiting for API endpoints

## Support and Maintenance

### Regular Maintenance Tasks

1. **Daily:**
   - Monitor system health and performance
   - Review error logs for issues
   - Check audio quality metrics

2. **Weekly:**
   - Run compliance tests to ensure continued conformance
   - Update system components and dependencies
   - Review and analyze usage statistics

3. **Monthly:**
   - Perform comprehensive integration testing
   - Update documentation and procedures
   - Review and update security measures

### Getting Help

For technical support or questions:

1. Check this documentation first
2. Review application logs for specific error messages
3. Run diagnostic tests using the provided tools
4. Consult the Bi-Directional Audio Streaming Integration Document.pdf
5. Contact the development team with specific details and log files

---

## Appendix

### File Structure

```
smartflo-ai-caller/
├── enhanced_voice_call_test_app.py      # Main Flask testing application
├── integration_client.py                # Direct integration testing client
├── audio_compliance_tester.py           # PDF compliance validation
├── requirements_enhanced_test_app.txt   # Dependencies for testing apps
├── VOICE_CALL_TESTING_GUIDE.md         # This documentation
├── voice_call_test_app.py               # Original basic test app
├── src/smartflo_ai_caller/              # Core system components
│   ├── audio_streaming.py               # Audio stream management
│   ├── bidirectional_ws_handler.py      # WebSocket handler
│   ├── ai_voice_client.py               # AI Voice Mate client
│   └── ...                              # Other core modules
└── tests/                               # Test files and samples
    └── audio/                           # Audio test samples
```

### Version History

- **v1.0** - Initial implementation with basic WebSocket support
- **v2.0** - Enhanced Flask application with real system integration
- **v2.1** - Added direct integration client and compliance testing
- **v2.2** - Comprehensive documentation and troubleshooting guide

---

*This guide is maintained as part of the smartflo-ai-caller project. Please keep it updated as the system evolves.*