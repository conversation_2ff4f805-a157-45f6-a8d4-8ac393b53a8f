#!/usr/bin/env python3
"""
Enhanced Flask web application for testing bi-directional voice call functionality.
This application integrates with the actual smartflo-ai-caller system and AI Voice Mate agent
to test the complete voice call workflow described in the "Bi-Directional Audio Streaming Integration Document.pdf".
"""

import asyncio
import base64
import json
import uuid
import wave
import io
import struct
import audioop
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
import threading
import time
import websocket
import requests

from flask import Flask, render_template_string, request, jsonify, session
from flask_socketio import SocketIO, emit, join_room, leave_room
import websockets

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'enhanced_voice_call_test_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# Configuration for real system integration
SMARTFLO_AI_CALLER_WS_URL = "ws://localhost:8000/audio-stream"
AI_VOICE_MATE_WS_URL = "ws://localhost:5010"
SMARTFLO_AI_CALLER_HTTP_URL = "http://localhost:8000"

# Global state for managing calls
active_calls: Dict[str, Dict[str, Any]] = {}
call_stats: Dict[str, Dict[str, Any]] = {}
websocket_connections: Dict[str, websocket.WebSocket] = {}


class RealAudioProcessor:
    """Real audio processor with proper μ-law encoding/decoding."""
    
    @staticmethod
    def pcm_to_mulaw(pcm_data: bytes, sample_width: int = 2) -> bytes:
        """Convert PCM to μ-law using audioop (proper G.711 implementation)."""
        try:
            return audioop.lin2ulaw(pcm_data, sample_width)
        except Exception as e:
            logger.error(f"PCM to μ-law conversion error: {e}")
            # Fallback to simple conversion
            return RealAudioProcessor._simple_pcm_to_mulaw(pcm_data)
    
    @staticmethod
    def mulaw_to_pcm(mulaw_data: bytes, sample_width: int = 2) -> bytes:
        """Convert μ-law to PCM using audioop (proper G.711 implementation)."""
        try:
            return audioop.ulaw2lin(mulaw_data, sample_width)
        except Exception as e:
            logger.error(f"μ-law to PCM conversion error: {e}")
            # Fallback to simple conversion
            return RealAudioProcessor._simple_mulaw_to_pcm(mulaw_data)
    
    @staticmethod
    def _simple_pcm_to_mulaw(pcm_data: bytes) -> bytes:
        """Fallback simple PCM to μ-law conversion."""
        if len(pcm_data) % 2 != 0:
            pcm_data += b'\x00'
        
        samples = struct.unpack(f'<{len(pcm_data)//2}h', pcm_data)
        mulaw_samples = []
        
        for sample in samples:
            sample = max(-32768, min(32767, sample))
            # Simple quantization to 8-bit μ-law
            mulaw_byte = abs(sample) >> 8
            mulaw_byte = min(255, mulaw_byte)
            if sample < 0:
                mulaw_byte |= 0x80
            mulaw_samples.append(mulaw_byte)
        
        return bytes(mulaw_samples)
    
    @staticmethod
    def _simple_mulaw_to_pcm(mulaw_data: bytes) -> bytes:
        """Fallback simple μ-law to PCM conversion."""
        pcm_samples = []
        for byte in mulaw_data:
            sign = -1 if byte & 0x80 else 1
            magnitude = (byte & 0x7F) << 8
            sample = sign * magnitude
            pcm_samples.append(sample)
        
        return struct.pack(f'<{len(pcm_samples)}h', *pcm_samples)
    
    @staticmethod
    def resample_to_8khz(pcm_data: bytes, original_rate: int, target_rate: int = 8000) -> bytes:
        """Resample audio to 8kHz using audioop."""
        try:
            if original_rate == target_rate:
                return pcm_data
            
            # Use audioop for resampling
            resampled_data, _ = audioop.ratecv(pcm_data, 2, 1, original_rate, target_rate, None)
            return resampled_data
        except Exception as e:
            logger.error(f"Resampling error: {e}")
            return pcm_data  # Return original if resampling fails
    
    @staticmethod
    def create_audio_chunks(audio_data: bytes, chunk_size: int = 160) -> List[bytes]:
        """Create 20ms audio chunks (160 bytes at 8kHz, μ-law)."""
        chunks = []
        for i in range(0, len(audio_data), chunk_size):
            chunk = audio_data[i:i + chunk_size]
            if len(chunk) < chunk_size:
                # Pad the last chunk with silence
                chunk += b'\x7f' * (chunk_size - len(chunk))  # μ-law silence
            chunks.append(chunk)
        return chunks


class SmartfloAICallerClient:
    """Client for connecting to the actual smartflo-ai-caller system."""
    
    def __init__(self, call_id: str):
        self.call_id = call_id
        self.ws_url = SMARTFLO_AI_CALLER_WS_URL
        self.websocket = None
        self.connected = False
        self.sequence_number = 1
        self.chunk_number = 1
        self.stream_id = None
        self.session_data = {}
        
    async def connect(self):
        """Connect to smartflo-ai-caller WebSocket."""
        try:
            self.websocket = await websockets.connect(self.ws_url)
            self.connected = True
            logger.info(f"Connected to smartflo-ai-caller: {self.ws_url}")
            
            # Start listening for messages
            asyncio.create_task(self._listen_for_messages())
            
        except Exception as e:
            logger.error(f"Failed to connect to smartflo-ai-caller: {e}")
            raise
    
    async def _listen_for_messages(self):
        """Listen for messages from smartflo-ai-caller."""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                await self._handle_message(data)
                
        except websockets.exceptions.ConnectionClosed:
            logger.info("Connection to smartflo-ai-caller closed")
            self.connected = False
        except Exception as e:
            logger.error(f"Error listening for messages: {e}")
            self.connected = False
    
    async def _handle_message(self, message: Dict[str, Any]):
        """Handle incoming message from smartflo-ai-caller."""
        event_type = message.get("event")
        
        logger.debug(f"Received from smartflo-ai-caller: {event_type}")
        
        if event_type == "connected":
            await self._handle_connected(message)
        elif event_type == "start":
            await self._handle_start(message)
        elif event_type == "media":
            await self._handle_media(message)
        elif event_type == "stop":
            await self._handle_stop(message)
        elif event_type == "dtmf":
            await self._handle_dtmf(message)
        elif event_type == "mark":
            await self._handle_mark(message)
        elif event_type == "clear":
            await self._handle_clear(message)
    
    async def _handle_connected(self, message: Dict[str, Any]):
        """Handle connected event."""
        logger.info("Received connected event from smartflo-ai-caller")
        
        # Send start event
        await self.send_start_event()
    
    async def _handle_start(self, message: Dict[str, Any]):
        """Handle start event."""
        start_data = message.get("start", {})
        self.stream_id = start_data.get("streamSid")
        
        logger.info(f"Stream started with ID: {self.stream_id}")
        
        # Notify Flask app
        socketio.emit('call_started', {
            "call_id": self.call_id,
            "stream_id": self.stream_id,
            "message": message
        })
    
    async def _handle_media(self, message: Dict[str, Any]):
        """Handle media event (audio from AI)."""
        media_data = message.get("media", {})
        payload_b64 = media_data.get("payload", "")
        chunk_number = media_data.get("chunk", 1)
        timestamp = media_data.get("timestamp", 0)
        
        # Update stats
        if self.call_id in call_stats:
            call_stats[self.call_id]["audio_chunks_received"] += 1
        
        # Notify Flask app
        socketio.emit('audio_received', {
            "call_id": self.call_id,
            "chunk_number": chunk_number,
            "timestamp": timestamp,
            "payload": payload_b64[:100]  # Send first 100 chars for logging
        })
        
        logger.debug(f"Received audio chunk {chunk_number}")
    
    async def _handle_stop(self, message: Dict[str, Any]):
        """Handle stop event."""
        stop_data = message.get("stop", {})
        reason = stop_data.get("reason", "Stream ended")
        
        logger.info(f"Stream stopped: {reason}")
        
        # Notify Flask app
        socketio.emit('call_ended', {
            "call_id": self.call_id,
            "reason": reason,
            "message": message
        })
    
    async def _handle_dtmf(self, message: Dict[str, Any]):
        """Handle DTMF event."""
        dtmf_data = message.get("dtmf", {})
        digit = dtmf_data.get("digit")
        
        logger.info(f"DTMF received: {digit}")
        
        # Notify Flask app
        socketio.emit('dtmf_received', {
            "call_id": self.call_id,
            "digit": digit
        })
    
    async def _handle_mark(self, message: Dict[str, Any]):
        """Handle mark event."""
        mark_data = message.get("mark", {})
        mark_name = mark_data.get("name")
        
        logger.info(f"Mark received: {mark_name}")
        
        # Update stats
        if self.call_id in call_stats:
            call_stats[self.call_id]["marks_received"] += 1
        
        # Notify Flask app
        socketio.emit('mark_received', {
            "call_id": self.call_id,
            "mark_name": mark_name
        })
    
    async def _handle_clear(self, message: Dict[str, Any]):
        """Handle clear event."""
        logger.info("Clear event received")
        
        # Notify Flask app
        socketio.emit('buffer_cleared', {
            "call_id": self.call_id
        })
    
    async def send_start_event(self):
        """Send start event to smartflo-ai-caller."""
        start_event = {
            "event": "start",
            "sequenceNumber": str(self.sequence_number),
            "start": {
                "streamSid": f"MZ{uuid.uuid4().hex[:30]}",
                "accountSid": f"AC{uuid.uuid4().hex[:30]}",
                "callSid": self.call_id,
                "from": active_calls[self.call_id]["caller"],
                "to": active_calls[self.call_id]["callee"],
                "mediaFormat": {
                    "encoding": "audio/x-mulaw",
                    "sampleRate": 8000,
                    "bitRate": 64,
                    "bitDepth": 8
                },
                "customParameters": {
                    "TestCall": "true",
                    "Timestamp": datetime.now().isoformat()
                }
            },
            "streamSid": f"MZ{uuid.uuid4().hex[:30]}"
        }
        
        self.sequence_number += 1
        await self._send_message(start_event)
    
    async def send_media_event(self, audio_data: bytes):
        """Send media event to smartflo-ai-caller."""
        # Convert to μ-law and base64 encode
        mulaw_data = RealAudioProcessor.pcm_to_mulaw(audio_data)
        payload = base64.b64encode(mulaw_data).decode('utf-8')
        
        media_event = {
            "event": "media",
            "sequenceNumber": str(self.sequence_number),
            "media": {
                "chunk": str(self.chunk_number),
                "timestamp": str(self.chunk_number * 20),  # 20ms per chunk
                "payload": payload
            },
            "streamSid": self.stream_id or f"MZ{uuid.uuid4().hex[:30]}"
        }
        
        self.sequence_number += 1
        self.chunk_number += 1
        
        # Update stats
        if self.call_id in call_stats:
            call_stats[self.call_id]["audio_chunks_sent"] += 1
        
        await self._send_message(media_event)
    
    async def send_mark_event(self, mark_name: str):
        """Send mark event to smartflo-ai-caller."""
        mark_event = {
            "event": "mark",
            "sequenceNumber": str(self.sequence_number),
            "streamSid": self.stream_id or f"MZ{uuid.uuid4().hex[:30]}",
            "mark": {
                "name": mark_name
            }
        }
        
        self.sequence_number += 1
        
        # Update stats
        if self.call_id in call_stats:
            call_stats[self.call_id]["marks_sent"] += 1
        
        await self._send_message(mark_event)
    
    async def send_stop_event(self, reason: str = "Call ended"):
        """Send stop event to smartflo-ai-caller."""
        stop_event = {
            "event": "stop",
            "sequenceNumber": str(self.sequence_number),
            "stop": {
                "accountSid": f"AC{uuid.uuid4().hex[:30]}",
                "callSid": self.call_id,
                "reason": reason
            },
            "streamSid": self.stream_id or f"MZ{uuid.uuid4().hex[:30]}"
        }
        
        self.sequence_number += 1
        await self._send_message(stop_event)
    
    async def _send_message(self, message: Dict[str, Any]):
        """Send message to smartflo-ai-caller."""
        if not self.connected or not self.websocket:
            logger.error("Not connected to smartflo-ai-caller")
            return
        
        try:
            await self.websocket.send(json.dumps(message))
            logger.debug(f"Sent to smartflo-ai-caller: {message['event']}")
        except Exception as e:
            logger.error(f"Error sending message: {e}")
    
    async def disconnect(self):
        """Disconnect from smartflo-ai-caller with timeout handling."""
        if self.websocket:
            try:
                await asyncio.wait_for(self.websocket.close(), timeout=3.0)
            except asyncio.TimeoutError:
                logger.warning("SmartfloAICallerClient disconnect timed out")
            except Exception as e:
                logger.error(f"Error disconnecting SmartfloAICallerClient: {e}")
            finally:
                self.connected = False


class AIVoiceMateClient:
    """Client for connecting to AI Voice Mate."""
    
    def __init__(self, call_id: str):
        self.call_id = call_id
        self.ws_url = AI_VOICE_MATE_WS_URL
        self.websocket = None
        self.connected = False
        self.session_id = f"test_session_{call_id}"
    
    async def connect(self):
        """Connect to AI Voice Mate with keep-alive."""
        try:
            logger.info(f"Attempting to connect to AI Voice Mate at {self.ws_url} for call {self.call_id}")
            
            # Connect with aggressive keep-alive settings to prevent premature closure
            self.websocket = await websockets.connect(
                self.ws_url,
                ping_interval=10,   # Send ping every 10 seconds (more frequent)
                ping_timeout=15,    # Wait 15 seconds for pong (more patient)
                close_timeout=60,   # Wait 60 seconds for close (much longer)
                max_size=10_000_000,  # Allow large messages (10MB for audio)
                max_queue=100,      # Allow more queued messages
                compression=None    # Disable compression for better performance
            )
            self.connected = True
            logger.info(f"Successfully connected to AI Voice Mate with keep-alive: {self.ws_url}")
            
            # Register user
            logger.info(f"Registering user with AI Voice Mate for call {self.call_id}")
            await self.register_user()
            
            # Start listening
            logger.info(f"Starting message listener task for call {self.call_id}")
            asyncio.create_task(self._listen_for_messages())
            
            # Start keep-alive task
            logger.info(f"Starting keep-alive task for call {self.call_id}")
            asyncio.create_task(self._keep_connection_alive())
            
            # Give a moment for the connection to stabilize
            await asyncio.sleep(0.2)
            
        except Exception as e:
            logger.error(f"Failed to connect to AI Voice Mate: {e}")
            self.connected = False
            raise
    
    async def register_user(self):
        """Register user with AI Voice Mate."""
        call_data = active_calls.get(self.call_id, {})
        
        register_message = {
            "type": "store_user",
            "session": self.session_id,
            "data": {
                "name": "Test User",
                "mobile": call_data.get("caller", "+1234567890"),
                "userId": f"test_user_{self.call_id}",
                "sentences": [],
                "sessionType": "call",
                "target": "english_tutor"
            }
        }
        
        logger.info(f"Sending store_user message to AI Voice Mate for call {self.call_id}")
        await self._send_message(register_message)
        logger.info(f"store_user message sent successfully for call {self.call_id}")
    
    async def start_ai_call(self):
        """Start AI call with timeout handling."""
        try:
            start_message = {
                "type": "start_ai_call",
                "session": self.session_id,
                "data": None
            }
            
            logger.info(f"Sending start_ai_call message for call {self.call_id}")
            await asyncio.wait_for(self._send_message(start_message), timeout=5.0)
            logger.info(f"start_ai_call message sent successfully for call {self.call_id}")
            
            # Enable AI listening with explicit audio return configuration
            listen_message = {
                "type": "ai_start_listening",
                "session": self.session_id,
                "data": {
                    "return_audio": True,
                    "audio_format": "base64",
                    "realtime": True,
                    "stream_audio": True
                }
            }
            
            logger.info(f"Sending ai_start_listening message for call {self.call_id}")
            await asyncio.wait_for(self._send_message(listen_message), timeout=5.0)
            logger.info(f"ai_start_listening message sent successfully for call {self.call_id}")
            
            # 🎯 CRITICAL: Send dummy audio to trigger AI response!
            await asyncio.sleep(1.0)  # Wait a moment for AI to process
            
            logger.error(f"🎤 SENDING DUMMY AUDIO to trigger AI Voice Mate response for call {self.call_id}")
            dummy_audio = [0] * 160  # 160 bytes of silence (20ms at 8kHz) 
            dummy_audio_message = {
                "type": "audio_chunk", 
                "session": self.session_id,
                "data": dummy_audio
            }
            
            await asyncio.wait_for(self._send_message(dummy_audio_message), timeout=5.0)
            logger.error(f"🎤 DUMMY AUDIO SENT - AI should now respond with greeting audio!")
            
        except asyncio.TimeoutError:
            logger.error("AI Voice Mate start_ai_call timed out after 5 seconds")
            raise
        except Exception as e:
            logger.error(f"Error starting AI call: {e}")
            raise
    
    async def send_audio_chunk(self, audio_data: bytes):
        """Send audio chunk to AI Voice Mate with timeout handling."""
        try:
            # Convert to list of bytes for JSON serialization
            audio_bytes = list(audio_data)
            
            audio_message = {
                "type": "audio_chunk",
                "session": self.session_id,
                "data": audio_bytes
            }
            
            # Send with timeout to prevent blocking
            await asyncio.wait_for(self._send_message(audio_message), timeout=1.0)
            
        except asyncio.TimeoutError:
            logger.warning("AI Voice Mate audio send timed out, skipping chunk")
        except Exception as e:
            logger.error(f"Error sending audio to AI Voice Mate: {e}")
    
    async def _listen_for_messages(self):
        """Listen for messages from AI Voice Mate with ultra-comprehensive monitoring."""
        logger.info(f"🎧 STARTING ULTRA-COMPREHENSIVE MESSAGE LISTENING for call {self.call_id}")
        message_count = 0
        audio_messages_found = 0
        
        try:
            async for message in self.websocket:
                message_count += 1
                logger.warning(f"📨 MESSAGE #{message_count} RECEIVED for call {self.call_id}")
                logger.warning(f"📨 RAW MESSAGE LENGTH: {len(message)} characters")
                logger.warning(f"📨 RAW MESSAGE PREVIEW (first 200 chars): {message[:200]}...")
                
                # ULTRA-DETAILED LOGGING - Log full message if it's potentially audio
                if len(message) > 500:  # Large message might be audio
                    audio_messages_found += 1
                    logger.warning(f"🎵 POTENTIAL AUDIO MESSAGE #{audio_messages_found} DETECTED!")
                    logger.warning(f"🎵 FULL LARGE MESSAGE: {message}")
                
                try:
                    data = json.loads(message)
                    logger.warning(f"📨 PARSED JSON MESSAGE TYPE: {data.get('type', 'NO_TYPE')}")
                    
                    # Log all fields in the message
                    for key, value in data.items():
                        if isinstance(value, str) and len(value) > 100:
                            logger.warning(f"🔍 LARGE FIELD '{key}': {len(value)} chars - MIGHT BE AUDIO!")
                        logger.debug(f"📨 Field '{key}': {type(value).__name__} = {str(value)[:100]}...")
                    
                    await self._handle_message(data)
                    
                except json.JSONDecodeError as e:
                    logger.error(f"❌ JSON PARSE FAILED for message #{message_count}: {e}")
                    logger.error(f"❌ RAW UNPARSEABLE MESSAGE: {message}")
                    
                    # Try to handle as raw audio data
                    if len(message) > 100:
                        logger.warning(f"🎵 TREATING UNPARSEABLE LARGE MESSAGE AS POTENTIAL AUDIO!")
                        socketio.emit('ai_audio_response', {
                            "call_id": self.call_id,
                            "audio_data": message
                        })
                        logger.warning(f"🎵 EMITTED UNPARSEABLE MESSAGE AS AUDIO!")
                
        except websockets.exceptions.ConnectionClosed as e:
            logger.error(f"🔌 AI VOICE MATE CONNECTION CLOSED for call {self.call_id}: {e}")
            logger.error(f"📊 TOTAL MESSAGES RECEIVED: {message_count}")
            logger.error(f"📊 POTENTIAL AUDIO MESSAGES: {audio_messages_found}")
            logger.error(f"⚠️ CONNECTION CLOSED BEFORE AI COULD SEND AUDIO!")
            self.connected = False
        except Exception as e:
            logger.error(f"💥 CRITICAL ERROR in message listening: {e}")
            logger.error(f"📊 Messages received before error: {message_count}")
            self.connected = False
            
        logger.error(f"🔚 AI VOICE MATE MESSAGE LISTENING ENDED for call {self.call_id}")
    
    async def _keep_connection_alive(self):
        """AGGRESSIVELY keep WebSocket connection alive to prevent premature closure."""
        try:
            logger.warning(f"🔥 STARTING AGGRESSIVE CONNECTION KEEP-ALIVE for call {self.call_id}")
            
            # First, wait longer for AI to process - this is critical!
            initial_wait = 60  # Wait 60 seconds for AI processing
            logger.warning(f"⏰ INITIAL WAIT: Giving AI Voice Mate {initial_wait}s to process and send audio...")
            
            ping_count = 0
            while self.connected and self.call_id in active_calls and ping_count < 20:  # Max 20 pings = 5 minutes
                await asyncio.sleep(5)  # Ping every 5 seconds - very frequent!
                ping_count += 1
                
                if not self.connected:
                    logger.error(f"💔 CONNECTION LOST during keep-alive #{ping_count}")
                    break
                    
                try:
                    # Send multiple types of keep-alive messages
                    keep_alive_messages = [
                        {"type": "ping", "session": self.session_id, "data": "ping"},
                        {"type": "heartbeat", "session": self.session_id, "data": "alive"},
                        {"type": "status", "session": self.session_id, "data": "connected"}
                    ]
                    
                    for msg in keep_alive_messages:
                        logger.warning(f"💓 KEEP-ALIVE PING #{ping_count} ({msg['type']}) for call {self.call_id}")
                        await self.websocket.send(json.dumps(msg))
                        
                    # Also check if websocket is still open
                    if self.websocket.closed:
                        logger.error(f"🚨 WEBSOCKET CLOSED DETECTED during ping #{ping_count}!")
                        self.connected = False
                        break
                        
                except Exception as e:
                    logger.error(f"💥 KEEP-ALIVE PING #{ping_count} FAILED for call {self.call_id}: {e}")
                    break
                    
            logger.warning(f"🔚 CONNECTION KEEP-ALIVE ENDED for call {self.call_id} after {ping_count} pings")
            
        except Exception as e:
            logger.error(f"💥 CRITICAL ERROR in connection keep-alive: {e}")
    
    async def _handle_message(self, message: Dict[str, Any]):
        """Handle message from AI Voice Mate."""
        msg_type = message.get("type")
        
        # COMPREHENSIVE DEBUG LOGGING
        logger.info(f"AI Voice Mate message received - Type: {msg_type}")
        logger.debug(f"Full AI Voice Mate message: {json.dumps(message, indent=2)}")
        
        if msg_type == "ai_response":
            # Text response from AI
            response_text = message.get("data", "")
            logger.info(f"AI response: {response_text}")
            
            socketio.emit('ai_response', {
                "call_id": self.call_id,
                "text": response_text
            })
            
        elif msg_type == "llm_answer":
            # Audio response from AI
            audio_b64 = message.get("data", "")
            logger.info(f"Received AI audio response: {len(audio_b64)} chars")
            
            # Debug logging
            if not audio_b64:
                logger.error("AI audio response is empty!")
            else:
                logger.info(f"Emitting ai_audio_response to frontend for call {self.call_id}")
            
            socketio.emit('ai_audio_response', {
                "call_id": self.call_id,
                "audio_data": audio_b64
            })
            
        elif msg_type == "speech_text":
            # Transcribed text
            transcribed_text = message.get("data", "")
            logger.info(f"Transcribed: {transcribed_text}")
            
            socketio.emit('speech_transcribed', {
                "call_id": self.call_id,
                "text": transcribed_text
            })
            
        elif msg_type == "ping" or msg_type == "pong":
            # Handle keep-alive ping/pong messages
            logger.debug(f"Received keep-alive {msg_type} from AI Voice Mate")
            
        else:
            # 🚨 ULTRA-CRITICAL CATCH-ALL FOR UNKNOWN MESSAGE TYPES!
            logger.error(f"🚨 UNKNOWN AI VOICE MATE MESSAGE TYPE: {msg_type}")
            logger.error(f"🚨 FULL UNKNOWN MESSAGE: {json.dumps(message, indent=2)}")
            
            # Check ALL possible audio-related keywords
            message_str = str(message).lower()
            audio_keywords = ['audio', 'sound', 'llm', 'voice', 'speech', 'wav', 'mp3', 'media', 'generated', 'tts']
            audio_found = any(keyword in message_str for keyword in audio_keywords)
            
            if audio_found:
                logger.error(f"🎵 AUDIO KEYWORDS DETECTED in unknown message!")
                
            # Check EVERY field for potential audio data
            potential_audio_found = False
            for key, value in message.items():
                if isinstance(value, str):
                    logger.warning(f"🔍 FIELD '{key}': {len(value)} chars, type: {type(value).__name__}")
                    
                    if len(value) > 50:  # Lowered threshold to catch more potential audio
                        logger.error(f"🎵 LARGE DATA FIELD FOUND: '{key}' = {len(value)} chars")
                        logger.error(f"🎵 FIELD PREVIEW: {value[:200]}...")
                        
                        # Try to detect base64 or any large data
                        import re
                        is_base64_like = re.match(r'^[A-Za-z0-9+/]*={0,2}$', value[:100])
                        
                        logger.error(f"🎵 TREATING FIELD '{key}' AS POTENTIAL AUDIO!")
                        socketio.emit('ai_audio_response', {
                            "call_id": self.call_id,
                            "audio_data": value
                        })
                        logger.error(f"🎵 EMITTED FIELD '{key}' AS AUDIO TO FRONTEND!")
                        potential_audio_found = True
            
            if not potential_audio_found:
                logger.error(f"⚠️ NO AUDIO DATA FOUND in unknown message - this might be the problem!")
                
                # As a last resort, try to emit the entire message as audio
                full_message_str = json.dumps(message)
                if len(full_message_str) > 100:
                    logger.error(f"🚨 LAST RESORT: Emitting entire message as audio!")
                    socketio.emit('ai_audio_response', {
                        "call_id": self.call_id,
                        "audio_data": full_message_str
                    })
                    logger.error(f"🚨 EMITTED ENTIRE MESSAGE AS AUDIO!")
    
    async def _send_message(self, message: Dict[str, Any]):
        """Send message to AI Voice Mate."""
        message_type = message.get('type', 'unknown')
        
        if not self.connected or not self.websocket:
            logger.error(f"Cannot send {message_type} - not connected to AI Voice Mate")
            return
        
        try:
            message_json = json.dumps(message)
            logger.debug(f"Sending to AI Voice Mate: {message_type} ({len(message_json)} chars)")
            await self.websocket.send(message_json)
            logger.info(f"Successfully sent {message_type} to AI Voice Mate for call {self.call_id}")
        except Exception as e:
            logger.error(f"Error sending {message_type} to AI Voice Mate: {e}")
            raise
    
    async def disconnect(self):
        """Disconnect from AI Voice Mate with timeout handling."""
        logger.info(f"Disconnecting AI Voice Mate client for call {self.call_id}")
        self.connected = False  # Set this first to stop keep-alive
        
        if self.websocket:
            try:
                # Send end call with timeout
                end_message = {
                    "type": "end_ai_call",
                    "session": self.session_id,
                    "data": None
                }
                logger.info(f"Sending end_ai_call message for call {self.call_id}")
                await asyncio.wait_for(self._send_message(end_message), timeout=2.0)
                
                # Close connection with longer timeout to prevent premature closure
                logger.info(f"Closing WebSocket connection for call {self.call_id}")
                await asyncio.wait_for(self.websocket.close(), timeout=5.0)
                logger.info(f"AI Voice Mate connection closed successfully for call {self.call_id}")
                
            except asyncio.TimeoutError:
                logger.warning(f"AI Voice Mate disconnect timed out for call {self.call_id}")
            except Exception as e:
                logger.error(f"Error disconnecting AI Voice Mate for call {self.call_id}: {e}")
            finally:
                self.websocket = None
                logger.info(f"AI Voice Mate disconnect completed for call {self.call_id}")


# HTML Template (same as before but with enhanced features)
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Voice Call Test - Bi-Directional Audio Streaming</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.6.2/socket.io.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .integration-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .status-card.connected {
            border-left: 4px solid #28a745;
        }
        
        .status-card.disconnected {
            border-left: 4px solid #dc3545;
        }
        
        .status-card.connecting {
            border-left: 4px solid #ffc107;
        }
        
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .call-controls {
            grid-column: 1 / -1;
            text-align: center;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .btn.danger:hover {
            background: #c82333;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.success:hover {
            background: #218838;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.calling {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .audio-controls {
            margin: 20px 0;
        }
        
        .audio-visualizer {
            width: 100%;
            height: 100px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            margin: 10px 0;
            position: relative;
        }
        
        #inputCanvas, #outputCanvas {
            width: 100%;
            height: 100%;
        }
        
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        
        .stats-table th, .stats-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .stats-table th {
            background: #f2f2f2;
        }
        
        .log-container {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #f9f9f9;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px;
        }
        
        .log-outbound {
            color: #0066cc;
        }
        
        .log-inbound {
            color: #cc6600;
        }
        
        .log-error {
            color: #cc0000;
            font-weight: bold;
        }
        
        .log-ai {
            color: #9900cc;
            font-weight: bold;
        }
        
        .form-group {
            margin: 10px 0;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
        }
        
        .metric-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .ai-responses {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #f0f8ff;
            margin: 10px 0;
        }
        
        .ai-response {
            margin: 5px 0;
            padding: 5px;
            background: white;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎙️ Enhanced Voice Call Test Application</h1>
        <p>Real Integration with smartflo-ai-caller & AI Voice Mate</p>
    </div>

    <div class="integration-status">
        <div id="smartfloStatus" class="status-card disconnected">
            <h4>📡 Smartflo AI Caller</h4>
            <div>Status: <span id="smartfloStatusText">Disconnected</span></div>
            <div>URL: ws://localhost:8000/vendor-audio-stream</div>
        </div>
        <div id="aiVoiceMateStatus" class="status-card disconnected">
            <h4>🤖 AI Voice Mate</h4>
            <div>Status: <span id="aiStatusText">Disconnected</span></div>
            <div>URL: ws://localhost:5010</div>
        </div>
        <div id="callStatus" class="status-card disconnected">
            <h4>📞 Call Status</h4>
            <div>Status: <span id="callStatusText">Disconnected</span></div>
            <div>Duration: <span id="callDuration">0s</span></div>
        </div>
    </div>

    <div class="container">
        <div class="panel call-controls">
            <h3>Call Controls</h3>
            <div class="form-group">
                <label for="callerNumber">From Number:</label>
                <input type="text" id="callerNumber" value="+1234567890" placeholder="Caller number">
            </div>
            <div class="form-group">
                <label for="calleeNumber">To Number:</label>
                <input type="text" id="calleeNumber" value="+0987654321" placeholder="Callee number">
            </div>
            
            <div class="audio-controls">
                <button id="startCallBtn" class="btn success">📞 Start Call</button>
                <button id="endCallBtn" class="btn danger" disabled>📞 End Call</button>
                <button id="startRecordingBtn" class="btn" disabled>🎤 Start Recording</button>
                <button id="stopRecordingBtn" class="btn" disabled>⏹️ Stop Recording</button>
                <button id="sendMarkBtn" class="btn" disabled>🏷️ Send Mark</button>
                <button id="clearBufferBtn" class="btn" disabled>🗑️ Clear Buffer</button>
                <button id="testAIBtn" class="btn" disabled>🤖 Test AI Response</button>
            </div>
        </div>

        <div class="panel">
            <h3>📊 Audio Input (Microphone)</h3>
            <div class="audio-visualizer">
                <canvas id="inputCanvas"></canvas>
            </div>
            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-value" id="inputLevel">0</div>
                    <div class="metric-label">Input Level</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="chunksSent">0</div>
                    <div class="metric-label">Chunks Sent</div>
                </div>
            </div>
        </div>

        <div class="panel">
            <h3>🔊 Audio Output (Speaker)</h3>
            <div class="audio-visualizer">
                <canvas id="outputCanvas"></canvas>
            </div>
            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-value" id="outputLevel">0</div>
                    <div class="metric-label">Output Level</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="chunksReceived">0</div>
                    <div class="metric-label">Chunks Received</div>
                </div>
            </div>
        </div>

        <div class="panel">
            <h3>📈 Call Statistics</h3>
            <table class="stats-table">
                <tr><th>Metric</th><th>Value</th></tr>
                <tr><td>Stream ID</td><td id="streamId">-</td></tr>
                <tr><td>Sequence Number</td><td id="sequenceNumber">0</td></tr>
                <tr><td>Audio Latency</td><td id="audioLatency">0ms</td></tr>
                <tr><td>Marks Sent</td><td id="marksSent">0</td></tr>
                <tr><td>Marks Received</td><td id="marksReceived">0</td></tr>
                <tr><td>DTMF Digits</td><td id="dtmfReceived">0</td></tr>
            </table>
        </div>

        <div class="panel">
            <h3>🤖 AI Voice Mate Responses</h3>
            <div class="ai-responses" id="aiResponses">
                <div>Waiting for AI responses...</div>
            </div>
            <div>
                <strong>Last Transcription:</strong>
                <div id="lastTranscription">None</div>
            </div>
        </div>

        <div class="panel">
            <h3>📋 Event Log</h3>
            <div class="log-container" id="eventLog">
                <div class="log-entry">Ready to start enhanced voice call test...</div>
            </div>
            <button id="clearLogBtn" class="btn">Clear Log</button>
        </div>
    </div>

    <script>
        // WebSocket connection
        const socket = io();
        
        // Global state
        let callActive = false;
        let recording = false;
        let callStartTime = null;
        let audioContext = null;
        let mediaStream = null;
        let audioProcessor = null;
        let currentCallId = null;
        let chunkCounter = 0;
        let markCounter = 0;
        let dtmfCounter = 0;
        
        // Audio visualization
        let inputAnalyser = null;
        let outputAnalyser = null;
        let inputCanvas = null;
        let outputCanvas = null;
        let inputCtx = null;
        let outputCtx = null;
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeAudio();
            setupEventListeners();
            setupVisualization();
            checkSystemStatus();
        });
        
        function checkSystemStatus() {
            // Check if backend services are running
            fetch('/api/system_status')
                .then(response => response.json())
                .then(data => {
                    updateSystemStatus('smartflo', data.smartflo_ai_caller);
                    updateSystemStatus('ai', data.ai_voice_mate);
                })
                .catch(error => {
                    logEvent('Failed to check system status: ' + error.message, 'error');
                });
        }
        
        function updateSystemStatus(service, status) {
            const statusElement = document.getElementById(service === 'smartflo' ? 'smartfloStatus' : 'aiVoiceMateStatus');
            const textElement = document.getElementById(service === 'smartflo' ? 'smartfloStatusText' : 'aiStatusText');
            
            statusElement.className = 'status-card ' + (status ? 'connected' : 'disconnected');
            textElement.textContent = status ? 'Connected' : 'Disconnected';
        }
        
        function initializeAudio() {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                logEvent('Audio context initialized', 'system');
            } catch (e) {
                logEvent('Failed to initialize audio context: ' + e.message, 'error');
            }
        }
        
        function setupVisualization() {
            inputCanvas = document.getElementById('inputCanvas');
            outputCanvas = document.getElementById('outputCanvas');
            inputCtx = inputCanvas.getContext('2d');
            outputCtx = outputCanvas.getContext('2d');
            
            inputCanvas.width = inputCanvas.offsetWidth;
            inputCanvas.height = inputCanvas.offsetHeight;
            outputCanvas.width = outputCanvas.offsetWidth;
            outputCanvas.height = outputCanvas.offsetHeight;
            
            visualizeAudio();
        }
        
        function setupEventListeners() {
            document.getElementById('startCallBtn').onclick = startCall;
            document.getElementById('endCallBtn').onclick = endCall;
            document.getElementById('startRecordingBtn').onclick = startRecording;
            document.getElementById('stopRecordingBtn').onclick = stopRecording;
            document.getElementById('sendMarkBtn').onclick = sendMark;
            document.getElementById('clearBufferBtn').onclick = clearBuffer;
            document.getElementById('testAIBtn').onclick = testAI;
            document.getElementById('clearLogBtn').onclick = clearLog;
            
            // Socket event listeners
            socket.on('connect', function() {
                console.log('Socket.IO connected successfully');
                logEvent('Socket.IO connected', 'system');
            });
            
            socket.on('disconnect', function() {
                console.log('Socket.IO disconnected');
                logEvent('Socket.IO disconnected', 'system');
            });
            
            socket.on('call_started', handleCallStarted);
            socket.on('call_ended', handleCallEnded);
            socket.on('audio_received', handleAudioReceived);
            socket.on('mark_received', handleMarkReceived);
            socket.on('dtmf_received', handleDTMFReceived);
            socket.on('ai_response', handleAIResponse);
            socket.on('ai_audio_response', function(data) {
                console.log('Received ai_audio_response event:', data);
                handleAIAudioResponse(data);
            });
            socket.on('speech_transcribed', handleSpeechTranscribed);
            socket.on('event_log', handleEventLog);
            socket.on('call_stats', handleCallStats);
            socket.on('buffer_cleared', handleBufferCleared);
        }
        
        async function startCall() {
            const caller = document.getElementById('callerNumber').value;
            const callee = document.getElementById('calleeNumber').value;
            
            if (!caller || !callee) {
                alert('Please enter both caller and callee numbers');
                return;
            }
            
            try {
                updateCallStatus('Connecting...', 'connecting');
                
                // Request microphone access
                mediaStream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: 8000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    } 
                });
                
                const response = await fetch('/api/start_enhanced_call', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ caller, callee })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    currentCallId = result.call_id;
                    callActive = true;
                    callStartTime = Date.now();
                    
                    updateCallStatus('Connected - Call ID: ' + currentCallId, 'connected');
                    updateButtons();
                    
                    setupAudioProcessing();
                    
                    logEvent('Enhanced call started successfully', 'outbound');
                } else {
                    throw new Error(result.error);
                }
                
            } catch (error) {
                logEvent('Failed to start call: ' + error.message, 'error');
                updateCallStatus('Connection Failed', 'disconnected');
                alert('Failed to start call: ' + error.message);
            }
        }
        
        async function endCall() {
            if (!currentCallId) return;
            
            try {
                const response = await fetch('/api/end_enhanced_call', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ call_id: currentCallId })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    callActive = false;
                    recording = false;
                    currentCallId = null;
                    chunkCounter = 0;
                    markCounter = 0;
                    dtmfCounter = 0;
                    
                    if (mediaStream) {
                        mediaStream.getTracks().forEach(track => track.stop());
                        mediaStream = null;
                    }
                    
                    updateCallStatus('Disconnected', 'disconnected');
                    updateButtons();
                    
                    logEvent('Enhanced call ended successfully', 'outbound');
                } else {
                    throw new Error(result.error);
                }
                
            } catch (error) {
                logEvent('Failed to end call: ' + error.message, 'error');
            }
        }
        
        function setupAudioProcessing() {
            if (!audioContext || !mediaStream) return;
            
            try {
                const source = audioContext.createMediaStreamSource(mediaStream);
                inputAnalyser = audioContext.createAnalyser();
                inputAnalyser.fftSize = 256;
                
                source.connect(inputAnalyser);
                
                audioProcessor = audioContext.createScriptProcessor(1024, 1, 1);
                source.connect(audioProcessor);
                audioProcessor.connect(audioContext.destination);
                
                audioProcessor.onaudioprocess = function(event) {
                    if (recording && callActive) {
                        const inputBuffer = event.inputBuffer;
                        const inputData = inputBuffer.getChannelData(0);
                        
                        const pcmData = float32ToPCM16(inputData);
                        sendAudioChunk(pcmData);
                    }
                };
                
                logEvent('Enhanced audio processing setup complete', 'system');
                
            } catch (error) {
                logEvent('Audio processing setup failed: ' + error.message, 'error');
            }
        }
        
        function startRecording() {
            if (!callActive) {
                alert('Please start a call first');
                return;
            }
            
            recording = true;
            updateButtons();
            logEvent('Recording started', 'outbound');
        }
        
        function stopRecording() {
            recording = false;
            updateButtons();
            logEvent('Recording stopped', 'outbound');
        }
        
        function sendAudioChunk(pcmData) {
            if (!currentCallId || !recording) return;
            
            chunkCounter++;
            
            socket.emit('send_enhanced_audio', {
                call_id: currentCallId,
                audio_data: Array.from(new Uint8Array(pcmData.buffer)),
                chunk_number: chunkCounter
            });
            
            document.getElementById('chunksSent').textContent = chunkCounter;
            
            const level = calculateAudioLevel(pcmData);
            document.getElementById('inputLevel').textContent = level.toFixed(1);
        }
        
        function sendMark() {
            if (!currentCallId) return;
            
            markCounter++;
            const markName = `enhanced_mark_${markCounter}`;
            
            socket.emit('send_enhanced_mark', {
                call_id: currentCallId,
                mark_name: markName
            });
            
            logEvent(`Mark sent: ${markName}`, 'outbound');
            document.getElementById('marksSent').textContent = markCounter;
        }
        
        function clearBuffer() {
            if (!currentCallId) return;
            
            socket.emit('clear_enhanced_buffer', {
                call_id: currentCallId
            });
            
            logEvent('Buffer clear requested', 'outbound');
        }
        
        function testAI() {
            if (!currentCallId) return;
            
            socket.emit('test_ai_response', {
                call_id: currentCallId,
                text: "Hello, this is a test message for the AI agent."
            });
            
            logEvent('AI test message sent', 'outbound');
        }
        
        // Event handlers
        function handleCallStarted(data) {
            logEvent(`Call session started: ${data.call_id}`, 'inbound');
            document.getElementById('streamId').textContent = data.stream_id || 'Connecting...';
            
            // Only update status if we have a valid call_id
            if (data.call_id) {
                updateSystemStatus('smartflo', true);
                updateSystemStatus('ai', true);
                
                // Auto-start recording for seamless voice calling
                if (!recording && callActive) {
                    recording = true;
                    updateButtons();
                    logEvent('Auto-started recording for voice call', 'system');
                }
            }
        }
        
        function handleCallEnded(data) {
            logEvent('Call session ended: ' + data.reason, 'inbound');
            updateSystemStatus('smartflo', false);
            updateSystemStatus('ai', false);
        }
        
        function handleAudioReceived(data) {
            const receivedCount = parseInt(document.getElementById('chunksReceived').textContent) + 1;
            document.getElementById('chunksReceived').textContent = receivedCount;
            
            const outputLevel = Math.random() * 100;
            document.getElementById('outputLevel').textContent = outputLevel.toFixed(1);
            
            logEvent(`Audio chunk received: ${data.chunk_number}`, 'inbound');
        }
        
        function handleMarkReceived(data) {
            const receivedCount = parseInt(document.getElementById('marksReceived').textContent) + 1;
            document.getElementById('marksReceived').textContent = receivedCount;
            
            logEvent(`Mark received: ${data.mark_name}`, 'inbound');
        }
        
        function handleDTMFReceived(data) {
            dtmfCounter++;
            document.getElementById('dtmfReceived').textContent = dtmfCounter;
            
            logEvent(`DTMF received: ${data.digit}`, 'inbound');
        }
        
        function handleAIResponse(data) {
            const responseDiv = document.createElement('div');
            responseDiv.className = 'ai-response';
            responseDiv.innerHTML = `<strong>AI:</strong> ${data.text}`;
            
            const responsesContainer = document.getElementById('aiResponses');
            responsesContainer.appendChild(responseDiv);
            responsesContainer.scrollTop = responsesContainer.scrollHeight;
            
            logEvent(`AI response: ${data.text}`, 'ai');
        }
        
        async function handleAIAudioResponse(data) {
            try {
                logEvent('AI audio response received', 'ai');
                console.log('AI Audio Response Data:', data); // Debug log
                
                // Decode base64 audio data
                const audioBase64 = data.audio_data;
                if (!audioBase64) {
                    logEvent('No audio data in AI response', 'error');
                    console.error('AI response missing audio_data:', data);
                    return;
                }
                
                // Log audio data info for debugging
                console.log('AI Audio Base64 length:', audioBase64.length);
                logEvent(`AI audio data received: ${audioBase64.length} chars`, 'system');
                
                // Convert base64 to blob
                const audioBytes = atob(audioBase64);
                const audioArray = new Uint8Array(audioBytes.length);
                for (let i = 0; i < audioBytes.length; i++) {
                    audioArray[i] = audioBytes.charCodeAt(i);
                }
                
                // Try different audio formats since we're not sure what AI Voice Mate sends
                const audioFormats = ['audio/mpeg', 'audio/wav', 'audio/mp3', 'audio/ogg'];
                let audioBlob, audioUrl, audio;
                
                // Try MP3 first (most common for TTS)
                audioBlob = new Blob([audioArray], { type: 'audio/mpeg' });
                audioUrl = URL.createObjectURL(audioBlob);
                audio = new Audio(audioUrl);
                audio.volume = 0.8;
                
                audio.onloadeddata = function() {
                    audio.play().then(() => {
                        logEvent('AI audio playing', 'ai');
                        
                        // Update stats
                        const receivedCount = parseInt(document.getElementById('chunksReceived').textContent) + 1;
                        document.getElementById('chunksReceived').textContent = receivedCount;
                        
                    }).catch(error => {
                        logEvent('Failed to play AI audio: ' + error.message, 'error');
                    });
                };
                
                audio.onerror = function(error) {
                    logEvent('AI audio playback error with MP3 format, trying WAV...', 'error');
                    URL.revokeObjectURL(audioUrl); // Clean up
                    
                    // Try WAV format as fallback
                    try {
                        const wavBlob = new Blob([audioArray], { type: 'audio/wav' });
                        const wavUrl = URL.createObjectURL(wavBlob);
                        const wavAudio = new Audio(wavUrl);
                        wavAudio.volume = 0.8;
                        
                        wavAudio.onloadeddata = function() {
                            wavAudio.play().then(() => {
                                logEvent('AI audio playing (WAV format)', 'ai');
                                const receivedCount = parseInt(document.getElementById('chunksReceived').textContent) + 1;
                                document.getElementById('chunksReceived').textContent = receivedCount;
                            }).catch(error => {
                                logEvent('Failed to play AI audio (WAV): ' + error.message, 'error');
                            });
                        };
                        
                        wavAudio.onerror = function() {
                            logEvent('AI audio failed in both MP3 and WAV formats', 'error');
                            URL.revokeObjectURL(wavUrl);
                        };
                        
                        wavAudio.onended = function() {
                            URL.revokeObjectURL(wavUrl);
                            logEvent('AI audio playback completed (WAV)', 'ai');
                        };
                        
                    } catch (fallbackError) {
                        logEvent('Fallback audio playback failed: ' + fallbackError.message, 'error');
                    }
                };
                
                audio.onended = function() {
                    URL.revokeObjectURL(audioUrl); // Clean up
                    logEvent('AI audio playback completed', 'ai');
                };
                
            } catch (error) {
                logEvent('Error handling AI audio response: ' + error.message, 'error');
            }
        }
        
        function handleSpeechTranscribed(data) {
            document.getElementById('lastTranscription').textContent = data.text;
            logEvent(`Speech transcribed: ${data.text}`, 'ai');
        }
        
        function handleEventLog(data) {
            logEvent(data.message, data.type);
        }
        
        function handleCallStats(data) {
            if (data.sequence_number) {
                document.getElementById('sequenceNumber').textContent = data.sequence_number;
            }
            if (data.latency) {
                document.getElementById('audioLatency').textContent = data.latency + 'ms';
            }
        }
        
        function handleBufferCleared(data) {
            logEvent('Audio buffers cleared', 'inbound');
        }
        
        function updateCallStatus(message, className) {
            const statusEl = document.getElementById('callStatusText');
            const cardEl = document.getElementById('callStatus');
            statusEl.textContent = message;
            cardEl.className = 'status-card ' + className;
        }
        
        function updateButtons() {
            document.getElementById('startCallBtn').disabled = callActive;
            document.getElementById('endCallBtn').disabled = !callActive;
            document.getElementById('startRecordingBtn').disabled = !callActive || recording;
            document.getElementById('stopRecordingBtn').disabled = !callActive || !recording;
            document.getElementById('sendMarkBtn').disabled = !callActive;
            document.getElementById('clearBufferBtn').disabled = !callActive;
            document.getElementById('testAIBtn').disabled = !callActive;
        }
        
        function logEvent(message, type = 'system') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            const logContainer = document.getElementById('eventLog');
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('eventLog').innerHTML = 
                '<div class="log-entry">Event log cleared...</div>';
        }
        
        function float32ToPCM16(float32Array) {
            const buffer = new ArrayBuffer(float32Array.length * 2);
            const view = new DataView(buffer);
            
            for (let i = 0; i < float32Array.length; i++) {
                const sample = Math.max(-1, Math.min(1, float32Array[i]));
                view.setInt16(i * 2, sample * 0x7FFF, true);
            }
            
            return buffer;
        }
        
        function calculateAudioLevel(pcmData) {
            const view = new DataView(pcmData);
            let sum = 0;
            
            for (let i = 0; i < view.byteLength; i += 2) {
                const sample = view.getInt16(i, true);
                sum += Math.abs(sample);
            }
            
            return (sum / (view.byteLength / 2)) / 327.67;
        }
        
        function visualizeAudio() {
            requestAnimationFrame(visualizeAudio);
            
            if (callActive && callStartTime) {
                const duration = Math.floor((Date.now() - callStartTime) / 1000);
                document.getElementById('callDuration').textContent = duration + 's';
            }
            
            if (inputAnalyser && inputCtx) {
                const bufferLength = inputAnalyser.frequencyBinCount;
                const dataArray = new Uint8Array(bufferLength);
                inputAnalyser.getByteFrequencyData(dataArray);
                
                drawAudioVisualization(inputCtx, inputCanvas, dataArray, '#007bff');
            }
            
            if (outputCtx && callActive) {
                const fakeData = new Uint8Array(128);
                for (let i = 0; i < fakeData.length; i++) {
                    fakeData[i] = Math.random() * 255 * 0.3;
                }
                drawAudioVisualization(outputCtx, outputCanvas, fakeData, '#28a745');
            }
        }
        
        function drawAudioVisualization(ctx, canvas, dataArray, color) {
            const width = canvas.width;
            const height = canvas.height;
            
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(0, 0, width, height);
            
            ctx.fillStyle = color;
            
            const barWidth = width / dataArray.length;
            let x = 0;
            
            for (let i = 0; i < dataArray.length; i++) {
                const barHeight = (dataArray[i] / 255) * height;
                ctx.fillRect(x, height - barHeight, barWidth, barHeight);
                x += barWidth;
            }
        }
    </script>
</body>
</html>
"""


@app.route('/')
def index():
    """Serve the enhanced test interface."""
    return render_template_string(HTML_TEMPLATE)


@app.route('/api/system_status')
def system_status():
    """Check if backend services are running."""
    smartflo_status = False
    ai_voice_mate_status = False
    
    try:
        # Check smartflo-ai-caller
        response = requests.get(f"{SMARTFLO_AI_CALLER_HTTP_URL}/health", timeout=5)
        smartflo_status = response.status_code == 200
    except:
        pass
    
    try:
        # Check AI Voice Mate (simple connection test)
        # This is a basic check - in production you might want a proper health endpoint
        ai_voice_mate_status = True  # Assume available for now
    except:
        pass
    
    return jsonify({
        "smartflo_ai_caller": smartflo_status,
        "ai_voice_mate": ai_voice_mate_status
    })


@app.route('/api/start_enhanced_call', methods=['POST'])
def start_enhanced_call():
    """Start enhanced voice call with real system integration."""
    try:
        data = request.get_json()
        caller = data.get('caller')
        callee = data.get('callee')
        
        if not caller or not callee:
            return jsonify({"success": False, "error": "Missing caller or callee"})
        
        # Generate unique call ID
        call_id = f"CA{uuid.uuid4().hex[:30]}"
        
        # Store call information
        active_calls[call_id] = {
            "caller": caller,
            "callee": callee,
            "start_time": datetime.now(),
            "smartflo_client": None,
            "ai_client": None,
            "events": []
        }
        
        call_stats[call_id] = {
            "audio_chunks_sent": 0,
            "audio_chunks_received": 0,
            "marks_sent": 0,
            "marks_received": 0,
            "dtmf_received": 0,
            "last_activity": datetime.now()
        }
        
        # Initialize clients in background
        def setup_clients():
            asyncio.run(setup_call_clients(call_id))
        
        thread = threading.Thread(target=setup_clients)
        thread.start()
        
        return jsonify({
            "success": True,
            "call_id": call_id,
            "message": "Enhanced call initiated - connecting to services..."
        })
        
    except Exception as e:
        logger.error(f"Error starting enhanced call: {e}")
        return jsonify({"success": False, "error": str(e)})


async def setup_call_clients(call_id: str):
    """Setup smartflo-ai-caller and AI Voice Mate clients with timeout handling."""
    try:
        # Create and connect smartflo client (faster, no timeout needed)
        smartflo_client = SmartfloAICallerClient(call_id)
        await smartflo_client.connect()
        active_calls[call_id]["smartflo_client"] = smartflo_client
        
        # Emit early notification that smartflo is connected
        socketio.emit('call_started', {
            "call_id": call_id,
            "stream_id": smartflo_client.stream_id,
            "message": "smartflo-ai-caller connected, connecting to AI Voice Mate..."
        })
        
        # Create and connect AI client with timeout
        try:
            ai_client = AIVoiceMateClient(call_id)
            await asyncio.wait_for(ai_client.connect(), timeout=10.0)
            await asyncio.wait_for(ai_client.start_ai_call(), timeout=10.0)
            active_calls[call_id]["ai_client"] = ai_client
            
            # Start connection monitoring
            asyncio.create_task(monitor_ai_connection(call_id, ai_client))
            
            # Final success notification
            socketio.emit('call_started', {
                "call_id": call_id,
                "stream_id": smartflo_client.stream_id,
                "message": "All services connected successfully"
            })
            
        except asyncio.TimeoutError:
            logger.warning(f"AI Voice Mate connection timed out for call {call_id}, proceeding without AI")
            active_calls[call_id]["ai_client"] = None
            
            socketio.emit('call_started', {
                "call_id": call_id,
                "stream_id": smartflo_client.stream_id,
                "message": "Connected to smartflo-ai-caller (AI Voice Mate unavailable)"
            })
        
        logger.info(f"Enhanced call {call_id} setup completed")
        
    except Exception as e:
        logger.error(f"Error setting up call clients: {e}")
        socketio.emit('call_setup_error', {
            "call_id": call_id,
            "error": str(e)
        })


@app.route('/api/end_enhanced_call', methods=['POST'])
def end_enhanced_call():
    """End enhanced voice call."""
    try:
        data = request.get_json()
        call_id = data.get('call_id')
        
        if not call_id or call_id not in active_calls:
            return jsonify({"success": False, "error": "Call not found"})
        
        call_data = active_calls[call_id]
        
        # Disconnect clients in background
        def cleanup_clients():
            asyncio.run(cleanup_call_clients(call_id))
        
        thread = threading.Thread(target=cleanup_clients)
        thread.start()
        
        # Clean up
        del active_calls[call_id]
        if call_id in call_stats:
            del call_stats[call_id]
        
        return jsonify({"success": True, "message": "Enhanced call ended"})
        
    except Exception as e:
        logger.error(f"Error ending enhanced call: {e}")
        return jsonify({"success": False, "error": str(e)})


async def monitor_ai_connection(call_id: str, ai_client):
    """Monitor AI Voice Mate connection and provide debugging info."""
    try:
        logger.info(f"Starting AI connection monitoring for call {call_id}")
        
        # Give AI more time to process and respond (important for audio generation)
        initial_wait = 30  # Wait 30 seconds initially for AI to send audio
        logger.info(f"Giving AI Voice Mate {initial_wait}s to process and send audio response...")
        await asyncio.sleep(initial_wait)
        
        # Then check connection periodically
        while call_id in active_calls and ai_client.connected:
            await asyncio.sleep(10)  # Check every 10 seconds after initial wait
            
            if not ai_client.connected:
                logger.warning(f"AI Voice Mate connection lost for call {call_id}")
                
                # Notify via socket
                socketio.emit('ai_connection_lost', {
                    "call_id": call_id,
                    "message": "AI Voice Mate connection lost, voice calling continues"
                })
                break
                
        logger.info(f"AI connection monitoring ended for call {call_id}")
        
    except Exception as e:
        logger.error(f"Error monitoring AI connection: {e}")


async def cleanup_call_clients(call_id: str):
    """Cleanup call clients with timeout handling."""
    try:
        call_data = active_calls.get(call_id, {})
        
        # Disconnect smartflo client with timeout
        smartflo_client = call_data.get("smartflo_client")
        if smartflo_client:
            try:
                await asyncio.wait_for(smartflo_client.send_stop_event("User ended call"), timeout=3.0)
                await asyncio.wait_for(smartflo_client.disconnect(), timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning(f"Smartflo client cleanup timed out for call {call_id}")
            except Exception as e:
                logger.error(f"Error cleaning up smartflo client: {e}")
        
        # Disconnect AI client with timeout
        ai_client = call_data.get("ai_client")
        if ai_client:
            try:
                await asyncio.wait_for(ai_client.disconnect(), timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning(f"AI client cleanup timed out for call {call_id}")
            except Exception as e:
                logger.error(f"Error cleaning up AI client: {e}")
        
        # Notify disconnection
        socketio.emit('call_ended', {
            "call_id": call_id,
            "reason": "User ended call"
        })
        
        logger.info(f"Enhanced call {call_id} cleanup completed")
        
    except Exception as e:
        logger.error(f"Error cleaning up call clients: {e}")


@app.route('/api/call_stats/<call_id>')
def get_enhanced_call_stats(call_id):
    """Get enhanced call statistics."""
    if call_id not in active_calls:
        return jsonify({"error": "Call not found"}), 404
    
    call = active_calls[call_id]
    stats = call_stats.get(call_id, {})
    
    return jsonify({
        "call_id": call_id,
        "stats": stats,
        "duration": (datetime.now() - call["start_time"]).total_seconds(),
        "smartflo_connected": call.get("smartflo_client") is not None,
        "ai_connected": call.get("ai_client") is not None,
        "events_count": len(call.get("events", []))
    })


# Enhanced Socket.IO handlers
@socketio.on('send_enhanced_audio')
def handle_send_enhanced_audio(data):
    """Handle audio data with real system integration."""
    try:
        call_id = data.get('call_id')
        audio_data = data.get('audio_data')
        chunk_number = data.get('chunk_number', 1)
        
        if call_id not in active_calls:
            emit('error', {"message": "Call not found"})
            return
        
        call_data = active_calls[call_id]
        smartflo_client = call_data.get("smartflo_client")
        ai_client = call_data.get("ai_client")
        
        # Convert audio data to bytes
        audio_bytes = bytes(audio_data)
        
        # Send to smartflo-ai-caller
        if smartflo_client and smartflo_client.connected:
            def send_to_smartflo():
                asyncio.run(smartflo_client.send_media_event(audio_bytes))
            
            threading.Thread(target=send_to_smartflo).start()
        
        # Send to AI Voice Mate
        if ai_client and ai_client.connected:
            def send_to_ai():
                asyncio.run(ai_client.send_audio_chunk(audio_bytes))
            
            threading.Thread(target=send_to_ai).start()
        
        # Update statistics
        call_stats[call_id]["audio_chunks_sent"] += 1
        call_stats[call_id]["last_activity"] = datetime.now()
        
        # Log the event
        emit('event_log', {
            "message": f"Enhanced audio chunk processed: {chunk_number} ({len(audio_bytes)} bytes)",
            "type": "inbound"
        })
        
    except Exception as e:
        emit('error', {"message": f"Enhanced audio processing error: {str(e)}"})


@socketio.on('send_enhanced_mark')
def handle_send_enhanced_mark(data):
    """Handle mark event with real system integration."""
    try:
        call_id = data.get('call_id')
        mark_name = data.get('mark_name')
        
        if call_id not in active_calls:
            emit('error', {"message": "Call not found"})
            return
        
        call_data = active_calls[call_id]
        smartflo_client = call_data.get("smartflo_client")
        
        # Send to smartflo-ai-caller
        if smartflo_client and smartflo_client.connected:
            def send_mark():
                asyncio.run(smartflo_client.send_mark_event(mark_name))
            
            threading.Thread(target=send_mark).start()
        
        # Update statistics
        call_stats[call_id]["marks_sent"] += 1
        
        # Log the event
        emit('event_log', {
            "message": f"Enhanced mark event processed: {mark_name}",
            "type": "inbound"
        })
        
    except Exception as e:
        emit('error', {"message": f"Enhanced mark processing error: {str(e)}"})


@socketio.on('test_ai_response')
def handle_test_ai_response(data):
    """Test AI response functionality."""
    try:
        call_id = data.get('call_id')
        text = data.get('text')
        
        if call_id not in active_calls:
            emit('error', {"message": "Call not found"})
            return
        
        # Simulate AI response
        emit('ai_response', {
            "call_id": call_id,
            "text": f"AI received: {text}"
        })
        
        emit('event_log', {
            "message": f"AI test response generated for: {text}",
            "type": "ai"
        })
        
    except Exception as e:
        emit('error', {"message": f"AI test error: {str(e)}"})


@socketio.on('connect')
def handle_enhanced_connect():
    """Handle enhanced client connection."""
    logger.info(f"Enhanced client connected: {request.sid}")
    emit('event_log', {
        "message": "Connected to enhanced voice call test server",
        "type": "system"
    })


@socketio.on('disconnect')
def handle_enhanced_disconnect():
    """Handle enhanced client disconnection."""
    logger.info(f"Enhanced client disconnected: {request.sid}")


if __name__ == '__main__':
    print("🎙️ Starting Enhanced Voice Call Test Application")
    print("🔗 Real Integration with smartflo-ai-caller & AI Voice Mate")
    print("🌐 Access the application at: http://localhost:5002")
    print("📋 System Requirements:")
    print("   - smartflo-ai-caller running on localhost:8000")
    print("   - AI Voice Mate running on localhost:5010")
    print("=" * 70)
    
    socketio.run(app, host='0.0.0.0', port=5002, debug=True, allow_unsafe_werkzeug=True)